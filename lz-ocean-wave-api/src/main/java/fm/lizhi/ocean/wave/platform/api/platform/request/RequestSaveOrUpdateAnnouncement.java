package fm.lizhi.ocean.wave.platform.api.platform.request;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class RequestSaveOrUpdateAnnouncement {


    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 更新时存在
     */
    private Long id;

    /**
     * 配置的业务
     */
    private List<Integer> appIds;
}
