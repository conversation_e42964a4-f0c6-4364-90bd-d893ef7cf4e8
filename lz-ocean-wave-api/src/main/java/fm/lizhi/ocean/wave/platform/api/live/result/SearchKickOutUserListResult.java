package fm.lizhi.ocean.wave.platform.api.live.result;

import fm.lizhi.ocean.wave.platform.api.live.bean.KickOutUserRecordBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class SearchKickOutUserListResult {

    /**
     * 记录列表
     */
    private List<KickOutUserRecordBean> recordList;

    /**
     * 用户分页，透传
     */
    private String performanceId;
}
