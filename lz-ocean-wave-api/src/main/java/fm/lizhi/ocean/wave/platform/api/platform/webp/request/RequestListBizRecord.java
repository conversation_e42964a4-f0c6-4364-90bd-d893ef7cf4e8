package fm.lizhi.ocean.wave.platform.api.platform.webp.request;

import fm.lizhi.ocean.wave.platform.api.platform.webp.constant.WebpConvertRequestTypeEnum;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 列出webp业务转换记录的请求
 */
@Data
public class RequestListBizRecord {

    /**
     * 分页页码
     */
    @NotNull
    @Min(1)
    private Integer pageNumber;

    /**
     * 分页大小
     */
    @NotNull
    @Min(1)
    @Max(1000)
    private Integer pageSize;

    /**
     * 应用id
     */
    private Integer appId;

    /**
     * 业务请求id, 由业务方生成并保证唯一
     */
    private String bizRequestId;

    /**
     * 业务类型, 由业务方定义, 用于分类
     */
    private String bizType;

    /**
     * 请求类型
     *
     * @see WebpConvertRequestTypeEnum
     */
    private String requestType;

    /**
     * 源文件类型
     */
    private String sourceType;

    /**
     * 源文件SHA256值
     */
    private String sourceSha;

    /**
     * 源文件路径, 斜杆开头的相对路径
     */
    private String sourcePath;

    /**
     * 转换质量选项
     */
    private Integer qualityOption;

    /**
     * 转换后的webp文件SHA256值
     */
    private String webpSha;

    /**
     * 转换后的webp文件路径, 斜杆开头的相对路径
     */
    private String webpPath;

    /**
     * 最小转换耗时秒数, 包含
     */
    private Long minCostSeconds;

    /**
     * 最大转换耗时秒数, 包含
     */
    private Long maxCostSeconds;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 错误码, 用于记录失败信息, 成功时值为0
     */
    private Integer errorCode;

    /**
     * 最小创建时间, 包含
     */
    private Long minCreateTime;

    /**
     * 最大创建时间, 包含
     */
    private Long maxCreateTime;
}
