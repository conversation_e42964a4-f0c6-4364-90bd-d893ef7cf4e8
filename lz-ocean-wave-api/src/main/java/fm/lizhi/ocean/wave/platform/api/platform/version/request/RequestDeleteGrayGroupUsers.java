package fm.lizhi.ocean.wave.platform.api.platform.version.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量删除灰度分组成员请求
 */
@Data
public class RequestDeleteGrayGroupUsers {

    /**
     * 灰度分组id
     */
    @NotNull
    private Long groupId;

    /**
     * 应用id
     */
    @NotNull
    private Integer appId;

    /**
     * 用户id列表
     */
    @NotEmpty
    private List<Long> userIds;

    /**
     * 操作人
     */
    @NotNull
    private String operator;
}
