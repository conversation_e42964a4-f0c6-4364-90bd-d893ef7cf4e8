package fm.lizhi.ocean.wave.platform.api.user.service;

import fm.lizhi.commons.service.client.pojo.Result;

/**
 * <AUTHOR>
 * @date 2023/9/21 16:37
 */
public interface UserGroupService {

    /**
     *  是否在用户组(线上)
     *
     * @param groupId
     *            用户组id
     * @param userId
     *            用户id
     * @param userIdType
     *            用户id类型 0.用户id (可以用于所有的用户组，如果是cp组，会根据用户id查询cp_id) 1.cp_id（只能用于cp组）
     * @return
     */
    Result<Boolean> isUserInGroup(long groupId, long userId, int userIdType);
}
