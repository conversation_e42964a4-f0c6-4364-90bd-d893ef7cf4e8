package fm.lizhi.pp.activity.dto;

import fm.lizhi.pp.activity.constant.UserFeedbackDisplay;
import lombok.Data;

import java.util.Date;

/**
 * 用户反馈Dto
 */
@Data
public class UserFeedbackDto {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 反馈内容
     */
    private String description;

    /**
     * 图片列表（相对路径，用,隔开）
     */
    private String imageList;
    /**
     * 是否展示 {@link UserFeedbackDisplay}
     */
    private Integer isDisplay;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
}
