package fm.lizhi.pp.screencasting.api;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.pp.screencasting.dto.*;

/**
 * 投屏服务
 */
public interface ScreenCastingService {

    /**
     * 开始投屏
     *
     * @param req
     * @return
     */
    Result<ResStartScreenCastingDto> start(ReqStartScreenCastingDto req);

    /**
     * 结束投屏
     *
     * @param req
     * @return
     */
    Result<ResFinishScreenCastingDto> finish(ReqFinishScreenCastingDto req);

    /**
     * 控制投屏
     *
     * @param req
     * @return
     */
    Result<ResControlScreenCastingDto> control(ReqControlScreenCastingDto req);

    /**
     * 获取直播间投屏信息
     *
     * @param req
     * @return
     */
    Result<ResGetScreenCastingInfoDto> getInfo(ReqGetScreenCastingInfoDto req);

    /**
     * 获取直播间投屏状态
     *
     * @param req
     * @return
     */
    Result<ResGetScreenCastingStatusDto> getStatus(ReqGetScreenCastingStatusDto req);

    /**
     * 获取直播间投屏状态
     *
     * @param req
     * @return
     */
    Result<ResGetScreenCastingStatusDto> afterReviewReject(ReqAfterReviewRejectDto req);

    /**
     * 参数异常
     */
    public static final int RCODE_START_PARAM_ERROR = 1;
    /**
     * 失败
     */
    public static final int RCODE_START_FAIL = 2;
    /**
     * 审核失败
     */
    public static final int RCODE_START_REVIEW_FAIL = 3;
    /**
     * 没有权限
     */
    public static final int RCODE_START_NO_PERMISSION = 4;

    /**
     * 参数异常
     */
    public static final int RCODE_GET_INFO_PARAM_ERROR = 1;
    /**
     * 失败
     */
    public static final int RCODE_GET_INFO_FAIL = 2;

    /**
     * 参数异常
     */
    public static final int RCODE_CONTROL_PARAM_ERROR = 1;
    /**
     * 失败
     */
    public static final int RCODE_CONTROL_FAIL = 2;
    /**
     * 没有权限
     */
    public static final int RCODE_CONTROL_NO_PERMISSION = 3;

    /**
     * 参数异常
     */
    public static final int RCODE_FINISH_PARAM_ERROR = 1;
    /**
     * 失败
     */
    public static final int RCODE_FINISH_FAIL = 2;
    /**
     * 没有权限
     */
    public static final int RCODE_FINISH_NO_PERMISSION = 3;

    /**
     * 参数异常
     */
    public static final int RCODE_GET_STATUS_PARAM_ERROR = 1;
    /**
     * 失败
     */
    public static final int RCODE_GET_STATUS_FAIL = 2;

    /**
     * 参数异常
     */
    public static final int RCODE_AFTER_REVIEW_REJECT_PARAM_ERROR = 1;
    /**
     * 失败
     */
    public static final int RCODE_AFTER_REVIEW_REJECT_FAIL = 2;
}
