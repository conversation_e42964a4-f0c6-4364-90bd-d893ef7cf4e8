package fm.lizhi.pp.screencasting.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/3 19:37
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReqStartScreenCastingDto {
    private long liveId;
    /**
     * 发起人用户id
     */
    private long userId;
    /**
     * 图片链接列表
     */
    private List<String> images;
}
