package fm.lizhi.pp.guard.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.RequestGetUserRankList;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.ResponseGetUserRankList;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.RequestGetNjRankList;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.ResponseGetNjRankList;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.RequestGetUserRankInfo;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.ResponseGetUserRankInfo;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.RequestGetNjRankInfo;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.ResponseGetNjRankInfo;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.RankInfo;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.RankGuardInfo;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.GetUserRankListParam;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.GetNjRankListParam;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.GetUserRankInfoParam;
import fm.lizhi.pp.guard.protocol.PpGuardRankProto.GetNjRankInfoParam;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface PpGuardRankService {
	
	
	/**
	 *  获取用户榜单列表
	 *
	 * @param getUserRankListParam
	 *            
	 * @return 
	 *     //if rcode == 1 非法参数<br>
	 */
	@Service(domain = 363, op = 1151, request = RequestGetUserRankList.class, response = ResponseGetUserRankList.class)
	@Return(resultType = ResponseGetUserRankList.class)
	Result<ResponseGetUserRankList> getUserRankList(@Attribute(name = "getUserRankListParam") GetUserRankListParam getUserRankListParam);
	
	
	/**
	 *  获取主播榜单列表
	 *
	 * @param getNjRankListParam
	 *            
	 * @return 
	 *     //if rcode == 1 非法参数<br>
	 *     //if rcode == 2 没有数据<br>
	 */
	@Service(domain = 363, op = 1152, request = RequestGetNjRankList.class, response = ResponseGetNjRankList.class)
	@Return(resultType = ResponseGetNjRankList.class)
	Result<ResponseGetNjRankList> getNjRankList(@Attribute(name = "getNjRankListParam") GetNjRankListParam getNjRankListParam);
	
	
	/**
	 *  获取用户榜单信息
	 *
	 * @param getUserRankInfoParam
	 *            
	 * @return 
	 *     //if rcode == 1 非法参数<br>
	 *     //if rcode == 2 没有数据<br>
	 */
	@Service(domain = 363, op = 1153, request = RequestGetUserRankInfo.class, response = ResponseGetUserRankInfo.class)
	@Return(resultType = ResponseGetUserRankInfo.class)
	Result<ResponseGetUserRankInfo> getUserRankInfo(@Attribute(name = "getUserRankInfoParam") GetUserRankInfoParam getUserRankInfoParam);
	
	
	/**
	 *  获取主播榜单信息
	 *
	 * @param getNjRankInfoParam
	 *            
	 * @return 
	 *     //if rcode == 1 非法参数<br>
	 *     //if rcode == 2 没有数据<br>
	 */
	@Service(domain = 363, op = 1154, request = RequestGetNjRankInfo.class, response = ResponseGetNjRankInfo.class)
	@Return(resultType = ResponseGetNjRankInfo.class)
	Result<ResponseGetNjRankInfo> getNjRankInfo(@Attribute(name = "getNjRankInfoParam") GetNjRankInfoParam getNjRankInfoParam);
	
	
	public static final int GET_USER_RANK_LIST_ILLEGAL_PARAMS = 1; // 非法参数

	public static final int GET_NJ_RANK_LIST_ILLEGAL_PARAMS = 1; // 非法参数
	public static final int GET_NJ_RANK_LIST_NOT_DATA = 2; // 没有数据

	public static final int GET_USER_RANK_INFO_ILLEGAL_PARAMS = 1; // 非法参数
	public static final int GET_USER_RANK_INFO_NOT_DATA = 2; // 没有数据

	public static final int GET_NJ_RANK_INFO_ILLEGAL_PARAMS = 1; // 非法参数
	public static final int GET_NJ_RANK_INFO_NOT_DATA = 2; // 没有数据


}