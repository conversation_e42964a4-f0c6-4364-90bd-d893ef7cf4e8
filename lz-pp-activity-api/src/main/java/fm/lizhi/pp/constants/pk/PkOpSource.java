package fm.lizhi.pp.constants.pk;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * pk操作请求来源
 */
@Getter
@AllArgsConstructor
public enum PkOpSource {
    /**
     * 其他
     */
    OTHER(0),
    /**
     * 关闭直播
     */
    CLOSE_LIVE(1);

    final int source;

    public static PkOpSource from(int source) {
        for (PkOpSource value : values()) {
            if (value.source == source) {
                return value;
            }
        }
        return OTHER;
    }
}
