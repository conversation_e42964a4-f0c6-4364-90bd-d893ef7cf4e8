package fm.lizhi.pp.bean.amusementinteract;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.pp.amusementinteract.protocol.AmusementInteractProto;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Data
public class AmusementInteractLivePlayWayDto {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 厅主id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long njId;

    /**
     * 厅主波段号
     */
    private String band;

    /**
     * 厅主昵称
     */
    private String userName;

    /**
     * 玩法id集合
     */
    private List<PlayWay> playWays;

    private List<Long> playWayIds;

    /**
     * 批量增加时用
     */
    private List<String> playWayNames;

    /**
     * 记录状态，1为正常状态，100为已禁用
     */
    private Integer status;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createTime;

    /**
     * 修改时间
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long modifyTime;

    @Data
    public static class PlayWay {
        @JsonSerialize(using = ToStringSerializer.class)
        private Long playWayId;
        private String playWayName;
    }

    public static List<AmusementInteractProto.AmusementInteractLivePlayWay> java2pbList(List<AmusementInteractLivePlayWayDto> dtos) {
        if (dtos == null) {
            return Collections.emptyList();
        }

        List<AmusementInteractProto.AmusementInteractLivePlayWay> pbs = new ArrayList<>();
        for (AmusementInteractLivePlayWayDto dto : dtos) {
            pbs.add(java2pb(dto));
        }

        return pbs;
    }

    public static List<AmusementInteractLivePlayWayDto> pb2javaList(List<AmusementInteractProto.AmusementInteractLivePlayWay> pbs) {
        if (pbs == null) {
            return Collections.emptyList();
        }

        List<AmusementInteractLivePlayWayDto> dtos = new ArrayList<>();
        for (AmusementInteractProto.AmusementInteractLivePlayWay pb : pbs) {
            dtos.add(pb2java(pb));
        }

        return dtos;
    }

    public static AmusementInteractProto.AmusementInteractLivePlayWay java2pb(AmusementInteractLivePlayWayDto dto) {
        if (dto == null) {
            return null;
        }

        AmusementInteractProto.AmusementInteractLivePlayWay.Builder pbBuilder
                = AmusementInteractProto.AmusementInteractLivePlayWay.newBuilder();

        if (dto.getId() != null) {
            pbBuilder.setId(dto.getId());
        }
        if (dto.getNjId() != null) {
            pbBuilder.setNjId(dto.getNjId());
        }
        if (dto.getBand() != null) {
            pbBuilder.setBand(dto.getBand());
        }
        if (dto.getUserName() != null) {
            pbBuilder.setUserName(dto.getUserName());
        }
        if (dto.getStatus() != null) {
            pbBuilder.setStatus(dto.getStatus());
        }
        if (dto.getOperator() != null) {
            pbBuilder.setOperator(dto.getOperator());
        }
        if (dto.getCreateTime() != null) {
            pbBuilder.setCreateTime(dto.getCreateTime());
        }
        if (dto.getModifyTime() != null) {
            pbBuilder.setModifyTime(dto.getModifyTime());
        }

        if (dto.getPlayWayNames() != null) {
            pbBuilder.addAllPlayWayNames(dto.getPlayWayNames());
        }

        if (dto.getPlayWayIds() != null) {
            pbBuilder.addAllPlayWayIds(dto.getPlayWayIds());
        }

        if (dto.getPlayWays() != null) {
            List<AmusementInteractProto.PlayWay> playWays = new ArrayList<>();
            for (PlayWay playWay : dto.getPlayWays()) {
                AmusementInteractProto.PlayWay playWayPb = AmusementInteractProto.PlayWay.newBuilder()
                        .setPlayWayId(playWay.getPlayWayId())
                        .setPlayWayName(playWay.getPlayWayName())
                        .build();
                playWays.add(playWayPb);
            }

            pbBuilder.addAllPlayWay(playWays);
        }

        return pbBuilder.build();
    }

    public static AmusementInteractLivePlayWayDto pb2java(AmusementInteractProto.AmusementInteractLivePlayWay pb) {
        if (pb == null) {
            return null;
        }

        AmusementInteractLivePlayWayDto dto = new AmusementInteractLivePlayWayDto();
        if (pb.hasId()) {
            dto.setId(pb.getId());
        }

        if (pb.hasNjId()) {
            dto.setNjId(pb.getNjId());
        }

        if (pb.hasBand()) {
            dto.setBand(pb.getBand());
        }

        if (pb.hasUserName()) {
            dto.setUserName(pb.getUserName());
        }

        if (pb.hasStatus()) {
            dto.setStatus(pb.getStatus());
        }

        if (pb.hasOperator()) {
            dto.setOperator(pb.getOperator());
        }

        if (pb.hasCreateTime()) {
            dto.setCreateTime(pb.getCreateTime());
        }

        if (pb.hasModifyTime()) {
            dto.setModifyTime(pb.getModifyTime());
        }

        if (!CollectionUtils.isEmpty(pb.getPlayWayIdsList())) {
            dto.setPlayWayIds(pb.getPlayWayIdsList());
        }

        if (!CollectionUtils.isEmpty(pb.getPlayWayNamesList())) {
            dto.setPlayWayNames(pb.getPlayWayNamesList());
        }

        if (!CollectionUtils.isEmpty(pb.getPlayWayList())) {
            List<PlayWay> playWays = new ArrayList<>();
            for (AmusementInteractProto.PlayWay playWayPb : pb.getPlayWayList()) {
                PlayWay playWay = new PlayWay();
                playWay.setPlayWayId(playWayPb.getPlayWayId());
                playWay.setPlayWayName(playWayPb.getPlayWayName());
                playWays.add(playWay);
            }
            dto.setPlayWays(playWays);
        }

        return dto;
    }
}
