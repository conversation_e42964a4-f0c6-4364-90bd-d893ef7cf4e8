package fm.lizhi.pp.vote.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 投票对象类型
 */
@Getter
@AllArgsConstructor
public enum VoteTargetType {
    /**
     * 主播
     */
    ANCHOR(1),
    /**
     * 阵营
     */
    CAMP(2);

    private final int type;
    private static Map<Integer, VoteTargetType> map = new HashMap<>();

    static {
        for (VoteTargetType object : VoteTargetType.values()) {
            map.put(object.getType(), object);
        }
    }

    /**
     * 根据值类型找枚举
     *
     * @param type
     * @return
     */
    public static VoteTargetType from(int type) {
        return map.get(type);
    }
}
