package fm.lizhi.app.live.bean;

import fm.lizhi.pp.security.protocol.review.PpReviewLiveBaseProto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 直播审核结果
 *
 * <AUTHOR>
 * @date 2020-03-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiveReviewResult {

    /**
     * 审核结果
     */
    private boolean success;
    /**
     * 审核状态
     */
    private PpReviewLiveBaseProto.LiveReviewStatus status;
    /**
     * 审核失败提示信息
     */
    private String failedTip;

    public static LiveReviewResult failed(String failedTip, PpReviewLiveBaseProto.LiveReviewStatus status) {
        return LiveReviewResult.builder()
                .success(false)
                .status(status)
                .failedTip(failedTip)
                .build();
    }

    public static LiveReviewResult SUCCESS = LiveReviewResult.builder().success(true).build();

}
