package fm.lizhi.app.live.bean.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @description: MULTI_MODULE_PLAYER_CARD_CACHE key
 * @author: <a href="mailto:zhang<PERSON><PERSON>@lizhi.fm"><PERSON><PERSON><PERSON><PERSON><PERSON></a>
 * @date: 2021/3/20
 * @version: v3.1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MultiModulePlayerCardCacheKey {
    private long pageId;
    private boolean isAuditVersion;
    private int gender;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MultiModulePlayerCardCacheKey that = (MultiModulePlayerCardCacheKey) o;
        return pageId == that.pageId &&
                isAuditVersion == that.isAuditVersion && gender == that.gender;
    }

    @Override
    public int hashCode() {
        return Objects.hash(pageId, isAuditVersion, gender);
    }
}
