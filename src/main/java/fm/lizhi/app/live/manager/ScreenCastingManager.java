package fm.lizhi.app.live.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.pp.screencasting.api.ScreenCastingService;
import fm.lizhi.pp.screencasting.constant.ScreenCastingStatus;
import fm.lizhi.pp.screencasting.dto.ReqGetScreenCastingStatusDto;
import fm.lizhi.pp.screencasting.dto.ResGetScreenCastingStatusDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/5 14:18
 */
@Component
@Slf4j
public class ScreenCastingManager {
    @Autowired
    private ScreenCastingService screenCastingService;

    /**
     * 获取直播间投屏状态
     *
     * @param liveId
     * @return
     */
    public int getScreenCastingStatus(long liveId) {
        Result<ResGetScreenCastingStatusDto> result = screenCastingService.getStatus(
                ReqGetScreenCastingStatusDto.builder().liveId(liveId).build());
        int rCode = result.rCode();
        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("screenCastingService.getStatus error, rCode={}, liveId={}", rCode, liveId);
            return ScreenCastingStatus.FINISHED.getStatus();
        }
        return result.target().getStatus();
    }
}
