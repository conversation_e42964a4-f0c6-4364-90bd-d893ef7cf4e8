package fm.lizhi.app.live.manager.main.manager;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.live.pp.player.api.PpPlayerAutoSendService;

@AutoBindSingleton
public class CommentServiceManager {

    @Inject
    private PpPlayerAutoSendService ppPlayerAutoSendService;

    public void sendCommentToRoomPlayer(long liveId, long uid, int comeSource, String comeJson) {
        ppPlayerAutoSendService.sendComeRoomCommentToPlayer(liveId, uid, comeSource, comeJson);
    }
}