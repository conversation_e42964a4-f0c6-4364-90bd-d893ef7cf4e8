package fm.lizhi.app.live.manager.main.manager.livematch;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.pp.match.api.PpMatchCourseService;
import fm.lizhi.pp.match.protocol.PpMatchCourseProto;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;

/**
 * <AUTHOR>
 * @date 2021/8/30
 * @description
 */
@Slf4j
@AutoBindSingleton
public class LiveMatchServiceManager {

    @Inject
    private PpMatchCourseService ppMatchCourseService;

    /**
     * 校验用户是否有直播间PK权限
     *
     * @param userId 用户ID
     * @return
     */
    public boolean checkUserLiveMatchAuthority(long liveId, long njId, long userId) {
        try {
            PpMatchCourseProto.CheckUserLiveMatchAuthorityQuery.Builder builder = PpMatchCourseProto.CheckUserLiveMatchAuthorityQuery.newBuilder();
            builder.setLiveId(liveId);
            builder.setNjId(njId);
            builder.setUserId(userId);
            Result<PpMatchCourseProto.ResponseCheckUserLiveMatchAuthority> result = ppMatchCourseService.checkUserLiveMatchAuthority(builder.build());
            if (result.rCode() == 0) {
                return result.target().getLiveMatchAuthority();
            }
        } catch (Exception e) {
        }
        return false;
    }
}
