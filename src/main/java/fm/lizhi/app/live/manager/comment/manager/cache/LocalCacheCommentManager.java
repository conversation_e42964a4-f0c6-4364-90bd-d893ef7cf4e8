package fm.lizhi.app.live.manager.comment.manager.cache;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.datacenter.comment.pp.protocol.CommentProto;

import java.util.concurrent.TimeUnit;

/**
 * Created by liuxuejin on 2016/12/26.
 */
@AutoBindSingleton
public class LocalCacheCommentManager {

    private static final CommentProto.ResponseGetTransientComment DEFAULT_COMMENT = CommentProto.ResponseGetTransientComment.newBuilder().build();

    private static final Cache<CommentCacheKey, CommentProto.ResponseGetTransientComment> COMMENT_LOCAL_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(60, TimeUnit.SECONDS)
            .expireAfterAccess(60, TimeUnit.SECONDS).build();


    static Cache<CommentCacheKey, CommentProto.ResponseGetTransientComment> getCommentLocalCache() {
        return COMMENT_LOCAL_CACHE;
    }

    /**
     * 获取评论
     *
     * @param liveId    直播id
     * @param startTime 开始时间  秒
     * @param endTime   结束时间 秒
     * @return 评论
     */
    public CommentProto.ResponseGetTransientComment getComment(long liveId, long startTime, long endTime) {
        CommentProto.ResponseGetTransientComment comment = COMMENT_LOCAL_CACHE.getIfPresent(CommentCacheKey.build(liveId, startTime, endTime));
        if (comment != null) {
            return comment;
        }
        return DEFAULT_COMMENT;
    }
}
