package fm.lizhi.app.live.manager.recreation.manager;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.amusement.pp.api.PPNewLiveAmusementService;
import fm.lizhi.live.amusement.pp.protocol.LiveAmusementProto;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;

@Slf4j
@AutoBindSingleton
public class LiveAmusementServiceManager {

    @Inject
    private PPNewLiveAmusementService liveAmusementService;

    /**
     * 判断房间麦上是否有空坐席
     *
     * @param userId
     * @param liveId
     * @return
     */
    public boolean fetchGuestCount(long userId, long liveId) {
        try {
            Result<LiveAmusementProto.ResponseGuestCount> result = liveAmusementService.fetchGuestCount(liveId);
            if (result.rCode() == 0) {
                long count = result.target().getCount();
                //小于8意思是有空位
                if (count < 8) {
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        } catch (Exception e) {
            log.error("LiveAmusementManager fetchGuestCount error userId={},liveId={}", userId, liveId, e);
            return true;
        }
    }
}