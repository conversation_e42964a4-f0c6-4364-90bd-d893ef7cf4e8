package fm.lizhi.app.live.manager.comment.manager;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.yibasan.lizhifm.protocol.LZModelsPtlbuf;
import fm.lizhi.app.live.config.CommentConfig;
import fm.lizhi.commons.local.cache.builder.CacheManager;
import fm.lizhi.commons.local.cache.impl.IgnoreErrorAdvisor;
import fm.lizhi.commons.local.cache.impl.MessageLiteServiceResultEncoder;
import fm.lizhi.live.pp.emotion.api.LivePpEmotionService;
import fm.lizhi.pp.oss.api.LiveEmotionService;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Created in 2018-04-04 11:46.<br/>
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class CacheLiveEmotionManager {
    @Inject
    private LiveEmotionService liveEmotionService;

    @Inject
    private LivePpEmotionService livePpEmotionService;

    @Inject
    private CacheManager cacheManager;
    @Inject
    private CommentConfig commentConfig;


    private LiveEmotionService normalCacheLiveEmotionService;

    private LivePpEmotionService normalCachedLivePpEmotionService;

    @Getter
    private Supplier<List<LZModelsPtlbuf.liveEmotion>> greetEmotionSupplier;

    @PostConstruct
    public void init() {
        normalCacheLiveEmotionService = cacheManager.buildCacheProxy(LiveEmotionService.class, liveEmotionService, commentConfig.normCacheSec,
                new MessageLiteServiceResultEncoder(), new IgnoreErrorAdvisor(), null);
        normalCachedLivePpEmotionService = cacheManager.buildCacheProxy(LivePpEmotionService.class, livePpEmotionService, commentConfig.normCacheSec,
                new MessageLiteServiceResultEncoder(), new IgnoreErrorAdvisor(), null);

        greetEmotionSupplier = Suppliers.memoizeWithExpiration(new Supplier<List<LZModelsPtlbuf.liveEmotion>>() {
            @Override
            public List<LZModelsPtlbuf.liveEmotion> get() {
                if (StringUtils.isBlank(commentConfig.greetEmotion)) {
                    return Collections.emptyList();
                }
                List<JSONObject> jsonObjects = JSONArray.parseArray(commentConfig.greetEmotion, JSONObject.class);
                List<LZModelsPtlbuf.liveEmotion> liveEmotions = new ArrayList<>();
                for (JSONObject jsonObject : jsonObjects) {
                    LZModelsPtlbuf.liveEmotion.Builder builder = LZModelsPtlbuf.liveEmotion.newBuilder();
                    builder.setEmotionId(jsonObject.getLong("emotionId"));
                    builder.setName(jsonObject.getString("name"));
                    builder.setSvgaUrl(jsonObject.getString("svgaUrl"));
                    builder.setAspect(jsonObject.getIntValue("aspect"));
                    builder.setFactor(jsonObject.getIntValue("factor"));
                    builder.setImage(jsonObject.getString("image"));
                    liveEmotions.add(builder.build());
                }

                return liveEmotions;

            }
        }, 10, TimeUnit.MINUTES);
    }

    public LiveEmotionService getNormalCacheLiveEmotionService() {
        return normalCacheLiveEmotionService;
    }

    public LivePpEmotionService getNormalCachedLivePpEmotionService() {
        return normalCachedLivePpEmotionService;
    }
}
