package fm.lizhi.app.live.manager.main.manager.validator;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.yibasan.lizhifm.protocol.LZModelsPtlbuf;
import fm.lizhi.app.live.config.LiveMainConfig;
import fm.lizhi.app.live.manager.main.manager.business.PpNewSessionManager;
import fm.lizhi.app.live.manager.main.manager.cache.LiveCacheNewService;
import fm.lizhi.app.live.manager.pc.LiveKickOutManager;
import fm.lizhi.app.live.utils.PromptUtils;
import fm.lizhi.live.room.pp.constants.LiveConstants;
import fm.lizhi.live.room.pp.protocol.LiveNewProto;
import fm.lizhi.live.room.pp.protocol.LiveProto;
import fm.lizhi.pp.util.utils.ConfigUtil;
import fm.lizhi.server.util.LizhiUtil;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@AutoBindSingleton
public class Validator {
    @Inject
    private PpNewSessionManager ppNewSessionManager;
    @Inject
    private LiveCacheNewService liveCacheNewService;
    @Inject
    private LiveMainConfig liveMainConfig;
    @Inject
    private LiveKickOutManager liveKickOutManager;

    public LZModelsPtlbuf.Prompt isMustLoginOrNeedUpgrade(long currUid, boolean firstEntry, int clientVersion, String deviceType) {
        LZModelsPtlbuf.Prompt mustLoginPrompt = isMustLogin(currUid, firstEntry, clientVersion, deviceType);
        if (null != mustLoginPrompt) {
            return mustLoginPrompt;
        }
        LZModelsPtlbuf.Prompt needUpgradePrompt = isNeedUpgrade(currUid, clientVersion, deviceType);
        return needUpgradePrompt;
    }

    private LZModelsPtlbuf.Prompt isMustLogin(long currUid, boolean firstEntry, int clientVersion, String deviceType) {
        if (liveMainConfig.limitPpMustLoginSwitch && currUid == 0 && firstEntry) {
            if (isLimitAndroidLogin(clientVersion, deviceType) || LizhiUtil.isIosDevice(deviceType)) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("type", 42);
                return PromptUtils.buildPrompt(3, "", jsonObject.toString());
            }
        }
        return null;
    }

    private LZModelsPtlbuf.Prompt isNeedUpgrade(long currUserId, int clientVersion, String deviceType) {
        if (LizhiUtil.isAndroidDevice(deviceType) && clientVersion == liveMainConfig.androidRtcBugVersion) {
            return PromptUtils.buildPrompt(0, liveMainConfig.androidRtcBugHint, "");
        }
        String ppWhiteList = liveMainConfig.ppWhiteList;
        List<String> users = Lists.newArrayList();
        if (!Strings.isNullOrEmpty(ppWhiteList)) {
            users = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(ppWhiteList);
        }
        if (LizhiUtil.isAndroidDevice(deviceType) && clientVersion <= liveMainConfig.ppNotSupportAndroidMaxVersion) {
            // 返回升级提示
            if (!users.contains(String.valueOf(currUserId))) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("type", 6);
                jsonObject.addProperty("url", liveMainConfig.ppAndroidNewVersionUrl);
                return PromptUtils.buildPrompt(1, liveMainConfig.ppUpdateText, jsonObject.toString());
            }
        } else if (LizhiUtil.isIosDevice(deviceType) && clientVersion <= liveMainConfig.ppNotSupportIosMaxVersion) {
            // 返回升级提示
            if (!users.contains(String.valueOf(currUserId))) {
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("type", 6);
                jsonObject.addProperty("url", liveMainConfig.ppIosNewVersionUrl);
                return PromptUtils.buildPrompt(1, liveMainConfig.ppUpdateText, jsonObject.toString());
            }
        }
        return null;
    }

    private boolean isLimitAndroidLogin(int clientVersion, String deviceType) {
        if (LizhiUtil.isAndroidDevice(deviceType) && clientVersion > liveMainConfig.ppAndroidMustLoginVersion) {
            return liveMainConfig.limitAndroidDevice;
        }
        return false;
    }


    public boolean checkSession(long userId, LZModelsPtlbuf.head head) {
        // 校验Session
        if (userId > 0) {
            return ppNewSessionManager.checkSession(userId, head.getSessionKey(), head.getDeviceType(), head.getClientVersion());
        }
        return true;
    }

    public LZModelsPtlbuf.Prompt validatePpLiveInfoCondition(long currUserId, boolean firstEntry, int clientVersion, String deviceType, LiveProto.Live live) {
        // 校验当前用户直播状态
        if (firstEntry) {
            // PP第一次进直播间的时候判断一下
            Optional<LiveNewProto.Live> optional = liveCacheNewService.getLiveByUserId(currUserId);
            if (liveMainConfig.forbidEnterOthersRoom && optional.isPresent() && optional.get().getStatus() == LiveConstants.LIVE_STATUS_ON_AIR && currUserId != live.getUserId()) {
                return PromptUtils.buildPrompt(0, "当前直播中，不能进入房间", "");
            }
        }
        // 校验敏感时期是否可以进入直播间
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            Date startTime = simpleDateFormat.parse(liveMainConfig.sensitiveStartTime);
            Date endTime = simpleDateFormat.parse(liveMainConfig.sensitiveEndTime);
            long now = System.currentTimeMillis();
            if (now >= startTime.getTime() && now <= endTime.getTime()) {
                return PromptUtils.buildPrompt(1, liveMainConfig.sensitiveText, "");
            }
        } catch (Exception ignored) {
        }

        // 是否被禁止进入直播间
        Boolean kickOut = liveKickOutManager.isKickOut(live.getLiveRoomId(), currUserId);
        if (kickOut) {
            return PromptUtils.buildPrompt(0, "你已被禁止进入此直播间", "");
        }

        // ios 旧版本无法进入立体声的直播间
        if (LizhiUtil.isIosDevice(deviceType) && clientVersion < ConfigUtil.getBizUtilsConf().getMinSupportStereoSoundIosVer()) {
            if (ConfigUtil.getBizUtilsConf().isStereoSoundFullyEnabled()) {
                if (live.getActualStartTime() > ConfigUtil.getBizUtilsConf().getStereoSoundFullyEnabledTime()) {
                    // 开播时间大于配置时间的，则开启了立体声
                    return PromptUtils.buildPrompt(0, "该直播间已开启立体声，请升级版本后进入", "");
                }
            } else {
                if (ConfigUtil.getBizUtilsConf().getStereoSoundLiveUserIds().contains(String.valueOf(live.getUserId()))) {
                    // 命中白名单，则进行提醒
                    return PromptUtils.buildPrompt(0, "该直播间已开启立体声，请升级版本后进入", "");
                }
            }
        }

        return null;
    }
}
