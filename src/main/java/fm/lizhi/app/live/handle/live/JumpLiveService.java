package fm.lizhi.app.live.handle.live;

import com.google.inject.Inject;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageLite;
import com.lizhi.pplive.PPliveBusiness;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.yibasan.common.connecter.exception.DataCenterException;
import com.yibasan.lizhifm.protocol.LizhiFMPtlbuf;
import fm.lizhi.app.live.bean.LiveInfo;
import fm.lizhi.app.live.config.AppPpCoreConf;
import fm.lizhi.app.live.manager.core.manager.LiveManager;
import fm.lizhi.app.live.manager.core.manager.LiveRoomManager;
import fm.lizhi.app.live.manager.core.manager.TransientCommentManager;
import fm.lizhi.app.live.manager.main.manager.PpVipManager;
import fm.lizhi.app.live.manager.pc.LiveKickOutManager;
import fm.lizhi.app.live.manager.user.PpNewUserManager;
import fm.lizhi.app.live.utils.PromptUtils;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.pp.constants.LiveStatus;
import fm.lizhi.pp.user.account.user.protocol.PpUserBaseProto;
import fm.lizhi.server.business.socket.service.UnbindService;
import fm.lizhi.server.conn.socket.codec.SocketContext;
import fm.lizhi.server.exception.LizhiException;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

import static fm.lizhi.app.live.constant.Constant.DEFAULT_ERROR_CODE;

/**
 * 跳转直播
 */
@AutoBindSingleton
@Slf4j
public class JumpLiveService extends UnbindService {

    /**
     * 1-首页陪陪推荐
     */
    private static final int TYPE_GUEST_RCMD = 1;

    /**
     * 2-房间内邀请
     */
    private static final int TYPE_ROOM_INVITE = 2;

    /**
     * 3-消息页房间入口
     */
    private static final int TYPE_CHAT_PAGE = 3;

    @Inject
    private LiveManager liveManager;
    @Inject
    private LiveRoomManager liveRoomManager;
    @Inject
    private PpVipManager ppVipManager;
    @Inject
    private TransientCommentManager transientCommentManager;
    @Inject
    private LiveKickOutManager liveKickOutManager;
    @Inject
    private AppPpCoreConf appPpCoreConf;
    @Inject
    private PpNewUserManager ppNewUserManager;

    @Override
    public void serve(SocketContext ctx) throws InvalidProtocolBufferException, DataCenterException, LizhiException {
        PPliveBusiness.RequestPPJumpLive request = PPliveBusiness.RequestPPJumpLive.parseFrom(ctx.getRequest().getData());
        PPliveBusiness.ResponsePPJumpLive.Builder builder = PPliveBusiness.ResponsePPJumpLive.newBuilder();

        // 校验会话
        if (!checkHead(ctx, LizhiFMPtlbuf.head.parseFrom(request.getHead().toByteArray()))) {
            addToRes(ctx, GlobalResultCode.SESSION_ERR, builder);
            return;
        }

        long userId = ctx.getCurrUid();
        int type = request.getType();
        long liveId = request.getLiveId();
        long targetGuestUid = request.getTargetGuestUid();

        ctx.addReqLog("userId={}`type={}`liveId={}`targetGuestUid={}", userId, type, liveId, targetGuestUid);
        ctx.addResLog("userId={}`type={}`liveId={}`targetGuestUid={}", userId, type, liveId, targetGuestUid);

        if (userId <= 0) {
            addToRes(ctx, GlobalResultCode.SESSION_ERR, builder);
            return;
        }

        // 参数校验
        if (type != TYPE_GUEST_RCMD && type != TYPE_ROOM_INVITE && type != TYPE_CHAT_PAGE) {
            builder.setPrompt(PromptUtils.buildSimplePrompt("参数异常"));
            addToRes(ctx, DEFAULT_ERROR_CODE, builder);
            return;
        }

        // 检查直播是否已关闭
        LiveInfo liveInfo = liveManager.getLive(liveId);

        if (liveInfo == null
                || liveInfo.getLive() == null) {
            // 直播信息不存在，则返回失败
            log.info("JumpLiveService live not exist userId={},liveId={}", userId, liveId);
            builder.setPrompt(PromptUtils.buildSimplePrompt("直播不存在"));
            addToRes(ctx, DEFAULT_ERROR_CODE, builder);
            return;
        }

        if (liveInfo.getLive().getStatus() != LiveStatus.ON_AIR.getValue()) {
            //获取用户最新的直播, 客户端传的liveId可能是旧的已经关闭的
            Long onAirLiveId = liveRoomManager.getLatestOnAirLiveId(liveInfo.getLive().getUserId());
            if (onAirLiveId != null && onAirLiveId > 0) {
                liveInfo = liveManager.getLive(onAirLiveId);
                if (liveInfo == null
                        || liveInfo.getLive() == null
                        || liveInfo.getLive().getStatus() != LiveStatus.ON_AIR.getValue()) {
                    log.info("JumpLiveService serve room is close userId={},liveId={}", userId, liveId);
                    builder.setPrompt(PromptUtils.buildSimplePrompt("直播已关闭"));
                    addToRes(ctx, DEFAULT_ERROR_CODE, builder);
                    return;
                } else {
                    log.info("JumpLiveService serve new liveId userId={},newLiveId={}", userId, onAirLiveId);
                    builder.setLiveId(onAirLiveId);
                }
            } else {
                log.info("JumpLiveService serve onAirLiveId is null userId={}", userId);
                builder.setPrompt(PromptUtils.buildSimplePrompt("直播已关闭"));
                addToRes(ctx, DEFAULT_ERROR_CODE, builder);
                return;
            }
        } else {
            log.info("JumpLiveService serve old liveId userId={},oldLiveId={}", userId, liveId);
            builder.setLiveId(liveId);
        }

        Optional<PpUserBaseProto.User> guestOpt = ppNewUserManager.getUserCache(targetGuestUid);
        if (!guestOpt.isPresent()) {
            addToRes(ctx, DEFAULT_ERROR_CODE, builder);
            return;
        }
        PpUserBaseProto.User guest = guestOpt.get();

        // 首页陪陪推荐入口跳转
        if (type == TYPE_GUEST_RCMD) {
            // 发送进房公屏
            if (appPpCoreConf.isRcmdGuestEnterRoomNotice()) {
                transientCommentManager.sendRcmdGuestEnterRoomNotice(liveInfo, userId, guest.getName());
            }

            builder.setApplySeatBeforeText(appPpCoreConf.getRcmdGuestApplySeatBeforeText());
            builder.setApplySeatAfterText(appPpCoreConf.getRcmdGuestApplySeatAfterText());

            //V3.3.0 提升复合场景付费-房间内邀请-通过邀请进房后,需要在公屏区显示来源
        } else if (type == TYPE_ROOM_INVITE) {
            Optional<PpUserBaseProto.User> userOptional = ppNewUserManager.getUserCache(userId);
            if (userOptional.isPresent()) {
                //判断用户是否有开启特权-状态隐身 否则不用发公屏
                boolean invisible = ppVipManager.isUserEnterNoticeInvisible(userId);
                if (!invisible) {
                    Boolean kickOut = liveKickOutManager.isKickOut(liveInfo.getLive().getLiveRoomId(), userId);
                    if (!kickOut) {
                        //PC端没有被提出,发送进房公屏
                        transientCommentManager.sendRoomInviteGuestEnterRoomNotice(liveInfo, userOptional.get(), guest.getName());
                    }
                }
            }

            //V3.3.0 提升复合场景付费-消息页房间入口-用户通过消息页某陪玩的房间入口进入房间后，判断该陪玩是否在麦上，如果陪玩在麦，则在公屏区对房间麦上用户上报来源
        } else if (type == TYPE_CHAT_PAGE) {
//            UserDto user = userManager.findUser(userId);
//            //发送进房公屏
//            transientCommentManager.sendChatPageGuestEnterRoomNotice(liveInfo, user, guest.getName());
        }

        addToRes(ctx, GeneralRCode.GENERAL_RCODE_SUCCESS, builder);
    }

    @Override
    public int getOP() {
        // OP = 12631
        return 0x3157;
    }

    @Override
    public MessageLite handlerError(int resCode) {
        return PPliveBusiness.ResponsePPJumpLive.newBuilder().setRcode(resCode).build();
    }
}
