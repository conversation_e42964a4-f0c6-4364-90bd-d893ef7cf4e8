package fm.lizhi.app.live.handle.comment;

import com.google.inject.Inject;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.MessageLite;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.yibasan.common.connecter.exception.DataCenterException;
import com.yibasan.lizhifm.protocol.LizhiFMPtlbuf;
import fm.lizhi.app.live.manager.comment.manager.adapter.LiveCommentBubbleEffectsAdapter;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.commons.utils.CompressUtils;
import fm.lizhi.pp.oss.api.CommentStyleService;
import fm.lizhi.pp.oss.protocol.CommentStyleProto;
import fm.lizhi.server.business.socket.service.UnbindService;
import fm.lizhi.server.conn.socket.codec.SocketContext;
import fm.lizhi.server.exception.LizhiException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 获取直播评论气泡样式列表
 * Created in 2018-05-14 20:14.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class LiveCommentBubbleEffectsService extends UnbindService {
    private static Logger logger = LoggerFactory.getLogger(LiveCommentBubbleEffectsService.class);
    @Inject
    private LiveCommentBubbleEffectsAdapter adapter;
    @Inject
    private CommentStyleService commentStyleService;


    /**
     * 操作服务入口，例如：登陆、注册等。
     *
     * @param ctx socket请求传送内容
     */
    @Override
    public void serve(SocketContext ctx) throws InvalidProtocolBufferException, DataCenterException, LizhiException {
        LizhiFMPtlbuf.RequestLiveCommentBubbleEffects requestLiveCommentBubbleEffects = LizhiFMPtlbuf.RequestLiveCommentBubbleEffects.parseFrom(ctx
                .getRequest().getData());
        LizhiFMPtlbuf.ResponseLiveCommentBubbleEffects.Builder builder = LizhiFMPtlbuf.ResponseLiveCommentBubbleEffects.newBuilder();

        if (!checkHead(ctx, requestLiveCommentBubbleEffects.getHead())) {
            addToRes(ctx, GlobalResultCode.SESSION_ERR, builder);
            return;
        }

        String performanceId = requestLiveCommentBubbleEffects.getPerformanceId();
        long times = 0;
        if (StringUtils.isNotBlank(performanceId)) {
            try {
                times = Long.valueOf(performanceId);
            } catch (NumberFormatException e) {
            }
        }
        Result<CommentStyleProto.ResponseGetCommentStyleList> result = this.commentStyleService.getCommentStyleList(times);
        if (result.rCode() != 0 || result.target() == null) {
            logger.error("getCommentStyleList error, rcode:{}", result.rCode());
            addToRes(ctx, GlobalResultCode.BUSY, builder);
            return;
        }

        builder.setPerformanceId(String.valueOf(result.target().getTimes()));

        // 无更新
        if (!result.target().getUpdate()) {
            addToRes(ctx, 1, builder);
            return;
        }

        LizhiFMPtlbuf.liveGeneralData.Builder liveGeneralDataBuilder = LizhiFMPtlbuf.liveGeneralData.newBuilder();
        LizhiFMPtlbuf.liveCommentBubbleEffects.Builder effectsBuilder = LizhiFMPtlbuf.liveCommentBubbleEffects.newBuilder();
        effectsBuilder.addAllEffects(this.adapter.adapter(requestLiveCommentBubbleEffects.getHead().getDeviceType(), result.target().getCommentStylesList()));

        // 是否压缩
        byte[] bytes = effectsBuilder.build().toByteArray();
        if (bytes.length > 100) {
            liveGeneralDataBuilder.setFormat(1);
            liveGeneralDataBuilder.setData(ByteString.copyFrom(CompressUtils.gzipCompress(bytes)));
        } else {
            liveGeneralDataBuilder.setFormat(0);
            liveGeneralDataBuilder.setData(ByteString.copyFrom(bytes));
        }
        builder.setRcode(GlobalResultCode.SUCCESS);
        builder.setEffects(liveGeneralDataBuilder.build());
        addToRes(ctx, GlobalResultCode.SUCCESS, builder);
    }

    /**
     * Return the OP code of this service
     */
    @Override
    public int getOP() {
        // OP = 4631
        return 0x1217;
    }

    @Override
    public MessageLite handlerError(int resCode) {
        return LizhiFMPtlbuf.ResponseLiveCommentBubbleEffects.newBuilder().setRcode(resCode).build();
    }
}
