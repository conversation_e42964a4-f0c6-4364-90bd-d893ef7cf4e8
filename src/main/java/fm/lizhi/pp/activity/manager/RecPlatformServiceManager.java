package fm.lizhi.pp.activity.manager;

import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.google.protobuf.ByteString;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.pprec.platform.api.RecPlatformService;
import fm.lizhi.pprec.platform.protocol.RecPlatformProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.util.List;

/**
 * <AUTHOR>
 * 推荐平台接口管理
 */
@Slf4j
@AutoBindSingleton
public class RecPlatformServiceManager {
    @Inject
    private RecPlatformService recPlatformService;

    /**
     * 匹配推荐
     *
     * @param userId     用户id
     * @param deviceId   设备id
     * @param verCode    版本
     * @param deviceType 设备类型
     * @param appId      app
     * @param subAppId
     * @param categoryId 类别id
     * @return liveId，如果为0，则表示无数据
     */
    public ImmutablePair<Long, ByteString> rcmdMatch(long userId, String deviceId, int verCode, String deviceType,
                                                     int appId, int subAppId, String categoryId) {
        try {
            JSONObject extraJson = new JSONObject();
            extraJson.put("categoryId", categoryId);
            extraJson.put("freshType", 3);

            Result<RecPlatformProto.ResponseRecPlatform> result = recPlatformService.rcmd(userId, deviceId, verCode,
                    deviceType, appId, subAppId, "pp", "roomMatch", 0, 1, extraJson.toJSONString());
            if (result.rCode() != 0) {
                log.error("recPlatformService rcmd error, userId={}`deviceId={}`categoryId={}`rCode={}",
                        userId, deviceId, categoryId, result.rCode());
                return ImmutablePair.of(0L, null);
            }

            List<RecPlatformProto.RcmdItem> itemList = result.target().getItemList();
            log.info("recPlatformService rcmd success, userId={}`deviceId={}`categoryId={}`rcmdListSize={}",
                    userId, deviceId, categoryId, itemList.size());
            return ImmutablePair.of(itemList.get(0).getItemId(), itemList.get(0).getReportjsonBytes());
        } catch (Exception e) {
            log.error(String.format("recPlatformService rcmd error, userId={}`deviceId={}`categoryId={}`e={}",
                    userId, deviceId, categoryId, e));
            return ImmutablePair.of(0L, null);
        }
    }

}
