package fm.lizhi.pp.activity.manager;

import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.pp.activity.bean.CategoryMatchConfig;
import fm.lizhi.pp.activity.conf.LzConfig;
import fm.lizhi.pp.content.artrcmd.api.HomeRandomMatchService;
import fm.lizhi.pp.content.artrcmd.bean.HomeMatchInfoDto;
import fm.lizhi.pp.content.artrcmd.bean.HomeRandomMatchDto;
import fm.lizhi.pp.content.artrcmd.protocol.HomeRandomMatchProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

/**
 * 首页匹配管理器
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class HomeMatchManager {

    @Inject
    private HomeRandomMatchService homeRandomMatchService;
    @Inject
    private LzConfig lzConfig;

    /**
     * （找小哥哥找小姐姐）保存首页随机匹配信息
     * @param userId 用户id
     * @param cateId 分类id
     */
    public void saveHomeRandomMatchInfo(Long userId, Long cateId) {
        CategoryMatchConfig categoryMatchConfig =
                JSONObject.parseObject(lzConfig.getCategoryMatchConfig(), CategoryMatchConfig.class);

        for (CategoryMatchConfig.CategoryMatchConfigBean categoryMatchConfigBean : categoryMatchConfig.getConfigBeanList()) {
            if (categoryMatchConfigBean.getId() == cateId) {
                // 构建对象
                HomeMatchInfoDto dto = new HomeMatchInfoDto();
                dto.setMatchType(0);
                dto.setUserId(userId);
                dto.setCategoryMatchConfigId(cateId);
                dto.setCategoryMatchName(categoryMatchConfigBean.getName());

                // 保存默认的匹配信息
                Result<HomeRandomMatchProto.ResponseSaveHomeDefaultMatchResult> result =
                        homeRandomMatchService.saveHomeDefaultMatchResult(JSONObject.toJSONString(dto));

                if (result.rCode() != 0) {
                    log.error("saveHomeDefaultMatchResult fail. dto={}", dto);
                }
            }
        }
    }

    /**
     * （找小哥哥找小姐姐）首页随机匹配
     * @param userId 用户id
     * @param cateId 分类卡id
     * @param freeGiftQualify 免费礼物资格
     * @return left：直播id，right：陪玩id
     */
    public Pair<Long, Long> homeRandomMatch(Long userId, Long cateId, boolean freeGiftQualify) {
        HomeRandomMatchDto dto = new HomeRandomMatchDto();
        dto.setUserId(userId);
        dto.setMatchCardId(cateId);
        dto.setFreeGiftQualify(freeGiftQualify);

        Result<HomeRandomMatchProto.ResponseHomeRandomMatch> result =
                homeRandomMatchService.homeRandomMatch(JSONObject.toJSONString(dto));
        if (result.rCode() != 0) {
            log.warn("homeRandomMatch fail. rCode={}`dto={}", result.rCode(), dto);
            return null;
        }

        return Pair.of(result.target().getLiveId(), result.target().getPlayerId());
    }

}
