package fm.lizhi.pp.activity.utils;

import com.google.common.base.Strings;
import com.yibasan.lizhifm.protocol.LZModelsPtlbuf;
import fm.lizhi.server.util.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PromptUtils {

    private static Logger logger = LoggerFactory.getLogger(PromptUtils.class);
    /**
     * 0：Toast提示
     */
    public final static int TYPE_TOAST = 0;

    /**
     * 1：对话框提示，点击确认后使用action跳转（如果有），无取消按钮
     */
    public final static int TYPE_DIALOG_NO_CANCLE = 1;

    /**
     * 2：对话框提示，点击确认后使用action跳转，有取消按钮
     */
    public final static int TYPE_DIALOG = 2;

    /**
     * 3：无提示，直接使用action跳转
     */
    public final static int TYPE_NO_PROMPT = 3;

    /**
     * 构建提示信息
     *
     * @param type   类型
     * @param msg    提示
     * @param action 跳转动作
     */
    public static LZModelsPtlbuf.Prompt buildPrompt(int type, String msg, String action) {
        LZModelsPtlbuf.Prompt.Builder promptBuilder = LZModelsPtlbuf.Prompt.newBuilder();
        promptBuilder.setType(type);
        promptBuilder.setMsg(msg);
        if (!Strings.isNullOrEmpty(action)) {
            promptBuilder.setAction(action);
        }
        return promptBuilder.build();
    }

    public static LZModelsPtlbuf.Prompt buildSimplePrompt(@NonNull String msg) {
        LZModelsPtlbuf.Prompt.Builder builder = LZModelsPtlbuf.Prompt.newBuilder();
        builder.setType(0);
        builder.setMsg(msg);
        return builder.build();
    }

    public static LZModelsPtlbuf.Prompt buildPrompt(@NonNull String msg, int type) {
        LZModelsPtlbuf.Prompt.Builder builder = LZModelsPtlbuf.Prompt.newBuilder();
        builder.setType(TYPE_DIALOG);
        builder.setMsg(msg);
        builder.setAction(JsonUtil.dumps(new Action(type)));
        return builder.build();
    }

    public static LZModelsPtlbuf.Prompt buildPrompt(@NonNull String msg) {
        LZModelsPtlbuf.Prompt.Builder builder = LZModelsPtlbuf.Prompt.newBuilder();
        builder.setType(0);
        builder.setMsg(msg);
        return builder.build();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class Action {
        private Integer type;
    }
}
