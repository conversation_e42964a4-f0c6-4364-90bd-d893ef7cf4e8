package fm.lizhi.pp.activity.idl;

import com.pione.protocol.activity.request.RequestScreenCastingPolling;
import com.pione.protocol.activity.response.ResponseScreenCastingPolling;
import com.pione.protocol.activity.service.ScreenCastingService;
import com.pione.protocol.common.CommonMedia;
import fm.lizhi.common.lthrift.Result;
import fm.lizhi.common.lthrift.annotation.LThriftProvider;
import fm.lizhi.pp.activity.conf.LzConfig;
import fm.lizhi.pp.activity.manager.ScreenCastingManager;
import fm.lizhi.pp.screencasting.constant.ScreenCastingStatus;
import fm.lizhi.pp.screencasting.dto.ScreenCastingInfoDto;
import fm.lizhi.pp.util.utils.UrlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/5 15:30
 */
@LThriftProvider
@Slf4j
public class ScreenCastingServiceImpl implements ScreenCastingService {
    @Autowired
    private ScreenCastingManager screenCastingManager;
    @Autowired
    private LzConfig lzConfig;

    @Override
    public Result<ResponseScreenCastingPolling> screenCastingPolling(RequestScreenCastingPolling req) {
        long liveId = req.getLiveId();

        ResponseScreenCastingPolling res = new ResponseScreenCastingPolling();
        res.setRequestInterval(lzConfig.getScreenCastingPollingReqInterval());
        ScreenCastingInfoDto screenCastingInfo = screenCastingManager.getScreenCastingInfo(liveId);
        if (screenCastingInfo != null && screenCastingInfo.getStatus() == ScreenCastingStatus.IN_PROGRESS.getStatus()) {
            res.setScreenCastingId(screenCastingInfo.getId());
            List<CommonMedia> images = screenCastingInfo.getImages().stream()
                    .map(e -> {
                        CommonMedia commonMedia =new CommonMedia();
                        commonMedia.setMediaId(e.getId());
                        commonMedia.setUrl(UrlUtils.appendRomaCdnPrefix(e.getUrl()));
                        return commonMedia;
                    })
                    .collect(Collectors.toList());
            res.setImages(images);
            res.setCurImageId(screenCastingInfo.getCurImageId());
        }
        return new Result<>(0, res);
    }
}
