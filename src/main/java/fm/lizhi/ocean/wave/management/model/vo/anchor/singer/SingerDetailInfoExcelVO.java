package fm.lizhi.ocean.wave.management.model.vo.anchor.singer;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

@Data
public class SingerDetailInfoExcelVO {

    /**
     * 歌手信息
     */
    @ExcelProperty(value = "歌手ID")
    private String singerUserId;

    /**
     * 用户名
     */
    @ExcelProperty(value = "歌手用户名")
    private String singerName;

    /**
     * 波段号
     */
    @ExcelProperty(value = "歌手波段号")
    private String singerBand;

    /**
     * 性别
     */
    @ExcelProperty(value = "歌手性别")
    private String singerGender;

    /**
     * 家族名称
     */
    @ExcelProperty(value = "家族名称")
    private String familyName;

    /**
     * 厅主ID
     */
    @ExcelProperty(value = "厅主ID")
    private String njId;

    /**
     * 用户名
     */
    @ExcelProperty(value = "厅主用户名")
    private String njName;

    /**
     * 波段号
     */
    @ExcelProperty(value = "厅主波段号")
    private String njBand;

    /**
     * 歌手状态 1: 认证中 2: 生效中  3: 已淘汰
     */
    @ExcelProperty(value = "歌手状态")
    private String singerStatus;

    /**
     * 歌曲风格
     */
    @ExcelProperty(value = "歌曲风格")
    private String songStyle;

    /**
     * 是否原创歌手
     */
    @ExcelProperty(value = "是否原创歌手")
    private String originalSinger;

    /**
     * 是否已发放奖励 0 未发放 1 已发放
     */
    @ExcelProperty(value = "是否已发放奖励")
    private String rewardsIssued;

    /**
     * 淘汰时间
     */
    @ExcelProperty(value = "淘汰时间")
    private String eliminationTime;

    /**
     * 通过时间
     */
    @ExcelProperty(value = "通过时间")
    private String auditTime;

    /**
     * 淘汰原因
     */
    @ExcelProperty(value = "淘汰原因")
    private String eliminationReason;

    /**
     * 操作人
     */
    @ExcelProperty(value = "操作人")
    private String operator;
}
