package fm.lizhi.ocean.wave.management.model.param.activitycenter;

import lombok.Data;

import java.util.List;

/**
 * 更新活动模板上下架状态参数
 */
@Data
public class UpdateActivityTemplateShelfStatusParam {

    /**
     * 活动模板id
     */
    private Long id;

    /**
     * 封面地址
     */
    private String cover;

    /**
     * 上下架状态
     */
    private Integer status;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 是否热门推荐
     */
    private Boolean hotRec;

    /**
     * 热门推荐权重
     */
    private Integer hotWeight;

    /**
     * 活动模板亮点标签列表
     */
    private List<Highlight> highlights;

    /**
     * 上架开始时间
     */
    private Long upStartTime;

    /**
     * 上架结束时间
     */
    private Long upEndTime;

    /**
     * 厅主白名单列表
     */
    private List<Long> njWhiteList;

    /**
     * 活动模板高亮标签
     */
    @Data
    public static class Highlight {

        /**
         * 亮点标签key
         */
        private String highlightKey;

        /**
         * 亮点标签value
         */
        private String highlightValue;
    }
}
