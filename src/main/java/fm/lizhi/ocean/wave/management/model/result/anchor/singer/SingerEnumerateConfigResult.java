package fm.lizhi.ocean.wave.management.model.result.anchor.singer;

import java.util.List;

import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.DecorateTypeVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerTypeVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SingerVerifyAuditStatusVO;
import fm.lizhi.ocean.wave.management.model.vo.anchor.singer.SongStyleVO;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.DecorateTypeBean;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.bean.SingerTypeBean;
import lombok.Data;

@Data
public class SingerEnumerateConfigResult {

    /**
     * 歌曲风格列表
     */
    private List<SongStyleVO> songStyle;

    /**
     * 审核状态列表
     */
    private List<SingerVerifyAuditStatusVO> auditStatus;

    /**
     * 歌手类型
     */
    private List<SingerTypeVO> singerType;

    /**
     * 可发放装扮类型
     */
    private List<DecorateTypeVO> decorateType;

}
