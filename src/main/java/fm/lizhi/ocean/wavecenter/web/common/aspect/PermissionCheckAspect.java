package fm.lizhi.ocean.wavecenter.web.common.aspect;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.api.permissions.request.RequestGetUserRoomDataScope;
import fm.lizhi.ocean.wavecenter.api.permissions.service.UserPermissionService;
import fm.lizhi.ocean.wavecenter.api.user.bean.LoginRoleInfoBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserLoginService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.web.common.MsgCodes;
import fm.lizhi.ocean.wavecenter.web.common.ResultVO;
import fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck;
import fm.lizhi.ocean.wavecenter.web.common.context.ContextUtils;
import fm.lizhi.ocean.wavecenter.web.common.context.ServiceContext;
import fm.lizhi.ocean.wavecenter.web.common.util.EnvUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/28 18:01
 */
@Slf4j
@Aspect
@Component
@Order(20)
public class PermissionCheckAspect {

    @Autowired
    private UserLoginService userLoginService;
    @Autowired
    private UserPermissionService userPermissionService;

    @Pointcut("@within(fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck) || @annotation(fm.lizhi.ocean.wavecenter.web.common.anno.PermissionCheck)")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        ServiceContext serviceContext = ContextUtils.getContext();
        PermissionCheck permissionCheck = getAnnotation(joinPoint, PermissionCheck.class);

        Long userId = serviceContext.getUserId();
        BusinessEvnEnum businessEvn = serviceContext.getBusinessEvnEnum();

        if (userId == VerifyUserTokenAspect.EMPTY_USER_ID || businessEvn == null) {
            return ResultVO.failure(MsgCodes.NOT_LOGGED_IN);
        }

        if (EnvUtil.isTestEnv()) {
            return handlerTestEnv(joinPoint, permissionCheck, serviceContext);
        } else {
            return handlerPro(joinPoint, permissionCheck, serviceContext);
        }
    }

    /**
     * 正式环境
     * @param joinPoint
     * @param permissionCheck
     * @param serviceContext
     * @return
     * @throws Throwable
     */
    private Object handlerPro(ProceedingJoinPoint joinPoint, PermissionCheck permissionCheck, ServiceContext serviceContext) throws Throwable {
        String token = ContextUtils.getContext().getToken();
        Result<LoginRoleInfoBean> response = userLoginService.getUserLoginRoleSimpleInfo(token);
        //查询用户角色
        if (response.rCode() == UserLoginService.ROLE_AUTH_NOT_EXIST) {
            return ResultVO.failure(MsgCodes.CHOSE_DATA_SCOPE);
        }
        if (RpcResult.isFail(response)) {
            log.warn("getUserLoginRoleSimpleInfo fail. rCode={},token={},userId={}"
                    , response.rCode(), token, ContextUtils.getContext().getUserId());
            return ResultVO.failure("请重新登录");
        }
        String userRoleCode = response.target().getRoleCode();
        if (StringUtils.isBlank(userRoleCode)) {
            log.warn("userRoleCode is empty. token={},userId={}", token, ContextUtils.getContext().getUserId());
            return ResultVO.failure(MsgCodes.PERMISSION_ERROR);
        }
        serviceContext.setRoleCode(userRoleCode);
        serviceContext.setSubjectId(response.target().getSubjectId());
        serviceContext.setLoginType(response.target().getLoginType());

        return checkPermission(joinPoint, permissionCheck, serviceContext);
    }

    /**
     * 测试环境
     * @param joinPoint
     * @param permissionCheck
     * @param serviceContext
     * @return
     * @throws Throwable
     */
    private Object handlerTestEnv(ProceedingJoinPoint joinPoint, PermissionCheck permissionCheck, ServiceContext serviceContext) throws Throwable {
        Result<LoginRoleInfoBean> response = userLoginService.getUserLoginRoleSimpleInfo(ContextUtils.getContext().getToken());
        if (RpcResult.isSuccess(response)) {
            String userRoleCode = response.target().getRoleCode();
            if (StringUtils.isNotBlank(userRoleCode)) {
                serviceContext.setRoleCode(userRoleCode);
                serviceContext.setSubjectId(response.target().getSubjectId());
                serviceContext.setLoginType(response.target().getLoginType());
            }
        } else {
            if (StringUtils.isNotBlank(serviceContext.getHeader().getRoleCode())) {
                //请求头有写死才赋值
                serviceContext.setRoleCode(ContextUtils.getContext().getHeader().getRoleCode());
                serviceContext.setSubjectId(ContextUtils.getContext().getHeader().getSubjectId());
                serviceContext.setLoginType(ContextUtils.getContext().getHeader().getLoginType());
            } else {
                //查询用户角色
                if (response.rCode() == UserLoginService.ROLE_AUTH_NOT_EXIST) {
                    return ResultVO.failure(MsgCodes.CHOSE_DATA_SCOPE);
                }
                if (RpcResult.isFail(response)) {
                    return ResultVO.failure("请重新登录");
                }
            }
        }

        return checkPermission(joinPoint, permissionCheck, serviceContext);
    }

    /**
     * 权限检查
     * @param joinPoint
     * @param permissionCheck
     * @param serviceContext
     * @return
     * @throws Throwable
     */
    private Object checkPermission(ProceedingJoinPoint joinPoint, PermissionCheck permissionCheck, ServiceContext serviceContext) throws Throwable {
        MsgCodes passRoleCheck = passRoleCheck(permissionCheck, serviceContext);
        if (passRoleCheck != MsgCodes.SUCCESS) {
            return ResultVO.failure(passRoleCheck);
        }

        MsgCodes selfLoginCheck = selfLoginCheck(permissionCheck);
        if (selfLoginCheck != MsgCodes.SUCCESS) {
            return ResultVO.failure(selfLoginCheck);
        }

        MsgCodes roomDataScopeCheck = roomDataScopeCheck(serviceContext);
        if (roomDataScopeCheck != MsgCodes.SUCCESS) {
            return ResultVO.failure(roomDataScopeCheck);
        }

        return joinPoint.proceed();
    }

    /**
     * 角色检查
     * @param permissionCheck
     * @param serviceContext
     * @return
     */
    private MsgCodes passRoleCheck(PermissionCheck permissionCheck, ServiceContext serviceContext){
        RoleEnum[] passRole = permissionCheck.passRole();
        List<String> passRoleCode = Arrays.stream(passRole).map(RoleEnum::getRoleCode).collect(Collectors.toList());

        RoleEnum[] excludeRole = permissionCheck.excludeRole();
        List<String> excludeRoleCode = Arrays.stream(excludeRole).map(RoleEnum::getRoleCode).collect(Collectors.toList());

        if (passRoleCode.isEmpty() && !excludeRoleCode.isEmpty()) {
            if (!excludeRoleCode.contains(serviceContext.getRoleCode())) {
                return MsgCodes.SUCCESS;
            }
        }

        if (passRoleCode.contains(serviceContext.getRoleCode())) {
            return MsgCodes.SUCCESS;
        }

        if (passRoleCode.isEmpty() && excludeRoleCode.isEmpty()) {
            return MsgCodes.SUCCESS;
        }

        return MsgCodes.PERMISSION_ERROR;
    }

    /**
     * 只能本人登录检查
     * @return
     */
    private MsgCodes selfLoginCheck(PermissionCheck permissionCheck){
        if (!loginTypeCheck(permissionCheck)) {
            return MsgCodes.ONLY_SELF_LOGIN;
        }
        return MsgCodes.SUCCESS;
    }

    /**
     * 厅数据范围检查
     * @return
     */
    private MsgCodes roomDataScopeCheck(ServiceContext serviceContext){
        if (!RoleEnum.FAMILY_ADMIN.getRoleCode().equals(serviceContext.getRoleCode())) {
            return MsgCodes.SUCCESS;
        }

        //查询用户的厅数据权限范围
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        long userId = ContextUtils.getContext().getUserId();
        Result<List<Long>> result = userPermissionService.getUserRoomDataScope(new RequestGetUserRoomDataScope()
                .setAppId(appId)
                .setUserId(userId)
        );
        if (RpcResult.isFail(result)) {
            log.error("login getUserRoomDataScope fail. rCode={},appId={},userId={}", result.rCode(), appId, userId);
            return MsgCodes.FAIL;
        }

        List<Long> authRoomIds = result.target();
        if (CollectionUtils.isEmpty(authRoomIds)) {
            log.warn("login dataScopeEmpty. appId={},userId={}", appId, userId);
            //为空说明角色异常，跳转到角色选择页
            return MsgCodes.CHOSE_DATA_SCOPE;
        }
        serviceContext.putRoomResource(authRoomIds);
        return MsgCodes.SUCCESS;
    }

    /**
     * 登录方式检查
     * @return
     */
    private boolean loginTypeCheck(PermissionCheck permissionCheck){
        if (permissionCheck.onlySelfLogin() && (!ContextUtils.getContext().isSelfLogin())) {
            return false;
        }
        return true;
    }

    /**
     * 获取切入目标上指定的Annotation。此方法优先从方法获取，若不存在则从类获取
     *
     * @param pjp
     * @param annotationClass
     * @return
     */
    protected <T extends Annotation> T getAnnotation(final ProceedingJoinPoint pjp, Class<T> annotationClass) {
        Method targetMethod = getTargetMethod(pjp);
        T annotation = targetMethod.getAnnotation(annotationClass);
        if (annotation == null) {
            annotation = targetMethod.getDeclaringClass().getAnnotation(annotationClass);
        }
        return annotation;
    }

    /**
     * 获取切入目标方法
     *
     * @return
     */
    protected Method getTargetMethod(final ProceedingJoinPoint pjp) {
        return ((MethodSignature) pjp.getSignature()).getMethod();
    }
}
