package fm.lizhi.ocean.wavecenter.web.module.live.constant;

/**
 * 导出麦序福利厅明细的指标类型
 * <AUTHOR>
 */
public enum CheckInExportMetricsType {

    /**
     * 主播波段号
     */
    playerId,

    /**
     * 排档数
     */
    scheduledCnt,

    /**
     * 有效麦序
     */
    seatCnt,

    /**
     * 主持档数
     */
    hostCnt,

    /**
     * 主持档魅力值合计
     */
     hostCharmSum ,

    /**
     * 未完成任务分
     */
     notDoneScore ,

    /**
     * 有效麦序魅力值
     */
     seatCharm ,

    /**
     * 合计魅力值
     */
     sumCharm ,

    /**
     * 收光记录, 格式为"1280*1,2270*2"
     */
     lightGift ,

    /**
     * 收光奖励
     */
     lightGiftAmount ,

    /**
     * 合计钻石值
     */
     sumIncome ,

    /**
     * 全麦记录 格式：1680*2,2270*2 （阶梯魅力值 * 全麦记录数）
     */
     allMicGift ,

    /**
     * 全麦奖励
     */
     allMicGiftAmount ,

    /**
     * 未完成任务分
     */
     notDoneScoreDetail ,

    /**
     * 日麦序奖励
     */
     dayMicAmount ,

    /**
     * 私信人数
     */
     chatUserCnt ,

    /**
     * 私信回复率
     */
    chatUserReplyRate,


    /**
     * 魅力值明细
     */
    charmDetail,

    /**
     * 备注
     */
     remark ,


}
