package fm.lizhi.ocean.seal.singleton.game;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.proxy.ProxyBuilder;
import fm.lizhi.ocean.seal.api.GameService;


/**
 * Created in 2022-04-28 16:40.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class GameServiceProvider implements Provider<GameService> {

    @Inject
    protected ProxyBuilder proxyBuilder;

    @Override
    public GameService get() {
        return proxyBuilder.buildProxy(GameService.class);
    }
}
