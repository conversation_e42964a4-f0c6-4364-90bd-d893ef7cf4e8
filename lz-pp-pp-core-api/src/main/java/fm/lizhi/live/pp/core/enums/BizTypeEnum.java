package fm.lizhi.live.pp.core.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;

import java.util.Map;

@AllArgsConstructor
@Getter
@ToString
public enum BizTypeEnum {

    // --- 用户资料相关 begin
    /**
     * 资料页语音条
     */
    DATE_PLAY_MEDIA_AUDIO(11, true),
    /**
     * 资料页视频
     */
    DATE_PLAY_MEDIA_VIDEO(12, true),
    /**
     * 陪玩认证语音条
     */
    PLAYER_AUTH_MEDIA_AUDIO(13, false),
    /**
     * 房间陪玩认证语音条（暂没用到）
     */
    ROOM_AUTH_MEDIA_AUDIO(14, false),
    /**
     * 陪玩技能语音条
     */
    PLAYER_SKILL_MEDIA_AUDIO(15, false),
    /**
     * 资料页相册照片
     */
    USER_PROFILE_IMG(16, true),

    // --- 用户资料相关 end

    // --- 动态 begin
    /**
     * 动态语音
     */
    TREND_MEDIA_AUDIO(21, false),
    /**
     * 动态视频
     */
    TREND_MEDIA_VIDEO(22, false),

    // --- 动态 end

    // --- 点唱 begin
    /**
     * 点唱-歌单
     */
    SONG_LIST(31, false),
    /**
     * 歌手认证语音条
     */
    SINGER_AUTH_AUDIO(32, false),
    // --- 点唱 end

    /**
     * 直播间视频封面
     */
    DYN_LIVE_COVER(33, false),

    /**
     * 点唱-歌单（多张歌单的场景）
     */
    PLAYLIST(34, false),
    /**
     * 表情包
     */
    DIY_EMOTION(35, false),

    ;

    /**
     * 序列
     */
    private final int index;

    /**
     * 敏感时期是否限制操作
     */
    private final boolean sensitiveLimit;

    private static final Map<Integer, BizTypeEnum> MAP = Maps.newHashMap();

    static {
        for (BizTypeEnum typeEnum : BizTypeEnum.values()) {
            MAP.put(typeEnum.index, typeEnum);
        }
    }

    /**
     * 获取对应枚举类
     *
     * @param bizType 上传业务类型
     * @return
     */
    public static BizTypeEnum getUploadBizType(@NonNull Integer bizType) {
        return MAP.get(bizType);
    }

}
