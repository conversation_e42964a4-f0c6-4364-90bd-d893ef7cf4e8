package fm.lizhi.live.pp.core.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.pp.core.protocol.LiveTrendProto.RequestGetLiveTrend;
import fm.lizhi.live.pp.core.protocol.LiveTrendProto.ResponseGetLiveTrend;
import fm.lizhi.live.pp.core.protocol.LiveTrendProto.LiveTrendParam;
import fm.lizhi.live.pp.core.protocol.LiveTrendProto.LiveTrendEntrance;
import fm.lizhi.live.pp.core.protocol.LiveTrendProto.LiveTrendUser;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface PpLiveTrendService {
	
	
	/**
	 *  获取用户直播动态
	 *
	 * @param liveTrendParam
	 *            
	 * @return 
	 *     //if rcode == 0 成功<br>
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 失败<br>
	 */
	@Service(domain = 650, op = 1600, request = RequestGetLiveTrend.class, response = ResponseGetLiveTrend.class)
	@Return(resultType = ResponseGetLiveTrend.class)
	Result<ResponseGetLiveTrend> getLiveTrend(@Attribute(name = "liveTrendParam") LiveTrendParam liveTrendParam);
	
	
	public static final int GET_LIVE_TREND_SUCCESS = 0; // 成功
	public static final int GET_LIVE_TREND_PARAMS_ERROR = 1; // 参数非法
	public static final int GET_LIVE_TREND_FAIL = 2; // 失败


}