// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_list_invite_room.proto

package fm.lizhi.live.pp.user.protocol;

public final class PpUserListInviteRoomProto {
  private PpUserListInviteRoomProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestUserListInviteRoomOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户id
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户id
     * </pre>
     */
    long getUserId();

    // optional int64 targetUserId = 2;
    /**
     * <code>optional int64 targetUserId = 2;</code>
     *
     * <pre>
     *目标用户id
     * </pre>
     */
    boolean hasTargetUserId();
    /**
     * <code>optional int64 targetUserId = 2;</code>
     *
     * <pre>
     *目标用户id
     * </pre>
     */
    long getTargetUserId();

    // optional int64 liveId = 3;
    /**
     * <code>optional int64 liveId = 3;</code>
     *
     * <pre>
     *直播间ID
     * </pre>
     */
    boolean hasLiveId();
    /**
     * <code>optional int64 liveId = 3;</code>
     *
     * <pre>
     *直播间ID
     * </pre>
     */
    long getLiveId();

    // optional string msgSource = 4;
    /**
     * <code>optional string msgSource = 4;</code>
     *
     * <pre>
     *邀请来源: chat-最近聊天页  customer-客户列表页  fans-粉丝列表页
     * </pre>
     */
    boolean hasMsgSource();
    /**
     * <code>optional string msgSource = 4;</code>
     *
     * <pre>
     *邀请来源: chat-最近聊天页  customer-客户列表页  fans-粉丝列表页
     * </pre>
     */
    java.lang.String getMsgSource();
    /**
     * <code>optional string msgSource = 4;</code>
     *
     * <pre>
     *邀请来源: chat-最近聊天页  customer-客户列表页  fans-粉丝列表页
     * </pre>
     */
    com.google.protobuf.ByteString
        getMsgSourceBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.live.pp.user.protocol.RequestUserListInviteRoom}
   *
   * <pre>
   * PpUserListInviteRoomService.java
   * 用户列表邀请进房
   * domain = 650, op = 1500
   * </pre>
   */
  public static final class RequestUserListInviteRoom extends
      com.google.protobuf.GeneratedMessage
      implements RequestUserListInviteRoomOrBuilder {
    // Use RequestUserListInviteRoom.newBuilder() to construct.
    private RequestUserListInviteRoom(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestUserListInviteRoom(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestUserListInviteRoom defaultInstance;
    public static RequestUserListInviteRoom getDefaultInstance() {
      return defaultInstance;
    }

    public RequestUserListInviteRoom getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestUserListInviteRoom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              targetUserId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              liveId_ = input.readInt64();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              msgSource_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.internal_static_fm_lizhi_live_pp_user_protocol_RequestUserListInviteRoom_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.internal_static_fm_lizhi_live_pp_user_protocol_RequestUserListInviteRoom_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom.class, fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestUserListInviteRoom> PARSER =
        new com.google.protobuf.AbstractParser<RequestUserListInviteRoom>() {
      public RequestUserListInviteRoom parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestUserListInviteRoom(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestUserListInviteRoom> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户id
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户id
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    // optional int64 targetUserId = 2;
    public static final int TARGETUSERID_FIELD_NUMBER = 2;
    private long targetUserId_;
    /**
     * <code>optional int64 targetUserId = 2;</code>
     *
     * <pre>
     *目标用户id
     * </pre>
     */
    public boolean hasTargetUserId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 targetUserId = 2;</code>
     *
     * <pre>
     *目标用户id
     * </pre>
     */
    public long getTargetUserId() {
      return targetUserId_;
    }

    // optional int64 liveId = 3;
    public static final int LIVEID_FIELD_NUMBER = 3;
    private long liveId_;
    /**
     * <code>optional int64 liveId = 3;</code>
     *
     * <pre>
     *直播间ID
     * </pre>
     */
    public boolean hasLiveId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int64 liveId = 3;</code>
     *
     * <pre>
     *直播间ID
     * </pre>
     */
    public long getLiveId() {
      return liveId_;
    }

    // optional string msgSource = 4;
    public static final int MSGSOURCE_FIELD_NUMBER = 4;
    private java.lang.Object msgSource_;
    /**
     * <code>optional string msgSource = 4;</code>
     *
     * <pre>
     *邀请来源: chat-最近聊天页  customer-客户列表页  fans-粉丝列表页
     * </pre>
     */
    public boolean hasMsgSource() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional string msgSource = 4;</code>
     *
     * <pre>
     *邀请来源: chat-最近聊天页  customer-客户列表页  fans-粉丝列表页
     * </pre>
     */
    public java.lang.String getMsgSource() {
      java.lang.Object ref = msgSource_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          msgSource_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string msgSource = 4;</code>
     *
     * <pre>
     *邀请来源: chat-最近聊天页  customer-客户列表页  fans-粉丝列表页
     * </pre>
     */
    public com.google.protobuf.ByteString
        getMsgSourceBytes() {
      java.lang.Object ref = msgSource_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msgSource_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      userId_ = 0L;
      targetUserId_ = 0L;
      liveId_ = 0L;
      msgSource_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, targetUserId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(3, liveId_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getMsgSourceBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, targetUserId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, liveId_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getMsgSourceBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.live.pp.user.protocol.RequestUserListInviteRoom}
     *
     * <pre>
     * PpUserListInviteRoomService.java
     * 用户列表邀请进房
     * domain = 650, op = 1500
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoomOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.internal_static_fm_lizhi_live_pp_user_protocol_RequestUserListInviteRoom_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.internal_static_fm_lizhi_live_pp_user_protocol_RequestUserListInviteRoom_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom.class, fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom.Builder.class);
      }

      // Construct using fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        targetUserId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        liveId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        msgSource_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.internal_static_fm_lizhi_live_pp_user_protocol_RequestUserListInviteRoom_descriptor;
      }

      public fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom getDefaultInstanceForType() {
        return fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom.getDefaultInstance();
      }

      public fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom build() {
        fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom buildPartial() {
        fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom result = new fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.targetUserId_ = targetUserId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.liveId_ = liveId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.msgSource_ = msgSource_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom) {
          return mergeFrom((fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom other) {
        if (other == fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasTargetUserId()) {
          setTargetUserId(other.getTargetUserId());
        }
        if (other.hasLiveId()) {
          setLiveId(other.getLiveId());
        }
        if (other.hasMsgSource()) {
          bitField0_ |= 0x00000008;
          msgSource_ = other.msgSource_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.RequestUserListInviteRoom) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户id
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户id
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户id
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户id
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 targetUserId = 2;
      private long targetUserId_ ;
      /**
       * <code>optional int64 targetUserId = 2;</code>
       *
       * <pre>
       *目标用户id
       * </pre>
       */
      public boolean hasTargetUserId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 targetUserId = 2;</code>
       *
       * <pre>
       *目标用户id
       * </pre>
       */
      public long getTargetUserId() {
        return targetUserId_;
      }
      /**
       * <code>optional int64 targetUserId = 2;</code>
       *
       * <pre>
       *目标用户id
       * </pre>
       */
      public Builder setTargetUserId(long value) {
        bitField0_ |= 0x00000002;
        targetUserId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 targetUserId = 2;</code>
       *
       * <pre>
       *目标用户id
       * </pre>
       */
      public Builder clearTargetUserId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        targetUserId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 liveId = 3;
      private long liveId_ ;
      /**
       * <code>optional int64 liveId = 3;</code>
       *
       * <pre>
       *直播间ID
       * </pre>
       */
      public boolean hasLiveId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int64 liveId = 3;</code>
       *
       * <pre>
       *直播间ID
       * </pre>
       */
      public long getLiveId() {
        return liveId_;
      }
      /**
       * <code>optional int64 liveId = 3;</code>
       *
       * <pre>
       *直播间ID
       * </pre>
       */
      public Builder setLiveId(long value) {
        bitField0_ |= 0x00000004;
        liveId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 liveId = 3;</code>
       *
       * <pre>
       *直播间ID
       * </pre>
       */
      public Builder clearLiveId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        liveId_ = 0L;
        onChanged();
        return this;
      }

      // optional string msgSource = 4;
      private java.lang.Object msgSource_ = "";
      /**
       * <code>optional string msgSource = 4;</code>
       *
       * <pre>
       *邀请来源: chat-最近聊天页  customer-客户列表页  fans-粉丝列表页
       * </pre>
       */
      public boolean hasMsgSource() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string msgSource = 4;</code>
       *
       * <pre>
       *邀请来源: chat-最近聊天页  customer-客户列表页  fans-粉丝列表页
       * </pre>
       */
      public java.lang.String getMsgSource() {
        java.lang.Object ref = msgSource_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          msgSource_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string msgSource = 4;</code>
       *
       * <pre>
       *邀请来源: chat-最近聊天页  customer-客户列表页  fans-粉丝列表页
       * </pre>
       */
      public com.google.protobuf.ByteString
          getMsgSourceBytes() {
        java.lang.Object ref = msgSource_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          msgSource_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string msgSource = 4;</code>
       *
       * <pre>
       *邀请来源: chat-最近聊天页  customer-客户列表页  fans-粉丝列表页
       * </pre>
       */
      public Builder setMsgSource(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        msgSource_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string msgSource = 4;</code>
       *
       * <pre>
       *邀请来源: chat-最近聊天页  customer-客户列表页  fans-粉丝列表页
       * </pre>
       */
      public Builder clearMsgSource() {
        bitField0_ = (bitField0_ & ~0x00000008);
        msgSource_ = getDefaultInstance().getMsgSource();
        onChanged();
        return this;
      }
      /**
       * <code>optional string msgSource = 4;</code>
       *
       * <pre>
       *邀请来源: chat-最近聊天页  customer-客户列表页  fans-粉丝列表页
       * </pre>
       */
      public Builder setMsgSourceBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        msgSource_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.live.pp.user.protocol.RequestUserListInviteRoom)
    }

    static {
      defaultInstance = new RequestUserListInviteRoom(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.live.pp.user.protocol.RequestUserListInviteRoom)
  }

  public interface ResponseUserListInviteRoomOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code fm.lizhi.live.pp.user.protocol.ResponseUserListInviteRoom}
   *
   * <pre>
   * rCode == 0 (SUCCESS) == 成功
   * rCode == -1 (FAIL) == 失败
   * </pre>
   */
  public static final class ResponseUserListInviteRoom extends
      com.google.protobuf.GeneratedMessage
      implements ResponseUserListInviteRoomOrBuilder {
    // Use ResponseUserListInviteRoom.newBuilder() to construct.
    private ResponseUserListInviteRoom(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseUserListInviteRoom(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseUserListInviteRoom defaultInstance;
    public static ResponseUserListInviteRoom getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseUserListInviteRoom getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseUserListInviteRoom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.internal_static_fm_lizhi_live_pp_user_protocol_ResponseUserListInviteRoom_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.internal_static_fm_lizhi_live_pp_user_protocol_ResponseUserListInviteRoom_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom.class, fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseUserListInviteRoom> PARSER =
        new com.google.protobuf.AbstractParser<ResponseUserListInviteRoom>() {
      public ResponseUserListInviteRoom parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseUserListInviteRoom(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseUserListInviteRoom> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.live.pp.user.protocol.ResponseUserListInviteRoom}
     *
     * <pre>
     * rCode == 0 (SUCCESS) == 成功
     * rCode == -1 (FAIL) == 失败
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoomOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.internal_static_fm_lizhi_live_pp_user_protocol_ResponseUserListInviteRoom_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.internal_static_fm_lizhi_live_pp_user_protocol_ResponseUserListInviteRoom_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom.class, fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom.Builder.class);
      }

      // Construct using fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.internal_static_fm_lizhi_live_pp_user_protocol_ResponseUserListInviteRoom_descriptor;
      }

      public fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom getDefaultInstanceForType() {
        return fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom.getDefaultInstance();
      }

      public fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom build() {
        fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom buildPartial() {
        fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom result = new fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom) {
          return mergeFrom((fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom other) {
        if (other == fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.live.pp.user.protocol.PpUserListInviteRoomProto.ResponseUserListInviteRoom) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.live.pp.user.protocol.ResponseUserListInviteRoom)
    }

    static {
      defaultInstance = new ResponseUserListInviteRoom(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.live.pp.user.protocol.ResponseUserListInviteRoom)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_live_pp_user_protocol_RequestUserListInviteRoom_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_live_pp_user_protocol_RequestUserListInviteRoom_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_live_pp_user_protocol_ResponseUserListInviteRoom_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_live_pp_user_protocol_ResponseUserListInviteRoom_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\033user_list_invite_room.proto\022\036fm.lizhi." +
      "live.pp.user.protocol\"d\n\031RequestUserList" +
      "InviteRoom\022\016\n\006userId\030\001 \001(\003\022\024\n\014targetUser" +
      "Id\030\002 \001(\003\022\016\n\006liveId\030\003 \001(\003\022\021\n\tmsgSource\030\004 " +
      "\001(\t\"\034\n\032ResponseUserListInviteRoomB;\n\036fm." +
      "lizhi.live.pp.user.protocolB\031PpUserListI" +
      "nviteRoomProto"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_live_pp_user_protocol_RequestUserListInviteRoom_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_live_pp_user_protocol_RequestUserListInviteRoom_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_live_pp_user_protocol_RequestUserListInviteRoom_descriptor,
              new java.lang.String[] { "UserId", "TargetUserId", "LiveId", "MsgSource", });
          internal_static_fm_lizhi_live_pp_user_protocol_ResponseUserListInviteRoom_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_live_pp_user_protocol_ResponseUserListInviteRoom_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_live_pp_user_protocol_ResponseUserListInviteRoom_descriptor,
              new java.lang.String[] { });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
