package fm.lizhi.ocean.wavecenter.infrastructure.permissions.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface WcRoleAuthRefExtMapper {

    @Update({
            "<script>"
            , "update wavecenter_role_auth_ref set deleted=null,status=0,modify_time=now() where family_id=#{familyId} and app_id=#{appId} and user_id in "
            , "<foreach collection='userIds' item='uId' open='(' separator=',' close=')'>"
            , "#{uId}"
            , "</foreach>"
            , "</script>"
    })
    int deleteByFamilyAndUserIds(@Param("familyId") Long familyId
            , @Param("appId") int appId
            , @Param("userIds") List<Long> userIds);


    @Select("<script>" +
            "select DISTINCT(user_id) " +
            "FROM wavecenter_role_auth_ref " +
            "WHERE app_id = #{appId} AND `status` = 1 AND `deleted` = 0 AND  subject_id = #{subjectId} AND role_code = #{roleCode}" +
            "</script>")
    List<Long> getRoomRoleAuthRefList(@Param("appId") int appId, @Param("subjectId") long subjectId, @Param("roleCode") String roleCode);


}
