package fm.lizhi.ocean.wavecenter.infrastructure.sign.datastore.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 签约流程表
 *
 * @date 2024-10-28 02:28:19
 */
@Table(name = "`wavecenter_sign_flow`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcSignFlow {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 签约ID,合同ID或者签约记录ID
     */
    @Column(name= "`contract_id`")
    private Long contractId;

    /**
     * 状态：RUNNING进行中, CLOSED已关闭
     */
    @Column(name= "`status`")
    private String status;

    /**
     * 类型：SIGN签约，CANCEL解约
     */
    @Column(name= "`type`")
    private String type;

    /**
     * 是否有电子合同：1=有，0=没有
     */
    @Column(name= "`has_contract`")
    private Integer hasContract;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", contractId=").append(contractId);
        sb.append(", status=").append(status);
        sb.append(", type=").append(type);
        sb.append(", hasContract=").append(hasContract);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}