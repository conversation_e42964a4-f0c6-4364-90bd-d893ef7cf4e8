package fm.lizhi.ocean.wavecenter.infrastructure.award.singer.manager;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerStatusEnum;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.constants.SingerTypeEnum;
import fm.lizhi.ocean.wavecenter.api.award.singer.bean.SingerDecorateRuleBean;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestPageSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestSaveSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.api.award.singer.request.RequestUpdateSingerDecorateRule;
import fm.lizhi.ocean.wavecenter.common.dto.PageDto;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.convert.SingerDecorateConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.datastore.dao.SingerDecorateDao;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.datastore.entity.SingerDecorateRule;
import fm.lizhi.ocean.wavecenter.infrastructure.award.singer.datastore.mapper.SingerDecorateRuleMapper;
import fm.lizhi.ocean.wavecenter.service.award.singer.manager.SingerDecorateRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 *  歌手装扮规则配置
 * <AUTHOR>
 */
@Component
@Slf4j
public class SingerDecorateRuleManagerImpl implements SingerDecorateRuleManager {

    @Autowired
    private SingerDecorateDao singerDecorateDao;

    @Autowired
    private SingerDecorateRuleMapper singerDecorateRuleMapper;

    @Override
    public PageDto<SingerDecorateRuleBean> pageSingerDecorateRule(RequestPageSingerDecorateRule request) {
        PageList<SingerDecorateRule> pageList = singerDecorateDao.pageSingerDecorateRule(request.getAppId(), request.getPageNo(), request.getPageSize());
        if (CollUtil.isEmpty(pageList)){
            return PageDto.empty();
        }

        return PageDto.of(pageList.getTotal(), SingerDecorateConvert.I.convertSingerDecorateRuleBeanList(pageList));
    }

    @Override
    public Boolean saveSingerDecorateRule(RequestSaveSingerDecorateRule request) {
        SingerDecorateRule singerDecorateRule = SingerDecorateConvert.I.buildSingerDecorateRule(request);
        return singerDecorateRuleMapper.insert(singerDecorateRule) > 0;
    }

    @Override
    public Boolean updateSingerDecorateRule(RequestUpdateSingerDecorateRule request) {

        SingerDecorateRule rule = singerDecorateRuleMapper.selectByPrimaryKey(SingerDecorateRule.builder().id(request.getId()).build());
        if (rule == null) {
            log.warn("rule not found, id:{}", request.getId());
            return false;
        }

        SingerDecorateRule updateRule = SingerDecorateConvert.I.buildSingerDecorateRule(request);
        return singerDecorateRuleMapper.updateByPrimaryKey(updateRule) > 0;
    }

    @Override
    public List<SingerDecorateRuleBean> getSingerDecorateRule(int appId, SingerTypeEnum singerType, String songStyle) {
        List<SingerDecorateRule> list = singerDecorateDao.getSingerDecorateRule(appId, singerType, songStyle);
        return SingerDecorateConvert.I.convertSingerDecorateRuleBeanList(list);
    }
}
