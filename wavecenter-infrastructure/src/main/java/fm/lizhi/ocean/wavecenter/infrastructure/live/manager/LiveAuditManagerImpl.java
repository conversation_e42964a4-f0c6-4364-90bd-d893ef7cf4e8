package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.infrastructure.live.convert.LiveAuditConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.entity.AuditRecord;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.entity.WcAuditRecordFull;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.entity.WcAuditRecordFullExample;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.AuditRecordMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WcAuditRecordFullMapper;
import fm.lizhi.ocean.wavecenter.service.live.dto.GuildAuditRecordDto;
import fm.lizhi.ocean.wavecenter.service.live.dto.RoomAuditRecordDto;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveAuditManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;


@Slf4j
@Component
public class LiveAuditManagerImpl implements LiveAuditManager {


    @Autowired
    private AuditRecordMapper auditRecordMapper;
    @Autowired
    private WcAuditRecordFullMapper auditRecordFullMapper;


    @Override
    public PageBean<AuditRecordBean> getAuditRecordDetail(AuditRecordSearchParamBean paramBean, int page, int pageSize) {
        PageList<WcAuditRecordFull> pageList = auditRecordMapper.selectAuditRecordFull(paramBean, page, pageSize);
        return PageBean.of(pageList.getTotal(), LiveAuditConvert.I.fullEntitys2AuditRecordBeans(pageList));
    }

    @Override
    public boolean addAuditRecord(AddAuditRecordBean recordBean) {
        AuditRecord auditRecord = LiveAuditConvert.I.AddAuditRecordBeanTo2Bean(recordBean);
        int insert = auditRecordMapper.insert(auditRecord);
        log.info("addAuditRecord recordBean={},insert={}", JSONObject.toJSONString(recordBean), insert);
        return insert > 0;
    }


    @Override
    public PageBean<GuildAuditRecordDto> guildAuditRecordStats(GuildAuditStatsParamBean paramVo, int page, int pageSize) {
        String orderFile = "pushNumber";
        String orderType = "desc";
        if (paramVo.getOrderType() != null) {
            orderType = paramVo.getOrderType().getValue();
        }
        if (paramVo.getOrderMetrics() != null) {
            orderFile = paramVo.getOrderMetrics().getPoName();
        }
        PageList<GuildAuditRecordDto> pageList = auditRecordMapper.pageAuditRecordStats(paramVo, orderFile + " " + orderType, page, pageSize);
        return PageBean.of(pageList.getTotal(), pageList);
    }


    @Override
    public RoomAuditRecordDto roomAuditRecordStats(RoomAuditStatsParamBean paramVo) {
        return auditRecordMapper.roomAuditRecordStats(paramVo);
    }

    @Override
    public PageBean<Long> signRoomPushPlayer(RoomPushParamBean paramBean, int page, int pageSize) {
        PageList<Long> userIds = auditRecordMapper.roomAuditPlayer(paramBean, page, pageSize);
        return PageBean.of(userIds.getTotal(), userIds);
    }

    @Override
    public boolean addAuditRecordFull(AddAuditRecordFullParamBean recordFullBean) {
        WcAuditRecordFull entity = LiveAuditConvert.I.fullBean2Entity(recordFullBean);
        entity.setCreateTime(new Date());
        entity.setModifyTime(new Date());
        return auditRecordFullMapper.insert(entity) > 0;
    }

    @Override
    public boolean existAuditRecord(String recordId) {
        WcAuditRecordFullExample example = new WcAuditRecordFullExample();
        example.createCriteria().andRecordIdEqualTo(recordId);
        long count = auditRecordFullMapper.countByExample(example);
        return count > 0;
    }

    @Override
    public boolean updateAuditContentUrl(String recordId, String sourceContentUrl, String targetContentUrl) {
        WcAuditRecordFull recordFull = new WcAuditRecordFull();
        recordFull.setSourceContentUrl(sourceContentUrl);
        recordFull.setPublicContentUrl(targetContentUrl);
        recordFull.setRecordId(recordId);

        WcAuditRecordFullExample example = new WcAuditRecordFullExample();
        example.createCriteria().andRecordIdEqualTo(recordId);
        return auditRecordFullMapper.updateByExample(recordFull, example) > 0;
    }
}
