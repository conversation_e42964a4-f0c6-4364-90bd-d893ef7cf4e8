package fm.lizhi.ocean.wavecenter.infrastructure.datacenter.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.CountDataBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.PlayerPerformanceBean;
import fm.lizhi.ocean.wavecenter.api.datacenter.bean.RoomAssessmentInfoBean;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.common.MetricsUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.constants.MetricsMeta;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.convert.DataCenterInfraConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.*;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.mapper.ext.WcDataPayRoomDayExtMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.datastore.po.PlayerSignPerformancePo;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.ICharmStatRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.datacenter.remote.IRoomDataRemote;
import fm.lizhi.ocean.wavecenter.infrastructure.live.manager.CheckInRedisManagerImpl;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RoomDataManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/18 15:59
 */
@Component
@Slf4j
public class RoomDataManagerImpl implements RoomDataManager {

    @Autowired
    private ICharmStatRemote iCharmStatRemote;
    @Autowired
    private WcDataRoomFamilyDayMapper wcDataRoomFamilyDayMapper;
    @Autowired
    private WcDataRoomDayMapper roomDayMapper;
    @Autowired
    private WcDataRoomFamilyWeekMapper wcDataRoomFamilyWeekMapper;
    @Autowired
    private WcDataRoomWeekMapper roomWeekMapper;
    @Autowired
    private WcDataRoomFamilyMonthMapper wcDataRoomFamilyMonthMapper;
    @Autowired
    private WcDataRoomMonthMapper roomMonthMapper;
    @Autowired
    private IRoomDataRemote iRoomDataRemote;
    @Autowired
    private WcDataRoomFamilyDayExtMapper roomFamilyDayExtMapper;
    @Autowired
    private WcDataRoomFamilyWeekExtMapper roomFamilyWeekExtMapper;
    @Autowired
    private WcDataRoomFamilyMonthExtMapper roomFamilyMonthExtMapper;
    @Autowired
    private WcDataPayRoomDayExtMapper wcDataPayRoomDayExtMapper;
    @Autowired
    private CheckInRedisManagerImpl checkInRedisManager;

    @Override
    public Optional<RoomAssessmentInfoBean> getAssessmentInfo(int appId, long familyId, long roomId) {
        RoomAssessmentInfoBean roomAssessmentInfoBean = iRoomDataRemote.queryAssessment(familyId, roomId);
        roomAssessmentInfoBean.setFlushTime(String.valueOf(System.currentTimeMillis()));
        return Optional.of(roomAssessmentInfoBean);
    }

    @Override
    public PageBean<PlayerPerformanceBean> getPlayerPerformance(GetRoomPlayerPerformanceParamDto paramDto) {
        PageList<PlayerSignPerformancePo> playerSignPerformancePos = iRoomDataRemote.selectPlayerSignPerformance(paramDto);
        return PageBean.of(playerSignPerformancePos.getTotal(), DataCenterInfraConvert.I.playerSignPerformancePos2Beans(playerSignPerformancePos));
    }

    @Override
    public List<PlayerPerformanceBean> getPlayerPerformanceList(int appId, long familyId, long roomId, List<Long> playerId, AssessTimeDto assessTimeDto) {
        List<PlayerSignCharmSumPo> list = iCharmStatRemote.selectPlayerSignCharmSumByUsers(roomId, familyId, playerId, assessTimeDto.getStartDate(), assessTimeDto.getEndDate());
        return DataCenterInfraConvert.I.playerSignCharmSumPos2Beans(list);
    }

    @Override
    public Map<String, String> getRoomDayKeyIndicators(int appId, Long familyId, long roomId, Date date, List<String> valueMetrics) {
        if (familyId != null) {
            WcDataRoomFamilyDay param = new WcDataRoomFamilyDay();
            param.setAppId(appId);
            param.setRoomId(roomId);
            param.setFamilyId(familyId);
            param.setStatDateValue(Integer.valueOf(DateUtil.formatDateToString(date, DateUtil.date)));

            log.info("getRoomDayKeyIndicators selectMany. param={}", JsonUtil.dumps(param));
            List<WcDataRoomFamilyDay> wcDataRoomDays = wcDataRoomFamilyDayMapper.selectMany(param);
            Map<String, String> baseValueMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(wcDataRoomDays)) {
                WcDataRoomFamilyDay wcDataRoomDay = wcDataRoomDays.get(0);
                baseValueMap = MetricsUtil.convertToMap(WcDataRoomFamilyDay.class, wcDataRoomDay);
            }

            return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
        }

        WcDataRoomDay param = new WcDataRoomDay();
        param.setAppId(appId);
        param.setRoomId(roomId);
        param.setStatDateValue(Integer.valueOf(DateUtil.formatDateToString(date, DateUtil.date)));

        log.info("roomDayMapper selectMany. param={}", JsonUtil.dumps(param));
        List<WcDataRoomDay> wcDataRoomDays = roomDayMapper.selectMany(param);
        Map<String, String> baseValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(wcDataRoomDays)) {
            WcDataRoomDay wcDataRoomDay = wcDataRoomDays.get(0);
            baseValueMap = MetricsUtil.convertToMap(WcDataRoomDay.class, wcDataRoomDay);
        }
        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public Map<String, String> getRoomDayKeyIndicatorsSum(int appId, Long familyId, List<Long> roomIds, Date date, List<String> valueMetrics) {
        //指标持久化字段名称
        List<String> poMetricsNames = new ArrayList<>();
        for (String valueMetric : valueMetrics) {
            Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(valueMetric);
            metricsMeta.ifPresent(meta -> poMetricsNames.add(meta.getPoName()));
        }
        if (CollectionUtils.isEmpty(poMetricsNames)) {
            return Collections.emptyMap();
        }
        LogContext.addResLog("poMetricsNames={}", JsonUtil.dumps(poMetricsNames));
        List<WcDataRoomFamilyDay> sumList = roomFamilyDayExtMapper.sum(poMetricsNames, appId, familyId, roomIds, MyDateUtil.getDateDayValue(date));
        if (CollectionUtils.isEmpty(sumList)) {
            return Collections.emptyMap();
        }
        WcDataRoomFamilyDay sum = sumList.get(0);
        LogContext.addResLog("sum={}", JsonUtil.dumps(sum));
        Map<String, String> baseValueMap = MetricsUtil.convertToMap(WcDataRoomFamilyDay.class, sum);
        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public List<DataRoomFamilyDayDTO> getRoomDayKeyIndicatorsSum(Long familyId, List<Long> roomIds, List<Integer> dayValues, List<String> valueMetrics) {
        //指标持久化字段名称
        List<String> poMetricsNames = new ArrayList<>();
        for (String valueMetric : valueMetrics) {
            Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(valueMetric);
            metricsMeta.ifPresent(meta -> poMetricsNames.add(meta.getPoName()));
        }
        if (CollectionUtils.isEmpty(poMetricsNames)) {
            return Collections.emptyList();
        }
        LogContext.addResLog("poMetricsNames={}", JsonUtil.dumps(poMetricsNames));

        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        List<WcDataRoomFamilyDay> poList = roomFamilyDayExtMapper.sumDays(poMetricsNames, appId, familyId, roomIds, dayValues);

        return DataCenterInfraConvert.I.roomFamilyDay2DTO(poList);
    }

    @Override
    public Map<String, String> getRoomWeekKeyIndicators(int appId, Long familyId, long roomId, Date startDate, Date endDate, List<String> valueMetrics) {
        if (familyId != null) {
            WcDataRoomFamilyWeek param = new WcDataRoomFamilyWeek();
            param.setAppId(appId);
            param.setRoomId(roomId);
            param.setFamilyId(familyId);
            param.setStartWeekDate(startDate);
            param.setEndWeekDate(endDate);

            log.info("getRoomWeekKeyIndicators selectMany. param={}", JsonUtil.dumps(param));
            List<WcDataRoomFamilyWeek> list = wcDataRoomFamilyWeekMapper.selectMany(param);
            Map<String, String> baseValueMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(list)) {
                WcDataRoomFamilyWeek wcDataRoomWeek = list.get(0);
                baseValueMap = MetricsUtil.convertToMap(WcDataRoomFamilyWeek.class, wcDataRoomWeek);
            }

            return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
        }

        WcDataRoomWeek param = new WcDataRoomWeek();
        param.setAppId(appId);
        param.setRoomId(roomId);
        param.setStartWeekDate(startDate);
        param.setEndWeekDate(endDate);

        log.info("roomWeekMapper selectMany. param={}", JsonUtil.dumps(param));
        List<WcDataRoomWeek> list = roomWeekMapper.selectMany(param);
        Map<String, String> baseValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            WcDataRoomWeek wcDataRoomWeek = list.get(0);
            baseValueMap = MetricsUtil.convertToMap(WcDataRoomWeek.class, wcDataRoomWeek);
        }

        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);

    }

    @Override
    public Map<String, String> getRoomWeekKeyIndicatorsSum(Long familyId, List<Long> roomIds, Date startDate, Date endDate, List<String> valueMetrics) {
        //指标持久化字段名称
        List<String> poMetricsNames = new ArrayList<>();
        for (String valueMetric : valueMetrics) {
            Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(valueMetric);
            metricsMeta.ifPresent(meta -> poMetricsNames.add(meta.getPoName()));
        }
        if (CollectionUtils.isEmpty(poMetricsNames)) {
            return Collections.emptyMap();
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        LogContext.addResLog("poMetricsNames={}", JsonUtil.dumps(poMetricsNames));
        List<WcDataRoomFamilyWeek> sumList = roomFamilyWeekExtMapper.sum(poMetricsNames
                , appId
                , familyId, roomIds
                , DateUtil.formatDateToString(startDate, DateUtil.date_2)
                , DateUtil.formatDateToString(endDate, DateUtil.date_2)
        );
        if (CollectionUtils.isEmpty(sumList)) {
            return Collections.emptyMap();
        }
        WcDataRoomFamilyWeek sum = sumList.get(0);
        LogContext.addResLog("sum={}", JsonUtil.dumps(sum));
        Map<String, String> baseValueMap = MetricsUtil.convertToMap(WcDataRoomFamilyWeek.class, sum);
        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public Map<String, String> getRoomMonthKeyIndicatorsSum(Long familyId, List<Long> roomIds, Date monthDate, List<String> valueMetrics) {
        //指标持久化字段名称
        List<String> poMetricsNames = new ArrayList<>();
        for (String valueMetric : valueMetrics) {
            Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(valueMetric);
            metricsMeta.ifPresent(meta -> poMetricsNames.add(meta.getPoName()));
        }
        if (CollectionUtils.isEmpty(poMetricsNames)) {
            return Collections.emptyMap();
        }
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();

        LogContext.addResLog("poMetricsNames={}", JsonUtil.dumps(poMetricsNames));
        List<WcDataRoomFamilyMonth> sumList = roomFamilyMonthExtMapper.sum(poMetricsNames
                , appId
                , familyId, roomIds
                , MyDateUtil.getDateMonthValue(monthDate)
        );
        if (CollectionUtils.isEmpty(sumList)) {
            return Collections.emptyMap();
        }
        WcDataRoomFamilyMonth sum = sumList.get(0);
        LogContext.addResLog("sum={}", JsonUtil.dumps(sum));
        Map<String, String> baseValueMap = MetricsUtil.convertToMap(WcDataRoomFamilyMonth.class, sum);
        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public Map<String, String> getRoomMonthKeyIndicators(int appId, Long familyId, long roomId, Date monthDate, List<String> valueMetrics) {
        if (familyId != null) {
            WcDataRoomFamilyMonth param = new WcDataRoomFamilyMonth();
            param.setAppId(appId);
            param.setRoomId(roomId);
            param.setFamilyId(familyId);
            param.setStatMonth(Integer.valueOf(DateUtil.formatDateToString(monthDate, "yyyyMM")));

            log.info("getRoomMonthKeyIndicators selectMany. param={}", JsonUtil.dumps(param));
            List<WcDataRoomFamilyMonth> list = wcDataRoomFamilyMonthMapper.selectMany(param);
            Map<String, String> baseValueMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(list)) {
                WcDataRoomFamilyMonth wcDataRoomMonth = list.get(0);
                baseValueMap = MetricsUtil.convertToMap(WcDataRoomFamilyMonth.class, wcDataRoomMonth);
            }

            return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
        }

        WcDataRoomMonth param = new WcDataRoomMonth();
        param.setAppId(appId);
        param.setRoomId(roomId);
        param.setStatMonth(Integer.valueOf(DateUtil.formatDateToString(monthDate, "yyyyMM")));

        log.info("roomMonthMapper selectMany. param={}", JsonUtil.dumps(param));
        List<WcDataRoomMonth> list = roomMonthMapper.selectMany(param);
        Map<String, String> baseValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            WcDataRoomMonth wcDataRoomMonth = list.get(0);
            baseValueMap = MetricsUtil.convertToMap(WcDataRoomMonth.class, wcDataRoomMonth);
        }
        return MetricsUtil.convertToResultMap(valueMetrics, baseValueMap);
    }

    @Override
    public List<CountDataBean> getIndicatorTrend(Long familyId, long roomId, String metric, int days) {
        List<Integer> dates = MyDateUtil.getDateDayValueList(days);

        Map<Integer, Object> valueMap = new HashMap<>();

        if (familyId != null) {
            WcDataRoomFamilyDayExample example = new WcDataRoomFamilyDayExample();
            example.createCriteria()
                    .andRoomIdEqualTo(roomId)
                    .andFamilyIdEqualTo(familyId)
                    .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
                    .andStatDateValueIn(dates);
            List<WcDataRoomFamilyDay> list = wcDataRoomFamilyDayMapper.selectByExample(example);
            valueMap = list.stream().collect(Collectors.toMap(WcDataRoomFamilyDay::getStatDateValue, v -> v, (k1, k2) -> k2));
        } else {
            WcDataRoomDayExample example = new WcDataRoomDayExample();
            example.createCriteria()
                    .andRoomIdEqualTo(roomId)
                    .andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId())
                    .andStatDateValueIn(dates);
            List<WcDataRoomDay> list = roomDayMapper.selectByExample(example);
            valueMap = list.stream().collect(Collectors.toMap(WcDataRoomDay::getStatDateValue, v -> v, (k1, k2) -> k2));
        }

        List<CountDataBean> result = new ArrayList<>(days);
        for (Integer date : dates) {
            CountDataBean countDataBean = new CountDataBean();
            countDataBean.setDate(MyDateUtil.getDayValueDate(date));
            Object wcDataDay = valueMap.get(date);
            if (wcDataDay != null) {
                ReflectionUtils.doWithLocalFields(wcDataDay.getClass(), field -> {
                    String name = field.getName();
                    if (!name.equals(metric)) {
                        return;
                    }

                    Optional<MetricsMeta> metricsMeta = MetricsConstants.getMetricsMeta(name);
                    if (!metricsMeta.isPresent()) {
                        return;
                    }

                    ReflectionUtils.makeAccessible(field);
                    Object value = ReflectionUtils.getField(field, wcDataDay);

                    MetricsMeta.ValueFactory valueFactory = metricsMeta.get().getValueFactory();
                    String formatValue = valueFactory.calculateValue(metricsMeta.get(), name, String.valueOf(value));
                    countDataBean.setValue(formatValue);
                });
            }
            result.add(countDataBean);
        }

        return result;
    }

    @Override
    public List<DataRoomFamilyDayDTO> getRoomDayData(Integer appId, Long familyId, Date startDate, Date endDate) {
        return getRoomDayData(appId, familyId, null, startDate, endDate);
    }


    @Override
    public List<DataRoomFamilyDayDTO> getRoomDayData(Integer appId, Long familyId, Long roomId, Date startDate, Date endDate) {

        WcDataRoomFamilyDayExample example = new WcDataRoomFamilyDayExample();
        WcDataRoomFamilyDayExample.Criteria criteria = example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andAppIdEqualTo(appId)
                .andStatDateValueBetween(MyDateUtil.getDateDayValue(startDate), MyDateUtil.getDateDayValue(endDate));

        if (roomId != null){
            criteria.andRoomIdEqualTo(roomId);
        }

        List<WcDataRoomFamilyDay> roomFamilyDayList = wcDataRoomFamilyDayMapper.selectByExample(example);
        return DataCenterInfraConvert.I.roomFamilyDay2DTO(roomFamilyDayList);
    }

    @Override
    public List<DataRoomFamilyDayDTO> getRoomDayData(Long familyId, List<Long> roomIds, Date day) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcDataRoomFamilyDayExample example = new WcDataRoomFamilyDayExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andAppIdEqualTo(appId)
                .andRoomIdIn(roomIds)
                .andStatDateValueEqualTo(MyDateUtil.getDateDayValue(day));
        List<WcDataRoomFamilyDay> roomFamilyDayList = wcDataRoomFamilyDayMapper.selectByExample(example);
        return DataCenterInfraConvert.I.roomFamilyDay2DTO(roomFamilyDayList);
    }

    @Override
    public List<DataRoomFamilyWeekDTO> getRoomWeekData(Long familyId, List<Long> roomIds, Date startDay, Date endDay) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcDataRoomFamilyWeekExample example = new WcDataRoomFamilyWeekExample();
        example.createCriteria()
                .andFamilyIdEqualTo(familyId)
                .andAppIdEqualTo(appId)
                .andRoomIdIn(roomIds)
                .andStartWeekDateEqualTo(startDay)
                .andEndWeekDateEqualTo(endDay);
        List<WcDataRoomFamilyWeek> roomFamilyWeekList = wcDataRoomFamilyWeekMapper.selectByExample(example);
        return DataCenterInfraConvert.I.roomFamilyWeeks2DTOs(roomFamilyWeekList);
    }

    @Override
    public List<DataRoomFamilyMonthDTO> getRoomMonthData(Long familyId, List<Long> roomIds, Date month) {
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        WcDataRoomFamilyMonthExample example = new WcDataRoomFamilyMonthExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andRoomIdIn(roomIds)
                .andStatMonthEqualTo(MyDateUtil.getDateMonthValue(month));
        List<WcDataRoomFamilyMonth> poList = wcDataRoomFamilyMonthMapper.selectByExample(example);
        return DataCenterInfraConvert.I.roomFamilyMonths2DTOs(poList);
    }

    @Override
    public List<DataRoomFamilyDayDTO> getRoomFamilyDayList(GetRoomDayListParam param) {
        WcDataRoomFamilyDayExample example = new WcDataRoomFamilyDayExample();
        example.setOrderByClause("stat_date_value desc");
        WcDataRoomFamilyDayExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId());
        if (param.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(param.getFamilyId());
        }
        if (param.getRoomId() != null) {
            criteria.andRoomIdEqualTo(param.getRoomId());
        }
        if (CollectionUtils.isNotEmpty(param.getRoomIds())) {
            criteria.andRoomIdIn(param.getRoomIds());
        }
        if (CollectionUtils.isNotEmpty(param.getDayValues())) {
            criteria.andStatDateValueIn(param.getDayValues());
        }
        if (param.getStartDay() != null && param.getEndDay() != null) {
            criteria.andStatDateValueBetween(MyDateUtil.getDateDayValue(param.getStartDay())
                    , MyDateUtil.getDateDayValue(param.getEndDay())
            );
        }

        List<WcDataRoomFamilyDay> poList = wcDataRoomFamilyDayMapper.selectByExample(example);
        return DataCenterInfraConvert.I.roomFamilyDay2DTO(poList);
    }

    @Override
    public List<DataRoomDayDTO> getRoomDayList(GetRoomDayListParam param) {
        WcDataRoomDayExample example = new WcDataRoomDayExample();
        example.setOrderByClause("stat_date_value desc");
        WcDataRoomDayExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(ContextUtils.getBusinessEvnEnum().getAppId());
        if (param.getRoomId() != null) {
            criteria.andRoomIdEqualTo(param.getRoomId());
        }
        if (CollectionUtils.isNotEmpty(param.getDayValues())) {
            criteria.andStatDateValueIn(param.getDayValues());
        }
        if (CollectionUtils.isNotEmpty(param.getRoomIds())) {
            criteria.andRoomIdIn(param.getRoomIds());
        }
        if (param.getStartDay() != null && param.getEndDay() != null) {
            criteria.andStatDateValueBetween(MyDateUtil.getDateDayValue(param.getStartDay())
                    , MyDateUtil.getDateDayValue(param.getEndDay())
            );
        }
        List<WcDataRoomDay> poList = roomDayMapper.selectByExample(example);
        return DataCenterInfraConvert.I.roomDays2DTOs(poList);
    }



    /**
     * 连续天数有总收入的PGC厅ID列表
     */
    @Override
    public List<Long> getHasIncomeRoomIdList(GetHasIncomeRoomsParam param) {
        Integer appId = param.getAppId();
        Integer startDayValue = MyDateUtil.getDateDayValue(param.getStartDay());
        Integer endDayValue = MyDateUtil.getDateDayValue(param.getEndDay());
        String deployEnv = ConfigUtils.getEnvRequired().name();
        List<Long> cache = checkInRedisManager.getHasIncomeRoomIdList(appId, startDayValue, endDayValue);
        if(cache != null) {
            return cache;
        }
        List<Long> hasIncomeRoomIds = wcDataPayRoomDayExtMapper.findHasIncomeRoomIds(appId, startDayValue, endDayValue, deployEnv);
        checkInRedisManager.setHasIncomeRoomIdList(appId, startDayValue, endDayValue, hasIncomeRoomIds);
        return hasIncomeRoomIds;
    }
}
