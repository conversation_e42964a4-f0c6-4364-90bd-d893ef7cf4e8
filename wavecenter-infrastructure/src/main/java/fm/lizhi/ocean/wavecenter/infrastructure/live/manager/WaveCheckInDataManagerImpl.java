package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInPlayerStatistic;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInPlayerSum;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInRoomStatistic;
import fm.lizhi.ocean.wavecenter.api.live.request.RequestGetCheckInRoomSum;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInPlayerStatistic;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInPlayerSum;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInRoomStatistic;
import fm.lizhi.ocean.wavecenter.api.live.response.ResponseGetCheckInRoomSum;
import fm.lizhi.ocean.wavecenter.infrastructure.live.constant.CheckInScheduledConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.live.constant.CheckInStatusConstants;
import fm.lizhi.ocean.wavecenter.infrastructure.live.convert.WaveCheckInDataConverter;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.dao.WaveCheckInDataDao;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.entity.*;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WaveRoomCheckInRecordMapper;
import fm.lizhi.ocean.wavecenter.infrastructure.live.dto.*;
import fm.lizhi.ocean.wavecenter.service.live.manager.WaveCheckInDataManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 麦序福利数据管理器实现
 */
@Component
public class WaveCheckInDataManagerImpl implements WaveCheckInDataManager {

    private static final List<StatisticPeriodHandler> STATISTIC_PERIOD_HANDLERS = Arrays.asList(
            new DayStatisticPeriodHandler(),
            new WeekStatisticPeriodHandler()
    );

    private static final DateTimeFormatter PLAYER_DATE_FORMATTER = DateTimeFormatter.ofPattern("MM/dd");

    @Autowired
    private UserManager userManager;

    @Autowired
    private WaveCheckInDataConverter waveCheckInDataConverter;

    @Autowired
    private WaveCheckInDataDao waveCheckInDataDao;

    @Autowired
    private WaveRoomCheckInRecordMapper waveRoomCheckInRecordMapper;

    @Override
    public ResponseGetCheckInRoomSum getCheckInRoomSum(RequestGetCheckInRoomSum req) {
        Long roomId = req.getRoomId();
        Long startDate = req.getStartDate();
        Long endDate = req.getEndDate();
        Long familyId = req.getFamilyId();
        List<WaveCheckInSchedule> schedules = waveCheckInDataDao.getCheckInRoomSchedules(roomId, startDate, endDate, familyId);
        List<Long> scheduleIds = schedules.stream().map(WaveCheckInSchedule::getId).collect(Collectors.toList());
        WaveCheckInIncomeSumDTO incomeSumDTO = waveCheckInDataDao.getCheckInIncomeSum(scheduleIds);
        int scheduledPlayerCnt = waveCheckInDataDao.getCheckInScheduledPlayerCnt(scheduleIds);
        return waveCheckInDataConverter.toResponseGetCheckInRoomSum(incomeSumDTO, scheduledPlayerCnt);
    }

    @Override
    public ResponseGetCheckInRoomStatistic getCheckInRoomStatistic(RequestGetCheckInRoomStatistic req) {
        Long roomId = req.getRoomId();
        CheckInDateTypeEnum dateType = req.getDateType();
        Long startDate = req.getStartDate();
        Long endDate = req.getEndDate();
        Long familyId = req.getFamilyId();
        Integer appId = req.getAppId();
        List<WaveCheckInSchedule> schedules = waveCheckInDataDao.getCheckInRoomSchedules(roomId, startDate, endDate, familyId);
        List<Long> scheduleIds = schedules.stream().map(WaveCheckInSchedule::getId).collect(Collectors.toList());
        List<WaveCheckInRecord> records = waveCheckInDataDao.getCheckInRecordsByScheduleIds(scheduleIds);
        List<Long> playerIds = records.stream().map(WaveCheckInRecord::getUserId).distinct().sorted(Comparator.naturalOrder()).collect(Collectors.toList());

        // 获取主持人 ID 并加入 playerIds
        List<Long> hostIds = schedules.stream().map(WaveCheckInSchedule::getHostId).filter(Objects::nonNull)
                .filter(uid -> 0L != uid)
                .distinct().collect(Collectors.toList());
        CollUtil.addAllIfNotContains(playerIds, hostIds);

        List<WaveCheckInUnDone> unDoneList = waveCheckInDataDao.getCheckInRoomUserUnDoneList(playerIds, roomId, startDate, endDate);
        List<WaveCheckInRoomUserLightGiftGroupDTO> lightGiftGroups = waveCheckInDataDao.getCheckInRoomUserLightGiftGroups(scheduleIds);
        List<WaveCheckInRoomUserRewardAmountGroupDTO> rewardAmountGroups = waveCheckInDataDao.getCheckInRoomUserRewardAmountGroups(scheduleIds);
        List<WaveCheckInAllMicGiftRecord> allMicGiftRecords = waveCheckInDataDao.getAllMicGiftRecords(scheduleIds);
        List<WaveCheckInDayMicRecord> dayMicRecords = waveCheckInDataDao.getDayMicRecords(appId, familyId, roomId, playerIds, startDate, endDate);

        // 添加全麦的用户记录进来
        CollUtil.addAllIfNotContains(playerIds, allMicGiftRecords.stream().map(WaveCheckInAllMicGiftRecord::getAllocationUserId).collect(Collectors.toList()));
        Map<Long, SimpleUserDto> simpleUserMap = getSimpleUserMap(playerIds);
        StatisticPeriodHandler periodHandler = getStatisticPeriodHandler(dateType);
        // 排列所有时间段
        List<StatisticPeriod> presetPeriods = periodHandler.buildPresetStatisticPeriods(startDate, endDate);
        // 日麦序奖励
        Map<Long, DayMicCounter> dayMicCounterMap = periodHandler.buildDayMicCounterMap(dayMicRecords);
        // userId -> (period -> sum)
        Map<Long, Map<StatisticPeriod, WaveCheckInUserRecordSumBean>> userRecordSumMap = buildRoomUserRecordSumMap(periodHandler, schedules, records);
        // userId -> sum
        Map<Long, WaveCheckInUserSumBean> roomUserSumMap = buildRoomUserSumMap(schedules, records
                , unDoneList, lightGiftGroups, rewardAmountGroups
                , allMicGiftRecords, dayMicCounterMap);


        // 构造结果列表
        List<WaveCheckInRoomStatisticBean> list = new ArrayList<>(playerIds.size());
        for (Long playerId : playerIds) {
            // 用户信息
            WaveCheckInUserBean player = waveCheckInDataConverter.toWaveCheckInUserBean(simpleUserMap.get(playerId));
            // 明细
            Map<StatisticPeriod, WaveCheckInUserRecordSumBean> periodMap = userRecordSumMap.computeIfAbsent(playerId, k -> new HashMap<>());
            List<WaveCheckInUserRecordSumBean> detail = new ArrayList<>(presetPeriods.size());
            for (StatisticPeriod presetPeriod : presetPeriods) {
                WaveCheckInUserRecordSumBean sumBean = periodMap.computeIfAbsent(presetPeriod, k -> initUserRecordSumBean(presetPeriod, periodHandler));
                detail.add(sumBean);
            }
            // 汇总
            WaveCheckInUserSumBean sum = roomUserSumMap.computeIfAbsent(playerId, k -> new WaveCheckInUserSumBean());
            // 合并成统计bean
            WaveCheckInRoomStatisticBean statisticBean = new WaveCheckInRoomStatisticBean();
            statisticBean.setPlayer(player);
            statisticBean.setDetail(detail);
            statisticBean.setSum(sum);
            list.add(statisticBean);
        }
        ResponseGetCheckInRoomStatistic resp = new ResponseGetCheckInRoomStatistic();
        resp.setList(list);
        return resp;
    }

    private Map<Long, SimpleUserDto> getSimpleUserMap(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        return userManager.getSimpleUserMapByIds(userIds);
    }

    private SimpleUserDto getSimpleUser(long userId) {
        Map<Long, SimpleUserDto> simpleUserMap = getSimpleUserMap(Collections.singletonList(userId));
        return simpleUserMap.get(userId);
    }

    private StatisticPeriodHandler getStatisticPeriodHandler(CheckInDateTypeEnum dateType) {
        for (StatisticPeriodHandler handler : STATISTIC_PERIOD_HANDLERS) {
            if (handler.supports(dateType)) {
                return handler;
            }
        }
        throw new IllegalArgumentException("Unsupported date type: " + dateType);
    }

    private Map<Long, Map<StatisticPeriod, WaveCheckInUserRecordSumBean>> buildRoomUserRecordSumMap(
            StatisticPeriodHandler periodHandler, List<WaveCheckInSchedule> schedules, List<WaveCheckInRecord> records) {
        // scheduleId -> schedule
        Map<Long, WaveCheckInSchedule> scheduleMap = new HashMap<>();
        for (WaveCheckInSchedule schedule : schedules) {
            scheduleMap.put(schedule.getId(), schedule);
        }
        // userId -> (period -> sum)
        Map<Long, Map<StatisticPeriod, WaveCheckInUserRecordSumBean>> userRecordSumMap = new HashMap<>();
        for (WaveCheckInRecord record : records) {
            WaveCheckInSchedule schedule = scheduleMap.get(record.getScheduleId());
            if (schedule != null) {
                Long userId = record.getUserId();
                StatisticPeriod period = periodHandler.buildNormalizedStatisticPeriod(schedule.getStartTime().getTime());
                WaveCheckInUserRecordSumBean sumBean = userRecordSumMap
                        .computeIfAbsent(userId, k -> new HashMap<>())
                        .computeIfAbsent(period, k -> initUserRecordSumBean(period, periodHandler));
                // 累加数据
                sumBean.setCharm(sumBean.getCharm() + record.getCharmValue());
                sumBean.setIncome(sumBean.getIncome() + record.getIncome());
            }
        }
        return userRecordSumMap;
    }

    private WaveCheckInUserRecordSumBean initUserRecordSumBean(StatisticPeriod period, StatisticPeriodHandler periodHandler) {
        String time = periodHandler.formatStatisticPeriod(period);
        WaveCheckInUserRecordSumBean sumBean = new WaveCheckInUserRecordSumBean();
        sumBean.setTime(time);
        return sumBean;
    }

    private Map<Long, WaveCheckInUserSumBean> buildRoomUserSumMap(List<WaveCheckInSchedule> schedules,
                                                                  List<WaveCheckInRecord> records,
                                                                  List<WaveCheckInUnDone> unDoneList,
                                                                  List<WaveCheckInRoomUserLightGiftGroupDTO> lightGiftGroups,
                                                                  List<WaveCheckInRoomUserRewardAmountGroupDTO> rewardAmountGroups,
                                                                  List<WaveCheckInAllMicGiftRecord> allMicGiftRecords,
                                                                  Map<Long, DayMicCounter> dayMicCounterMap) {
        // scheduleId -> schedule
        Map<Long, WaveCheckInSchedule> scheduleMap = new HashMap<>();
        for (WaveCheckInSchedule schedule : schedules) {
            scheduleMap.put(schedule.getId(), schedule);
        }
        // userId -> (rewardLadder -> giftAmountSum)
        Map<Long, TreeMap<Long, Integer>> lightGiftSumMap = buildRoomUserLightGiftSumMap(lightGiftGroups);
        // userId -> (rewardLadder -> 全麦记录数)
        Map<Long, TreeMap<Long, Integer>> allMicGiftMap = buildAllMicGiftRecordMap(allMicGiftRecords);
        // userId -> sum
        Map<Long, WaveCheckInUserSumBean> roomUserSumMap = new HashMap<>();
        for (WaveCheckInRecord record : records) {
            Long userId = record.getUserId();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserSumBean());
            // 排档数
            if (Objects.equals(record.getScheduled(), CheckInScheduledConstants.SCHEDULER)) {
                sumBean.setScheduledCnt(sumBean.getScheduledCnt() + 1);
            }
            if (Objects.equals(record.getStatus(), CheckInStatusConstants.CHECK_IN)) {
                // 有效麦序
                sumBean.setSeatCnt(sumBean.getSeatCnt() + 1);
                // 有效麦序魅力值
                sumBean.setSeatCharm(sumBean.getSeatCharm() + record.getCharmValue());
            }
            // 主持档魅力值合计
            WaveCheckInSchedule schedule = scheduleMap.get(record.getScheduleId());
            if (schedule != null && Objects.equals(schedule.getHostId(), userId)) {
                sumBean.setHostCharmSum(sumBean.getHostCharmSum() + record.getCharmValue());
            }
            // 合计魅力值
            sumBean.setSumCharm(sumBean.getSumCharm() + record.getCharmValue());
            // 合计钻石值
            sumBean.setSumIncome(sumBean.getSumIncome() + record.getIncome());
        }
        for (WaveCheckInSchedule schedule : schedules) {
            Long hostId = schedule.getHostId();
            WaveCheckInUserSumBean hostSumBean = roomUserSumMap.computeIfAbsent(hostId, k -> new WaveCheckInUserSumBean());
            // 主持档数
            hostSumBean.setHostCnt(hostSumBean.getHostCnt() + 1);
        }
        for (WaveCheckInUnDone unDone : unDoneList) {
            Long userId = unDone.getUserId();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserSumBean());
            // 未完成任务分
            sumBean.setNotDoneScore(sumBean.getNotDoneScore() + unDone.getUnDoneScore());
        }
        for (Map.Entry<Long, TreeMap<Long, Integer>> entry : lightGiftSumMap.entrySet()) {
            Long recUserId = entry.getKey();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(recUserId, k -> new WaveCheckInUserSumBean());
            // 收光记录
            sumBean.setLightGift(formatLightGiftSum(entry.getValue()));
        }
        for (WaveCheckInRoomUserRewardAmountGroupDTO rewardAmountGroup : rewardAmountGroups) {
            Long recUserId = rewardAmountGroup.getRecUserId();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(recUserId, k -> new WaveCheckInUserSumBean());
            // 收光奖励
            sumBean.setLightGiftAmount(sumBean.getLightGiftAmount() + rewardAmountGroup.getRewardAmountSum());
        }
        // 全麦记录统计
        for (WaveCheckInAllMicGiftRecord allMicGiftRecord : allMicGiftRecords) {
            Long userId = allMicGiftRecord.getAllocationUserId();
            if (userId == null) {
                continue;
            }
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserSumBean());
            // 全麦奖励
            sumBean.setAllMicGiftAmount(sumBean.getAllMicGiftAmount() + allMicGiftRecord.getRewardAmount());
        }
        for (Map.Entry<Long, TreeMap<Long, Integer>> entry : allMicGiftMap.entrySet()) {
            Long userId = entry.getKey();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserSumBean());
            // 全麦记录
            sumBean.setAllMicGift(formatLightGiftSum(entry.getValue()));
        }
        for (Map.Entry<Long, DayMicCounter> entry : dayMicCounterMap.entrySet()) {
            Long userId = entry.getKey();
            WaveCheckInUserSumBean sumBean = roomUserSumMap.computeIfAbsent(userId, k -> new WaveCheckInUserSumBean());
            //日麦序奖励
            sumBean.setDayMicAmount(entry.getValue().getRewardAmountSum());
        }
        return roomUserSumMap;
    }

    private Map<Long, TreeMap<Long, Integer>> buildRoomUserLightGiftSumMap(
            List<WaveCheckInRoomUserLightGiftGroupDTO> lightGiftGroups) {
        // userId -> (rewardLadder -> giftAmountSum)
        Map<Long, TreeMap<Long, Integer>> lightGiftSumMap = new HashMap<>();
        for (WaveCheckInRoomUserLightGiftGroupDTO lightGiftGroup : lightGiftGroups) {
            Long recUserId = lightGiftGroup.getRecUserId();
            Map<Long, Integer> rewardLadderMap = lightGiftSumMap.computeIfAbsent(recUserId, k -> new TreeMap<>());
            Long rewardLadder = lightGiftGroup.getRewardLadder();
            Integer oldSum = rewardLadderMap.computeIfAbsent(rewardLadder, k -> 0);
            int newSum = oldSum + lightGiftGroup.getGiftAmountSum();
            rewardLadderMap.put(rewardLadder, newSum);
        }
        return lightGiftSumMap;
    }

    public Map<Long, TreeMap<Long, Integer>> buildAllMicGiftRecordMap(List<WaveCheckInAllMicGiftRecord> allMicGiftRecords){
        if (CollectionUtils.isEmpty(allMicGiftRecords)) {
            return Collections.emptyMap();
        }

        // userId -> (rewardLadder -> 用户对应记录数)
        Map<Long, TreeMap<Long, Integer>> allGiftMap = new HashMap<>();
        for (WaveCheckInAllMicGiftRecord allMicGiftRecord : allMicGiftRecords) {
            Long userId = allMicGiftRecord.getAllocationUserId();
            if (userId == null) {
                continue;
            }
            Map<Long, Integer> rewardLadderMap = allGiftMap.computeIfAbsent(userId, k -> new TreeMap<>());
            Long rewardLadder = allMicGiftRecord.getRewardLadder();
            rewardLadderMap.merge(rewardLadder, 1, Integer::sum);
        }
        return allGiftMap;
    }

    private String formatLightGiftSum(TreeMap<Long, Integer> rewardLadderToAmountSumMap) {
        if (MapUtils.isEmpty(rewardLadderToAmountSumMap)) {
            return StringUtils.EMPTY;
        }
        StringJoiner stringJoiner = new StringJoiner(",");
        for (Map.Entry<Long, Integer> entry : rewardLadderToAmountSumMap.entrySet()) {
            stringJoiner.add(entry.getKey() + "*" + entry.getValue());
        }
        return stringJoiner.toString();
    }

    @Override
    public ResponseGetCheckInPlayerSum getCheckInPlayerSum(RequestGetCheckInPlayerSum req) {
        Long playerId = req.getPlayerId();
        Long startDate = req.getStartDate();
        Long endDate = req.getEndDate();
        Long familyId = req.getFamilyId();
        Long roomId = req.getRoomId();
        WaveCheckInPlayerSumDTO playerSumDTO = waveCheckInDataDao.getCheckInPlayerSum(playerId, startDate, endDate, familyId, roomId);
        return waveCheckInDataConverter.toResponseGetCheckInPlayerSum(playerSumDTO);
    }

    @Override
    public ResponseGetCheckInPlayerStatistic getCheckInPlayerStatistic(RequestGetCheckInPlayerStatistic req) {
        Long playerId = req.getPlayerId();
        Long startDate = req.getStartDate();
        Long endDate = req.getEndDate();
        Long familyId = req.getFamilyId();
        Long roomId = req.getRoomId();
        Integer appId = req.getAppId();
        List<WaveCheckInSchedule> schedules = waveCheckInDataDao.getCheckInPlayerAndHostSchedules(playerId, startDate, endDate, familyId, roomId);
        List<Long> scheduleIds = schedules.stream().map(WaveCheckInSchedule::getId).collect(Collectors.toList());
        List<WaveCheckInRecord> records = waveCheckInDataDao.getCheckInPlayerRecords(scheduleIds, playerId);
        List<Long> roomIds = roomId != null ? Collections.singletonList(roomId) : schedules.stream().map(WaveCheckInSchedule::getRoomId).distinct().collect(Collectors.toList());
        List<WaveCheckInUnDone> unDoneList = waveCheckInDataDao.getCheckInUserUnDoneList(playerId, roomIds, startDate, endDate, familyId);
        List<WaveCheckInUserLightGiftGroupDTO> lightGiftGroups = waveCheckInDataDao.getCheckInUserLightGiftGroups(scheduleIds, playerId);
        List<WaveCheckInUserRewardAmountGroupDTO> rewardAmountGroups = waveCheckInDataDao.getCheckInUserRewardAmountGroups(scheduleIds, playerId);
        List<WaveCheckInAllMicGiftRecord> allMicGiftRecords = waveCheckInDataDao.getAllMicGiftRecordsByUserId(scheduleIds, playerId);
        List<WaveCheckInDayMicRecord> dayMicRecords = waveCheckInDataDao.getDayMicRecords(appId, familyId, roomId, Lists.newArrayList(playerId), startDate, endDate);

        SimpleUserDto simpleUser = getSimpleUser(playerId);
        // 主播视角明细是列出一天24个档期的数据. 但每行对应一天.
        StatisticPeriodHandler periodHandler = getStatisticPeriodHandler(CheckInDateTypeEnum.DAY);
        // 排列所有时间段
        TreeMap<Long, List<StatisticPeriod>> statisticPeriodsMap = buildPlayerPresetStatisticPeriodsMap(startDate, endDate, periodHandler);
        // startTimeOfDay -> (period -> sum)
        Map<Long, Map<StatisticPeriod, WaveCheckInUserRecordSumBean>> playerRecordSumMap = buildPlayerRecordSumMap(periodHandler, schedules, records, playerId);
        // startTimeOfDay -> sum
        Map<Long, WaveCheckInUserSumBean> playerSumMap = buildPlayerSumMap(playerId, schedules
                , records, unDoneList, lightGiftGroups, rewardAmountGroups
                , allMicGiftRecords, dayMicRecords
        );
        // 用户信息
        WaveCheckInUserBean player = waveCheckInDataConverter.toWaveCheckInUserBean(simpleUser);
        // 构造结果列表
        ArrayList<WaveCheckInPlayerStatisticBean> list = new ArrayList<>(statisticPeriodsMap.size());
        for (Map.Entry<Long, List<StatisticPeriod>> entry : statisticPeriodsMap.entrySet()) {
            Long startTimeOfDay = entry.getKey();
            List<StatisticPeriod> presetPeriods = entry.getValue();
            // 日期
            String date = formatPlayerDate(startTimeOfDay);
            // 明细
            Map<StatisticPeriod, WaveCheckInUserRecordSumBean> periodMap = playerRecordSumMap.computeIfAbsent(startTimeOfDay, k -> new HashMap<>());
            List<WaveCheckInUserRecordSumBean> detail = new ArrayList<>(presetPeriods.size());
            for (StatisticPeriod presetPeriod : presetPeriods) {
                WaveCheckInUserRecordSumBean sumBean = periodMap.computeIfAbsent(presetPeriod, k -> initUserRecordSumBean(presetPeriod, periodHandler));
                detail.add(sumBean);
            }
            // 汇总
            WaveCheckInUserSumBean sum = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 合并成统计bean
            WaveCheckInPlayerStatisticBean statisticBean = new WaveCheckInPlayerStatisticBean();
            statisticBean.setDate(date);
            statisticBean.setDetail(detail);
            statisticBean.setSum(sum);
            list.add(statisticBean);
        }
        ResponseGetCheckInPlayerStatistic resp = new ResponseGetCheckInPlayerStatistic();
        resp.setPlayer(player);
        resp.setList(list);
        return resp;
    }

    private TreeMap<Long, List<StatisticPeriod>> buildPlayerPresetStatisticPeriodsMap(long startDate, long endDate,
                                                                                      StatisticPeriodHandler periodHandler) {
        // startTimeOfDay -> hour periods
        TreeMap<Long, List<StatisticPeriod>> statisticPeriodsMap = new TreeMap<>();
        long startTimeOfDay = getStartTimeOfDay(startDate);
        while (startTimeOfDay <= endDate) {
            long endTime = startTimeOfDay + 24 * 60 * 60 * 1000L - 1;
            List<StatisticPeriod> periods = periodHandler.buildPresetStatisticPeriods(startTimeOfDay, endTime);
            statisticPeriodsMap.put(startTimeOfDay, periods);
            startTimeOfDay += 24 * 60 * 60 * 1000L;
        }
        return statisticPeriodsMap;
    }

    private Map<Long, Map<StatisticPeriod, WaveCheckInUserRecordSumBean>> buildPlayerRecordSumMap(
            StatisticPeriodHandler periodHandler, List<WaveCheckInSchedule> schedules, List<WaveCheckInRecord> records, Long playerId) {
        // scheduleId -> schedule
        Map<Long, WaveCheckInSchedule> scheduleMap = new HashMap<>();
        for (WaveCheckInSchedule schedule : schedules) {
            scheduleMap.put(schedule.getId(), schedule);
        }
        // startTimeOfDay -> (period -> sum)
        Map<Long, Map<StatisticPeriod, WaveCheckInUserRecordSumBean>> playerRecordSumMap = new HashMap<>();
        for (WaveCheckInRecord record : records) {
            WaveCheckInSchedule schedule = scheduleMap.get(record.getScheduleId());
            if (schedule != null && record.getUserId().equals(playerId)) {
                long startTime = schedule.getStartTime().getTime();
                StatisticPeriod period = periodHandler.buildNormalizedStatisticPeriod(startTime);
                long startTimeOfDay = getStartTimeOfDay(startTime);
                WaveCheckInUserRecordSumBean sumBean = playerRecordSumMap
                        .computeIfAbsent(startTimeOfDay, k -> new HashMap<>())
                        .computeIfAbsent(period, k -> initUserRecordSumBean(period, periodHandler));
                // 累加数据
                sumBean.setCharm(sumBean.getCharm() + record.getCharmValue());
                sumBean.setIncome(sumBean.getIncome() + record.getIncome());
            }
        }
        return playerRecordSumMap;
    }

    @Override
    public Long countUserCheckIn(Long userId, Date startDay, Date endTime) {
        return waveRoomCheckInRecordMapper.countUserCheckIn(ContextUtils.getBusinessEvnEnum().getAppId()
                , userId
                , DateUtil.getDayStart(startDay)
                , DateUtil.getDayEnd(endTime));
    }

    private Map<Long, WaveCheckInUserSumBean> buildPlayerSumMap(Long playerId,
                                                                List<WaveCheckInSchedule> schedules,
                                                                List<WaveCheckInRecord> records,
                                                                List<WaveCheckInUnDone> unDoneList,
                                                                List<WaveCheckInUserLightGiftGroupDTO> lightGiftGroups,
                                                                List<WaveCheckInUserRewardAmountGroupDTO> rewardAmountGroups,
                                                                List<WaveCheckInAllMicGiftRecord> allMicGiftRecords,
                                                                List<WaveCheckInDayMicRecord> dayMicRecords) {
        // scheduleId -> schedule
        Map<Long, WaveCheckInSchedule> scheduleMap = new HashMap<>();
        for (WaveCheckInSchedule schedule : schedules) {
            scheduleMap.put(schedule.getId(), schedule);
        }
        // startTimeOfDay -> (rewardLadder -> giftAmountSum)
        Map<Long, TreeMap<Long, Integer>> lightGiftSumMap = buildPlayerLightGiftSumMap(scheduleMap, lightGiftGroups);
        // startTimeOfDay -> sum
        Map<Long, WaveCheckInUserSumBean> playerSumMap = new HashMap<>();
        for (WaveCheckInRecord record : records) {
            WaveCheckInSchedule schedule = scheduleMap.get(record.getScheduleId());
            if (schedule == null || !record.getUserId().equals(playerId)) {
                continue;
            }
            long startTimeOfDay = getStartTimeOfDay(schedule.getStartTime().getTime());
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 排档数
            if (Objects.equals(record.getScheduled(), CheckInScheduledConstants.SCHEDULER)) {
                sumBean.setScheduledCnt(sumBean.getScheduledCnt() + 1);
            }
            if (Objects.equals(record.getStatus(), CheckInStatusConstants.CHECK_IN)) {
                // 有效麦序
                sumBean.setSeatCnt(sumBean.getSeatCnt() + 1);
                // 有效麦序魅力值
                sumBean.setSeatCharm(sumBean.getSeatCharm() + record.getCharmValue());
            }
            // 主持档魅力值合计
            if (Objects.equals(schedule.getHostId(), record.getUserId())) {
                sumBean.setHostCharmSum(sumBean.getHostCharmSum() + record.getCharmValue());
            }
            // 合计魅力值
            sumBean.setSumCharm(sumBean.getSumCharm() + record.getCharmValue());
            // 合计钻石值
            sumBean.setSumIncome(sumBean.getSumIncome() + record.getIncome());
        }
        for (WaveCheckInSchedule schedule : schedules) {
            if (Objects.equals(schedule.getHostId(), playerId)) {
                long startTimeOfDay = getStartTimeOfDay(schedule.getStartTime().getTime());
                WaveCheckInUserSumBean hostSumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
                // 主持档数
                hostSumBean.setHostCnt(hostSumBean.getHostCnt() + 1);
            }
        }
        for (WaveCheckInUnDone unDone : unDoneList) {
            long startTimeOfDay = getStartTimeOfDay(unDone.getCurrDate().getTime());
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 未完成任务分
            sumBean.setNotDoneScore(sumBean.getNotDoneScore() + unDone.getUnDoneScore());
        }
        for (Map.Entry<Long, TreeMap<Long, Integer>> entry : lightGiftSumMap.entrySet()) {
            Long startTimeOfDay = entry.getKey();
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 收光记录
            sumBean.setLightGift(formatLightGiftSum(entry.getValue()));
        }
        for (WaveCheckInUserRewardAmountGroupDTO rewardAmountGroup : rewardAmountGroups) {
            WaveCheckInSchedule schedule = scheduleMap.get(rewardAmountGroup.getScheduleId());
            if (schedule == null) {
                continue;
            }
            long startTimeOfDay = getStartTimeOfDay(schedule.getStartTime().getTime());
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 收光奖励
            sumBean.setLightGiftAmount(sumBean.getLightGiftAmount() + rewardAmountGroup.getRewardAmountSum());
        }
        for (WaveCheckInAllMicGiftRecord allMicGiftRecord : allMicGiftRecords) {
            WaveCheckInSchedule schedule = scheduleMap.get(allMicGiftRecord.getScheduleId());
            if (schedule == null) {
                continue;
            }
            long startTimeOfDay = getStartTimeOfDay(schedule.getStartTime().getTime());
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 全麦奖励
            sumBean.setAllMicGiftAmount(sumBean.getAllMicGiftAmount() + allMicGiftRecord.getRewardAmount());
        }
        Map<Long, TreeMap<Long, Integer>> allMicGiftTreeMap = buildAllMicGiftRecordMap(scheduleMap, allMicGiftRecords);
        for (Map.Entry<Long, TreeMap<Long, Integer>> entry : allMicGiftTreeMap.entrySet()) {
            Long startTimeOfDay = entry.getKey();
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            // 全麦记录
            sumBean.setAllMicGift(formatLightGiftSum(entry.getValue()));
        }
        for (WaveCheckInDayMicRecord dayMicRecord : dayMicRecords) {
            long startTimeOfDay = getStartTimeOfDay(dayMicRecord.getCalcDate().getTime());
            WaveCheckInUserSumBean sumBean = playerSumMap.computeIfAbsent(startTimeOfDay, k -> new WaveCheckInUserSumBean());
            //日麦序奖励
            sumBean.setDayMicAmount(sumBean.getDayMicAmount() + dayMicRecord.getRewardAmountSum());
        }
        return playerSumMap;
    }

    private Map<Long, TreeMap<Long, Integer>> buildPlayerLightGiftSumMap(Map<Long, WaveCheckInSchedule> scheduleMap,
                                                                         List<WaveCheckInUserLightGiftGroupDTO> lightGiftGroups) {
        // startTimeOfDay -> (rewardLadder -> giftAmountSum)
        Map<Long, TreeMap<Long, Integer>> lightGiftSumMap = new HashMap<>();
        for (WaveCheckInUserLightGiftGroupDTO lightGiftGroup : lightGiftGroups) {
            Long scheduleId = lightGiftGroup.getScheduleId();
            WaveCheckInSchedule schedule = scheduleMap.get(scheduleId);
            if (schedule != null) {
                long startTimeOfDay = getStartTimeOfDay(schedule.getStartTime().getTime());
                Map<Long, Integer> rewardLadderMap = lightGiftSumMap.computeIfAbsent(startTimeOfDay, k -> new TreeMap<>());
                Long rewardLadder = lightGiftGroup.getRewardLadder();
                Integer oldSum = rewardLadderMap.computeIfAbsent(rewardLadder, k -> 0);
                int newSum = oldSum + lightGiftGroup.getGiftAmountSum();
                rewardLadderMap.put(rewardLadder, newSum);
            }
        }
        return lightGiftSumMap;
    }

    public Map<Long, TreeMap<Long, Integer>> buildAllMicGiftRecordMap(Map<Long, WaveCheckInSchedule> scheduleMap, List<WaveCheckInAllMicGiftRecord> allMicGiftRecords){
        if (CollectionUtils.isEmpty(allMicGiftRecords)) {
            return Collections.emptyMap();
        }

        // startTimeOfDay -> (rewardLadder -> 用户对应记录数)
        Map<Long, TreeMap<Long, Integer>> allGiftMap = new HashMap<>();
        for (WaveCheckInAllMicGiftRecord allMicGiftRecord : allMicGiftRecords) {
            Long scheduleId = allMicGiftRecord.getScheduleId();
            WaveCheckInSchedule schedule = scheduleMap.get(scheduleId);
            if (schedule == null) {
                continue;
            }
            long startTimeOfDay = getStartTimeOfDay(schedule.getStartTime().getTime());
            Map<Long, Integer> rewardLadderMap = allGiftMap.computeIfAbsent(startTimeOfDay, k -> new TreeMap<>());
            Long rewardLadder = allMicGiftRecord.getRewardLadder();
            rewardLadderMap.merge(rewardLadder, 1, Integer::sum);
        }
        return allGiftMap;
    }

    private String formatPlayerDate(long startTimeOfDay) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(startTimeOfDay), ZoneId.systemDefault())
                .format(PLAYER_DATE_FORMATTER);
    }

    private static long getStartTimeOfDay(long time) {
        LocalDateTime t = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
        if (t.getHour() == 0 && t.getMinute() == 0 && t.getSecond() == 0 && t.getNano() == 0) {
            return time;
        }
        return t.withHour(0).withMinute(0).withSecond(0).withNano(0)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 统计时段
     */
    @Data
    private static class StatisticPeriod {
        /**
         * 时间范围类型
         */
        private final CheckInDateTypeEnum dateType;
        /**
         * 开始时间, 包含
         */
        private final long startTime;
    }

    /**
     * 日麦序奖励统计结果
     */
    @Data
    @Accessors(chain = true)
    private static class DayMicCounter {
        /**
         * 总奖励金额
         */
        private Long rewardAmountSum = 0L;
    }

    /**
     * 统计时间段处理器, 将时间段相关的操作封装到一个接口中, 以便于扩展.
     */
    private interface StatisticPeriodHandler {

        /**
         * 是否支持处理指定的时间范围类型
         *
         * @param dateType 时间范围类型
         * @return 是否支持
         */
        boolean supports(CheckInDateTypeEnum dateType);

        /**
         * 根据开始时间和结束时间构造预设的统计时间段列表, 因为部分档期可能没有打卡记录, 但需要在后台展示, 因此需要构造本应有的时间段.
         *
         * @param startDate 开始时间, 包含
         * @param endDate   结束时间, 包含
         * @return 预设的统计时间段列表
         */
        List<StatisticPeriod> buildPresetStatisticPeriods(long startDate, long endDate);

        /**
         * 根据开始时间构造标准化的统计时间段, 用于统计数据的归类.
         *
         * @param startTime 开始时间
         * @return 标准化的统计时间段
         */
        StatisticPeriod buildNormalizedStatisticPeriod(long startTime);

        /**
         * 格式化统计时间段, 用于展示.
         *
         * @param period 统计时间段
         * @return 格式化后的时间段
         */
        String formatStatisticPeriod(StatisticPeriod period);

        /**
         * 根据日麦序奖励记录，按照主播维度进行统计
         * @param dayMicRecords
         * @return key->主播用户ID, value->日麦序奖励统计结果
         */
        Map<Long, DayMicCounter> buildDayMicCounterMap(List<WaveCheckInDayMicRecord> dayMicRecords);
    }

    /**
     * 日统计时间段处理器实现
     */
    private static class DayStatisticPeriodHandler implements StatisticPeriodHandler {

        @Override
        public boolean supports(CheckInDateTypeEnum dateType) {
            return CheckInDateTypeEnum.DAY.equals(dateType);
        }

        @Override
        public List<StatisticPeriod> buildPresetStatisticPeriods(long startDate, long endDate) {
            // 日统计的每段为1小时
            List<StatisticPeriod> statisticPeriods = new ArrayList<>();
            long periodStart = startDate;
            while (periodStart <= endDate) {
                statisticPeriods.add(new StatisticPeriod(CheckInDateTypeEnum.DAY, periodStart));
                periodStart += 60 * 60 * 1000L;
            }
            return statisticPeriods;
        }

        @Override
        public StatisticPeriod buildNormalizedStatisticPeriod(long startTime) {
            // 日统计直接使用其开始时间表示时间段
            return new StatisticPeriod(CheckInDateTypeEnum.DAY, startTime);
        }

        @Override
        public String formatStatisticPeriod(StatisticPeriod period) {
            LocalDateTime time = LocalDateTime.ofInstant(Instant.ofEpochMilli(period.getStartTime()), ZoneId.systemDefault());
            int startHour = time.getHour();
            int endHour = startHour + 1;
            // 比如 1-2, 23-24
            return String.format("%d-%d", startHour, endHour);
        }

        @Override
        public Map<Long, DayMicCounter> buildDayMicCounterMap(List<WaveCheckInDayMicRecord> dayMicRecords) {
            if (CollectionUtils.isEmpty(dayMicRecords)) {
                return Collections.emptyMap();
            }
            //日维度，一个用户只会有一条数据
            return dayMicRecords.stream().collect(Collectors.toMap(
                    WaveCheckInDayMicRecord::getUserId,
                    v->new DayMicCounter().setRewardAmountSum(v.getRewardAmountSum()),
                    (k1, k2)->k2
            ));
        }
    }

    /**
     * 周统计时间段处理器实现
     */
    private static class WeekStatisticPeriodHandler implements StatisticPeriodHandler {

        private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("MM/dd");

        @Override
        public boolean supports(CheckInDateTypeEnum dateType) {
            return CheckInDateTypeEnum.WEEK.equals(dateType);
        }

        @Override
        public List<StatisticPeriod> buildPresetStatisticPeriods(long startDate, long endDate) {
            // 周统计的每段为1天
            List<StatisticPeriod> statisticPeriods = new ArrayList<>();
            long periodStart = startDate;
            while (periodStart <= endDate) {
                statisticPeriods.add(new StatisticPeriod(CheckInDateTypeEnum.WEEK, periodStart));
                periodStart += 24 * 60 * 60 * 1000L;
            }
            return statisticPeriods;
        }

        @Override
        public StatisticPeriod buildNormalizedStatisticPeriod(long startTime) {
            // 周统计使用开始时间所在当天的0点表示时间段
            long startTimeOfDay = getStartTimeOfDay(startTime);
            return new StatisticPeriod(CheckInDateTypeEnum.WEEK, startTimeOfDay);
        }

        @Override
        public String formatStatisticPeriod(StatisticPeriod period) {
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(period.getStartTime()), ZoneId.systemDefault())
                    .format(FORMATTER);
        }

        @Override
        public Map<Long, DayMicCounter> buildDayMicCounterMap(List<WaveCheckInDayMicRecord> dayMicRecords) {
            if (CollectionUtils.isEmpty(dayMicRecords)) {
                return Collections.emptyMap();
            }
            Map<Long, DayMicCounter> map = new HashMap<>();
            for (WaveCheckInDayMicRecord record : dayMicRecords) {
                DayMicCounter counter = map.computeIfAbsent(record.getUserId(), k -> new DayMicCounter());
                counter.setRewardAmountSum(counter.getRewardAmountSum() + record.getRewardAmountSum());
            }
            return map;
        }
    }
}
