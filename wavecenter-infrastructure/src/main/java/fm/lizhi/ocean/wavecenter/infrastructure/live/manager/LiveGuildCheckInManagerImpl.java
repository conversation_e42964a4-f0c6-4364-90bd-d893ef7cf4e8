package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.api.live.bean.*;
import fm.lizhi.ocean.wavecenter.api.user.bean.RoomBean;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.live.convert.LiveGuildCheckConvert;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.entity.GuildRoomHourSummaryEntity;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.entity.WaveCheckInRecordEntity;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.entity.WcRoomCheckInDayStats;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.mapper.WaveCheckInRecordEntityMapper;
import fm.lizhi.ocean.wavecenter.service.income.config.IncomeConfig;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveGuildCheckInManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
@Deprecated
public class LiveGuildCheckInManagerImpl implements LiveGuildCheckInManager {

    @Autowired
    private CheckInDayRouter checkInDayRouter;

    @Autowired
    private WaveCheckInRecordEntityMapper waveCheckInRecordMapper;

    @Autowired
    private IncomeConfig incomeConfig;
    @Autowired
    private CheckGroupManager checkGroupManager;


    /**
     * 1.查询合计
     * 2.查询明细
     *
     * @return
     */
    @Override
    public LGCSRoomDayStatsRes roomDayStats(GuildRoomDayCheckStatsReq req) {

        /**
         * 获取所有的厅ID
         */
        List<Long> njIds = checkInDayRouter.guildRoomDayAllNjId(req.getAppId(), req.getFamilyId(), req.getRoomId(), req.getStartDate(), req.getEndDate());
        if (CollectionUtils.isEmpty(njIds)) {
            return new LGCSRoomDayStatsRes();
        }
        // 获取
        List<Date> rangeDayDate = MyDateUtil.getRangeDayDate(req.getStartDate(), req.getEndDate(), req.getPageNo(), req.getPageSize());

        Date rangeStart = MyDateUtil.getRangeStartDesc(req.getStartDate(), req.getEndDate(), req.getPageNo(), req.getPageSize());
        Date rangeEnd = MyDateUtil.getRangeEndDesc(req.getStartDate(), req.getEndDate(), req.getPageNo(), req.getPageSize());

        List<WcRoomCheckInDayStats> wcRoomCheckInDayStats = checkInDayRouter.guildRoomDayDetail(req.getAppId(), req.getFamilyId(), req.getRoomId(), rangeStart, rangeEnd);

        Map<Long, Map<Date, GuildRoomDayDetail>> wcRoomCheckInDayStatsMap = wcRoomCheckInDayStats.stream()
                .collect(Collectors.groupingBy(WcRoomCheckInDayStats::getNjId))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .map(LiveGuildCheckConvert.I::convertGuildRoomDayDetail)
                                .collect(Collectors.toMap(
                                        GuildRoomDayDetail::getTime,
                                        detail -> detail,
                                        (existingValue, newValue) -> existingValue)
                                )
                ));

        log.info("checkInDayRouter guildRoomDayStats appId={},familyId={},roomId={},startDate={},endDate={}", req.getAppId(), req.getFamilyId(), req.getRoomId(), req.getStartDate(), req.getEndDate());
        List<GuildRoomDayStats> guildRoomDayStats = checkInDayRouter.guildRoomDayStats(req.getAppId(), req.getFamilyId(), req.getRoomId(), req.getStartDate(), req.getEndDate());

        Map<Long, GuildRoomDayStats> guildRoomDayStatsMap = guildRoomDayStats.stream()
                .collect(Collectors.toMap(GuildRoomDayStats::getNjId,
                        guildRoomDay -> guildRoomDay,
                        // 将重复的njId处理掉，保留第一个出现的值
                        (existingValue, newValue) -> existingValue));

        List<GuildRoomDayStatsRes> roomDayStatsRes = new ArrayList<>();

        for (Long njId : njIds) {
            RoomBean room = new RoomBean();
            room.setId(njId);

            GuildRoomDayStats roomDayStats = guildRoomDayStatsMap.getOrDefault(njId, GuildRoomDayStats.builder().build());

            List<GuildRoomDayDetail> timeDetails = new ArrayList<>();

            Map<Date, GuildRoomDayDetail> dayDetailMap = wcRoomCheckInDayStatsMap.getOrDefault(njId, new HashMap<>());
            for (Date date : rangeDayDate) {
                GuildRoomDayDetail timeDetail = dayDetailMap.getOrDefault(date, GuildRoomDayDetail.builder().time(date).build());
                timeDetails.add(timeDetail);
            }
            timeDetails.sort(Comparator.comparing(GuildRoomDayDetail::getTime).reversed());
            roomDayStatsRes.add(GuildRoomDayStatsRes.builder()
                    .room(room)
                    .stats(roomDayStats)
                    .detail(timeDetails)
                    .build());
        }

        //排序
        //statsRes排序
        roomDayStatsRes.sort((o1, o2) -> {
            if (o1 == null || o1.getStats() == null) {
                return 1;
            }
            if (o2 == null || o2.getStats() == null) {
                return -1;
            }
            BigDecimal income1 = o1.getStats().foundIncomeNum();
            BigDecimal income2 = o2.getStats().foundIncomeNum();
            return income1.compareTo(income2);
        });

        LGCSRoomDayStatsRes res = new LGCSRoomDayStatsRes();
        res.setList(roomDayStatsRes);
        res.setTimeStats(checkGroupManager.groupTimeStats(roomDayStatsRes));
        res.setTimeStatSum(checkGroupManager.sumTimeStats(roomDayStatsRes));
        return res;
    }

    @Override
    public GuildRoomDayStatsSummaryRes roomDayStatsSummary(GuildRoomDayCheckStatsReq req) {
        return checkInDayRouter.guildRoomDaySummary(req.getAppId(), req.getFamilyId(), req.getRoomId(), req.getStartDate(), req.getEndDate());
    }

    @Override
    public LGCSRoomHourStatsRes roomHourStats(GuildRoomHourCheckStatsReq req) {
        int pageNo = 1;
        List<WaveCheckInRecordEntity> waveRes = new ArrayList<>();
        boolean isHasNextPage = false;
        do {
            //分页查询，按家族查，数据量会比较大
            PageList<WaveCheckInRecordEntity> waveCheckInRecords = waveCheckInRecordMapper.guildRoomHourDetail(req.getAppId(), req.getFamilyId(),
                    req.getStartDate(), req.getEndDate(), pageNo, incomeConfig.getQueryCheckInRecordMaxSize());
            //是否有下一页
            isHasNextPage = waveCheckInRecords.isHasNextPage();
            waveRes.addAll(waveCheckInRecords);
            log.info("waveCheckInRecordMapper guildRoomHourDetail pageNo={},isHasNextPage={}", pageNo, isHasNextPage);
            pageNo++;
        } while (isHasNextPage);

        // 按小时
        Map<Long, Map<Date, List<WaveCheckInRecordEntity>>> hourGroupedRecordMap = waveRes.stream()
                .collect(Collectors.groupingBy(
                        WaveCheckInRecordEntity::getNjId,
                        Collectors.groupingBy(WaveCheckInRecordEntity::getStartTime)
                ));

        // 每天的 小时区分
        List<Date> rangeHourDate = MyDateUtil.getRangeHourDate(req.getStartDate(), req.getEndDate());

        // 按天
        Map<Long, List<WaveCheckInRecordEntity>> dayGroupedRecordMap = waveRes.stream()
                .collect(Collectors.groupingBy(
                        WaveCheckInRecordEntity::getNjId
                ));


        List<GuildRoomHourStatsRes> statsRes = new ArrayList<>();

        for (Long njId : hourGroupedRecordMap.keySet()) {
            GuildRoomHourStatsRes res = new GuildRoomHourStatsRes();
            RoomBean room = new RoomBean();
            room.setId(njId);

            List<WaveCheckInRecordEntity> dayRecords = dayGroupedRecordMap.get(njId);
            BigDecimal daySumInCome = new BigDecimal("0");
            long daySumCharm = 0;
            int daySumSeatOrder = 0;
            HashSet<Long> dayCheckPlayerSet = new HashSet<>();

            for (WaveCheckInRecordEntity checkInRecord : dayRecords) {
                if (checkInRecord.getIncome() != null) {
                    daySumInCome = daySumInCome.add(checkInRecord.getIncome());
                }

                if (checkInRecord.getCharmValue() != null) {
                    daySumCharm += checkInRecord.getCharmValue();
                } else {
                    if (checkInRecord.getCharm() != null) {
                        daySumCharm += checkInRecord.getCharm();
                    }
                }

                if (checkInRecord.getStatus() != null) {
                    daySumSeatOrder += checkInRecord.getStatus();
                    if (checkInRecord.getStatus() > 0) {
                        dayCheckPlayerSet.add(checkInRecord.getUserId());
                    }
                }

            }
            GuildRoomHourStats stats = new GuildRoomHourStats();
            stats.setCharm(daySumCharm);
            stats.setIncome(daySumInCome.toString());
            stats.setSeatOrder(daySumSeatOrder);
            stats.setCheckPlayerNumber(dayCheckPlayerSet.size());

            // 厅下面的 按天 分类的详情
            Map<Date, List<WaveCheckInRecordEntity>> dayMap = hourGroupedRecordMap.get(njId);

            List<GuildRoomHourDetail> details = new ArrayList<>();

            for (Date date : rangeHourDate) {
                GuildRoomHourDetail detail = new GuildRoomHourDetail();

                BigDecimal hourSumIncome = new BigDecimal(0);
                long hourSumCharm = 0;
                int hourSumSeatOrder = 0;
                HashSet<Long> playerSet = new HashSet<>();
                List<WaveCheckInRecordEntity> checkInRecords = dayMap.getOrDefault(date, new ArrayList<>());
                for (WaveCheckInRecordEntity checkInRecord : checkInRecords) {
                    if (checkInRecord.getIncome() != null) {
                        hourSumIncome = hourSumIncome.add(checkInRecord.getIncome());
                    }
                    if (checkInRecord.getCharmValue() != null) {
                        hourSumCharm += checkInRecord.getCharmValue();
                    } else {
                        if (checkInRecord.getCharm() != null) {
                            hourSumCharm += checkInRecord.getCharm();
                        }
                    }
                    if (checkInRecord.getStatus() != null) {
                        hourSumSeatOrder += checkInRecord.getStatus();
                        if (checkInRecord.getStatus() > 0) {
                            playerSet.add(checkInRecord.getUserId());
                        }
                    }
                }
                detail.setTime(date);
                detail.setIncome(hourSumIncome.toString());
                detail.setCharm(hourSumCharm);
                detail.setSeatOrder(hourSumSeatOrder);
                detail.setCheckPlayerNumber(playerSet.size());
                details.add(detail);
            }
            res.setRoom(room);
            res.setStats(stats);
            res.setDetail(details);
            statsRes.add(res);
        }

        //statsRes排序
        statsRes.sort((o1, o2) -> {
            if (o1 == null || o1.getStats() == null) {
                return 1;
            }
            if (o2 == null || o2.getStats() == null) {
                return -1;
            }
            BigDecimal income1 = o1.getStats().foundIncomeNum();
            BigDecimal income2 = o2.getStats().foundIncomeNum();
            return income1.compareTo(income2);
        });

        LGCSRoomHourStatsRes res = new LGCSRoomHourStatsRes();
        res.setTotal(statsRes.size());
        res.setList(statsRes);
        res.setTimeStats(checkGroupManager.groupTimeStats(statsRes));
        res.setTimeStatsSum(checkGroupManager.sumTimeStats(statsRes));
        return res;
    }

    @Override
    public GuildRoomHourStatsSummaryRes roomHourStatsSummary(GuildRoomHourCheckStatsReq req) {
        // 小时汇总
        GuildRoomHourSummaryEntity summaryEntity = waveCheckInRecordMapper.guildRoomHourSummary(req.getAppId(), req.getFamilyId(), req.getStartDate(), req.getEndDate());
        if (summaryEntity == null) {
            log.warn("roomHourStatsSummary summaryEntity is null req={}", JSONObject.toJSONString(req));
            return GuildRoomHourStatsSummaryRes.builder().build();
        }
        GuildRoomHourStatsSummaryRes.GuildRoomHourStatsSummaryResBuilder builder = GuildRoomHourStatsSummaryRes.builder();
        return builder.charm(summaryEntity.getCharm())
                .income(summaryEntity.getIncome().toString())
                .seatOrder(summaryEntity.getSeatOrder())
                .checkPlayerNumber(summaryEntity.getCheckPlayerNumber()).build();
    }

}
