package fm.lizhi.ocean.wavecenter.infrastructure.common.util;

import lombok.extern.slf4j.Slf4j;

import javax.persistence.Column;
import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @date 2025/4/24 15:09
 */
@Slf4j
public class ColumnUtils {
    /**
     * 根据字段名获取@Column注解的value值
     * @param clazz 目标类
     * @param fieldName 字段名
     * @return 字段对应的column name，若未找到返回null
     */
    public static String getColumnName(Class<?> clazz, String fieldName) {
        try {
            Field field = clazz.getDeclaredField(fieldName);
            Column column = field.getAnnotation(Column.class);
            if (column != null) {
                return column.name().replace("`", ""); // 去除模板中的反引号
            }
        } catch (NoSuchFieldException e) {
            log.error("getColumnName error:", e);
        }
        return null;
    }

}
