package fm.lizhi.ocean.wavecenter.infrastructure.live.constant;

import fm.lizhi.ocean.wavecenter.infrastructure.common.constants.IRedisKey;

/**
 * 打卡缓存key
 */
public enum CheckInRedisKey implements IRedisKey {

    /**
     * 打卡报告数据
     * WC_CHECK_IN_REPORT_STAT_DATA_[appId]_[dataType]_[roomId]_[yyyyMMddHH]
     * value=familyId
     */
    REPORT_STAT_DATA,

    /**
     * 打卡报告数据-锁
     * WC_CHECK_IN_REPORT_STAT_DATA_LOCK_[appId]_[dataType]_[roomId]_[yyyyMMddHH]
     * value=familyId
     */
    REPORT_STAT_DATA_LOCK
    ;

    @Override
    public String getPrefix() {
        return "WC_CHECK_IN";
    }

    @Override
    public String getName() {
        return this.name();
    }
}
