package fm.lizhi.ocean.wavecenter.infrastructure.live.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.service.live.handler.CheckInNotifyHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 发送打卡报告私信
 */
@Component
@Slf4j
public class PpSendCheckInReportJob implements JobHandler {

    @Autowired
    private CheckInNotifyHandler checkInNotifyHandler;

    @Override
    public void execute(JobExecuteContext context) throws Exception {
        log.info("SendCheckInChatJob PP job={}", context);
        Date triggerTime = new Date();
        if (null != context) {
            String param = context.getParam();
            if (StrUtil.isNotBlank(param)) {
                log.info("HySendCheckInReportJob execute param:{}", param);
                triggerTime = DateUtil.parse(param, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }
        checkInNotifyHandler.executeNotify(triggerTime, BusinessEvnEnum.PP.appId());
    }



}
