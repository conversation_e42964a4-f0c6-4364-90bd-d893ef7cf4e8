package fm.lizhi.ocean.wavecenter.infrastructure.live.manager;

import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomDayStats;
import fm.lizhi.ocean.wavecenter.api.live.bean.GuildRoomDayStatsSummaryRes;
import fm.lizhi.ocean.wavecenter.api.live.bean.RoomDayStatsSummaryRes;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.entity.RoomDayCalendarEntity;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.entity.RoomDayStatsDetailEntity;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.entity.RoomDayStatsEntity;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.entity.WcRoomCheckInDayStats;
import fm.lizhi.ocean.wavecenter.infrastructure.live.datastore.po.RoomCheckPlayerPo;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/21 17:49
 */
@Slf4j
@Component
@Deprecated
public class CheckInDayRouter implements ICheckInDayRouter{

    @Autowired
    private HistoryCheckDayDataManager history;
    @Autowired
    private LatestCheckDayDataManager latest;

    @Override
    public List<Long> guildRoomDayAllNjId(Integer appId, Long familyId, Long njId, Date startDate, Date endDate){
        //根据时间划分策越
        Set<Long> result = new HashSet<>();

        getTodayDateScope(startDate, endDate).ifPresent(v->{
            log.info("latest.guildRoomDayAllNjId appId={},familyId={},njId={},startDate={},endDate={}", appId, familyId, njId, v.startDate, v.endDate);
            result.addAll(latest.guildRoomDayAllNjId(appId, familyId, njId, v.startDate, v.endDate));
        });
        getHistoryDateScope(startDate, endDate).ifPresent(v->{
            log.info("history.guildRoomDayAllNjId appId={},familyId={},njId={},startDate={},endDate={}", appId, familyId, njId, v.startDate, v.endDate);
            result.addAll(history.guildRoomDayAllNjId(appId, familyId, njId, v.startDate, v.endDate));
        });

        return new ArrayList<>(result);
    }

    @Override
    public List<WcRoomCheckInDayStats> guildRoomDayDetail(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        List<WcRoomCheckInDayStats> result = new ArrayList<>();
        getTodayDateScope(startDate, endDate).ifPresent(v->{
            log.info("latest.guildRoomDayDetail appId={},familyId={},njId={},startDate={},endDate={}", appId, familyId, njId, v.startDate, v.endDate);
            result.addAll(latest.guildRoomDayDetail(appId, familyId, njId, v.startDate, v.endDate));
        });
        getHistoryDateScope(startDate, endDate).ifPresent(v->{
            log.info("history.guildRoomDayDetail appId={},familyId={},njId={},startDate={},endDate={}", appId, familyId, njId, v.startDate, v.endDate);
            result.addAll(history.guildRoomDayDetail(appId, familyId, njId, v.startDate, v.endDate));
        });
        return result;
    }

    @Override
    public List<RoomCheckPlayerPo> guildRoomDayDetailCheckPlayer(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        return Collections.emptyList();
    }

    @Override
    public List<RoomDayCalendarEntity> roomCalendar(Integer appId, Long njId, Date startDate, Date endDate) {
        List<RoomDayCalendarEntity> result = new ArrayList<>();
        getTodayDateScope(startDate, endDate).ifPresent(v->{
            log.info("latest.roomCalendar appId={},njId={},startDate={},endDate={}", appId, njId, v.startDate, v.endDate);
            result.addAll(latest.roomCalendar(appId, njId, v.startDate, v.endDate));
        });
        getHistoryDateScope(startDate, endDate).ifPresent(v->{
            log.info("history.roomCalendar appId={},njId={},startDate={},endDate={}", appId, njId, v.startDate, v.endDate);
            result.addAll(history.roomCalendar(appId, njId, v.startDate, v.endDate));
        });
        return result;
    }

    @Override
    public RoomDayStatsSummaryRes roomDaySummary(Integer appId, Long njId, Date startDate, Date endDate) {
        BigDecimal income = BigDecimal.ZERO;
        int charm = 0;
        int seatOrder = 0;
        int hostCnt = 0;

        Optional<DateScope> todayDateScope = getTodayDateScope(startDate, endDate);
        if (todayDateScope.isPresent()) {
            DateScope v = todayDateScope.get();
            log.info("latest.roomDaySummary appId={},njId={},startDate={},endDate={}", appId, njId, v.startDate, v.endDate);
            RoomDayStatsSummaryRes lastRes = latest.roomDaySummary(appId, njId, v.startDate, v.endDate);
            if (lastRes != null) {
                if (StringUtils.isNotBlank(lastRes.getIncome())) {
                    income = income.add(new BigDecimal(lastRes.getIncome()));
                }
                if (lastRes.getCharm() != null) {
                    charm += lastRes.getCharm();
                }
                if (lastRes.getSeatOrder() != null) {
                    seatOrder += lastRes.getSeatOrder();
                }
                if (lastRes.getHostCnt() != null) {
                    hostCnt += lastRes.getHostCnt();
                }
            }

        }

        Optional<DateScope> historyDateScope = getHistoryDateScope(startDate, endDate);
        if (historyDateScope.isPresent()) {
            DateScope v = historyDateScope.get();
            log.info("history.roomDaySummary appId={},njId={},startDate={},endDate={}", appId, njId, v.startDate, v.endDate);
            RoomDayStatsSummaryRes historyRes = this.history.roomDaySummary(appId, njId, v.startDate, v.endDate);
            if (historyRes != null) {
                if (StringUtils.isNotBlank(historyRes.getIncome())) {
                    income = income.add(new BigDecimal(historyRes.getIncome()));
                }
                if (historyRes.getCharm() != null) {
                    charm += historyRes.getCharm();
                }
                if (historyRes.getSeatOrder() != null) {
                    seatOrder += historyRes.getSeatOrder();
                }
                if (historyRes.getHostCnt() != null) {
                    hostCnt += historyRes.getHostCnt();
                }
            }
        }

        RoomDayStatsSummaryRes result = new RoomDayStatsSummaryRes();
        result.setIncome(income.toString());
        result.setCharm(charm);
        result.setSeatOrder(seatOrder);
        result.setHostCnt(hostCnt);

        return result;
    }

    @Override
    public List<GuildRoomDayStats> guildRoomDayStats(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        Map<Long, GuildRoomDayStats> userIdMap = new HashMap<>();

        Optional<DateScope> todayDateScope = getTodayDateScope(startDate, endDate);
        if (todayDateScope.isPresent()) {
            DateScope v = todayDateScope.get();
            log.info("latest.guildRoomDayStats appId={},familyId={},njId={},startDate={},endDate={}", appId, familyId, njId, v.startDate, v.endDate);
            List<GuildRoomDayStats> latestList = latest.guildRoomDayStats(appId, familyId, njId, v.startDate, v.endDate);
            if (CollectionUtils.isNotEmpty(latestList)) {
                userIdMap = latestList.stream().collect(Collectors.toMap(GuildRoomDayStats::getNjId, l -> l));
            }
        }

        Optional<DateScope> historyDateScope = getHistoryDateScope(startDate, endDate);
        if (historyDateScope.isPresent()) {
            DateScope v = historyDateScope.get();
            log.info("history.guildRoomDayStats appId={},familyId={},njId={},startDate={},endDate={}", appId, familyId, njId, v.startDate, v.endDate);
            List<GuildRoomDayStats> historyList = history.guildRoomDayStats(appId, familyId, njId, v.startDate, v.endDate);
            if (CollectionUtils.isNotEmpty(historyList)) {
                for (GuildRoomDayStats roomStats : historyList) {
                    GuildRoomDayStats latest = userIdMap.get(roomStats.getNjId());
                    if (latest == null) {
                        userIdMap.put(roomStats.getNjId(), roomStats);
                        continue;
                    }

                    latest.setIncome(incomeSum(latest.getIncome(), roomStats.getIncome()).toString());
                    latest.setCharm(intSum(latest.getCharm(), roomStats.getCharm()));
                    latest.setSeatOrder(intSum(latest.getSeatOrder(), roomStats.getSeatOrder()));
                    latest.setCheckPlayerNumber(intSum(latest.getCheckPlayerNumber(), roomStats.getCheckPlayerNumber()));
                }
            }
        }

        return new ArrayList<>(userIdMap.values());
    }

    @Override
    public GuildRoomDayStatsSummaryRes guildRoomDaySummary(Integer appId, Long familyId, Long njId, Date startDate, Date endDate) {
        BigDecimal income = BigDecimal.ZERO;
        int charm = 0;
        int seatOrder = 0;
        int checkPlayerNumber = 0;

        Optional<DateScope> todayDateScope = getTodayDateScope(startDate, endDate);
        if (todayDateScope.isPresent()) {
            DateScope v = todayDateScope.get();
            log.info("latest.guildRoomDaySummary appId={},familyId={},njId={},startDate={},endDate={}", appId, familyId, njId, v.startDate, v.endDate);
            GuildRoomDayStatsSummaryRes lastRes = latest.guildRoomDaySummary(appId, familyId, njId, v.startDate, v.endDate);
            if (lastRes != null) {
                if (StringUtils.isNotBlank(lastRes.getIncome())) {
                    income = income.add(new BigDecimal(lastRes.getIncome()));
                }
                if (lastRes.getCharm() != null) {
                    charm += lastRes.getCharm();
                }
                if (lastRes.getSeatOrder() != null) {
                    seatOrder += lastRes.getSeatOrder();
                }
                if (lastRes.getCheckPlayerNumber() != null) {
                    checkPlayerNumber += lastRes.getCheckPlayerNumber();
                }
            }
        }

        Optional<DateScope> historyDateScope = getHistoryDateScope(startDate, endDate);
        if (historyDateScope.isPresent()) {
            DateScope v = historyDateScope.get();
            log.info("history.guildRoomDaySummary appId={},familyId={},njId={},startDate={},endDate={}", appId, familyId, njId, v.startDate, v.endDate);
            GuildRoomDayStatsSummaryRes historyRes = this.history.guildRoomDaySummary(appId, familyId, njId, v.startDate, v.endDate);
            if (historyRes != null) {
                if (StringUtils.isNotBlank(historyRes.getIncome())) {
                    income = income.add(new BigDecimal(historyRes.getIncome()));
                }
                if (historyRes.getCharm() != null) {
                    charm += historyRes.getCharm();
                }
                if (historyRes.getSeatOrder() != null) {
                    seatOrder += historyRes.getSeatOrder();
                }
                if (historyRes.getCheckPlayerNumber() != null) {
                    checkPlayerNumber += historyRes.getCheckPlayerNumber();
                }
            }
        }

        GuildRoomDayStatsSummaryRes result = new GuildRoomDayStatsSummaryRes();
        result.setIncome(income.toString());
        result.setCharm(charm);
        result.setSeatOrder(seatOrder);
        result.setCheckPlayerNumber(checkPlayerNumber);
        return result;
    }

    @Override
    public List<Long> roomDayStatsUserId(Integer appId, Long njId, Long userId, Date startDate, Date endDate) {
        Set<Long> result = new HashSet<>();
        getTodayDateScope(startDate, endDate).ifPresent(v->{
            log.info("latest.roomDayStatsUserId appId={},njId={},userId={},startDate={},endDate={}", appId, njId, userId, v.startDate, v.endDate);
            result.addAll(latest.roomDayStatsUserId(appId, njId, userId, v.startDate, v.endDate));
        });
        getHistoryDateScope(startDate, endDate).ifPresent(v->{
            log.info("history.roomDayStatsUserId appId={},njId={},userId={},startDate={},endDate={}", appId, njId, userId, v.startDate, v.endDate);
            result.addAll(history.roomDayStatsUserId(appId, njId, userId, v.startDate, v.endDate));
        });
        return new ArrayList<>(result);
    }

    @Override
    public List<RoomDayStatsDetailEntity> roomDayStatsDetail(Integer appId, Long njId, Long userId, Date startDate, Date endDate) {
        List<RoomDayStatsDetailEntity> result = new ArrayList<>();
        getTodayDateScope(startDate, endDate).ifPresent(v->{
            log.info("latest.roomDayStatsDetail appId={},njId={},userId={},startDate={},endDate={}", appId, njId, userId, v.startDate, v.endDate);
            result.addAll(latest.roomDayStatsDetail(appId, njId, userId, v.startDate, v.endDate));
        });
        getHistoryDateScope(startDate, endDate).ifPresent(v->{
            log.info("history.roomDayStatsDetail appId={},njId={},userId={},startDate={},endDate={}", appId, njId, userId, v.startDate, v.endDate);
            result.addAll(history.roomDayStatsDetail(appId, njId, userId, v.startDate, v.endDate));
        });
        return result;
    }

    @Override
    public List<RoomDayStatsEntity> roomDayStats(Integer appId, Long njId, Long userId, Date startDate, Date endDate) {
        Map<Long, RoomDayStatsEntity> userIdMap = new HashMap<>();

        Optional<DateScope> todayDateScope = getTodayDateScope(startDate, endDate);
        if (todayDateScope.isPresent()) {
            DateScope v = todayDateScope.get();
            log.info("latest.roomDayStats appId={},njId={},userId={},startDate={},endDate={}", appId, njId, userId, v.startDate, v.endDate);
            List<RoomDayStatsEntity> latestList = latest.roomDayStats(appId, njId, userId, v.startDate, v.endDate);
            if (CollectionUtils.isNotEmpty(latestList)) {
                userIdMap = latestList.stream().collect(Collectors.toMap(RoomDayStatsEntity::getUserId, l -> l));
            }
        }

        Optional<DateScope> historyDateScope = getHistoryDateScope(startDate, endDate);
        if (historyDateScope.isPresent()) {
            DateScope v = historyDateScope.get();
            log.info("history.roomDayStats appId={},njId={},userId={},startDate={},endDate={}", appId, njId, userId, v.startDate, v.endDate);
            List<RoomDayStatsEntity> historyList = history.roomDayStats(appId, njId, userId, v.startDate, v.endDate);
            if (CollectionUtils.isNotEmpty(historyList)) {
                for (RoomDayStatsEntity roomStats : historyList) {
                    RoomDayStatsEntity latest = userIdMap.get(roomStats.getUserId());
                    if (latest == null) {
                        userIdMap.put(roomStats.getUserId(), roomStats);
                        continue;
                    }

                    latest.setIncome(incomeSum(latest.getIncome(), roomStats.getIncome()).toString());
                    latest.setCharm(intSum(latest.getCharm(), roomStats.getCharm()));
                    latest.setSeatOrder(intSum(latest.getSeatOrder(), roomStats.getSeatOrder()));
                    latest.setHostCnt(intSum(latest.getHostCnt(), roomStats.getHostCnt()));
                }
            }
        }

        return new ArrayList<>(userIdMap.values());
    }

    private BigDecimal incomeSum(String a, String b){
        BigDecimal income = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(a)) {
            income = new BigDecimal(a);
        }
        if (StringUtils.isNotBlank(b)) {
            income = income.add(new BigDecimal(b));
        }
        return income;
    }

    private Integer intSum(Integer a, Integer b){
        int sum = 0;
        if (a != null) {
            sum += a;
        }
        if (b != null) {
            sum += b;
        }
        return sum;
    }

    /**
     * 获取时间范围中 startDate至D-2的范围
     * 不考虑endDate大于今天的情况
     * @param startDate
     * @param endDate
     * @return
     */
    private Optional<DateScope> getHistoryDateScope(Date startDate, Date endDate){
        if (startDate == null || endDate == null) {
            return Optional.empty();
        }

        Date today = new Date();
        int yesterdayValue = MyDateUtil.getDateDayValue(DateUtil.getDayBefore(today, 1));
        Integer endValue = MyDateUtil.getDateDayValue(endDate);
        Integer startValue = MyDateUtil.getDateDayValue(startDate);
        if (startValue >= yesterdayValue) {
            return Optional.empty();
        }

        if (endValue >= yesterdayValue) {
            //包含D-1或者D, 返回startDate-D-2
            return Optional.of(new DateScope()
                    .setStartDate(DateUtil.getDayStart(startDate))
                    .setEndDate(DateUtil.getDayEnd(DateUtil.getDayBefore(today, 2)))
            );
        }

        return Optional.of(new DateScope().setStartDate(startDate).setEndDate(endDate));
    }

    /**
     * 获取时间范围中 D-1至D的范围
     * 不考虑endDate大于今天的情况
     * @param startDate
     * @param endDate
     * @return
     */
    private Optional<DateScope> getTodayDateScope(Date startDate, Date endDate){
        if (startDate == null || endDate == null) {
            return Optional.empty();
        }

        Date today = new Date();
        Integer todayValue = MyDateUtil.getDateDayValue(today);
        int yesterdayValue = MyDateUtil.getDateDayValue(DateUtil.getDayBefore(today, 1));
        Integer endValue = MyDateUtil.getDateDayValue(endDate);
        Integer startValue = MyDateUtil.getDateDayValue(startDate);
        if (startValue >= yesterdayValue) {
            return Optional.of(new DateScope()
                    .setStartDate(startDate)
                    .setEndDate(endDate));
        }

        //endValue = yesterdayValue, 返回 yesterdayValue的开始和结束
        if (endValue == yesterdayValue) {
            Date yesterday = MyDateUtil.getDayValueDate(yesterdayValue);
            return Optional.of(new DateScope()
                    .setStartDate(DateUtil.getDayStart(yesterday))
                    .setEndDate(DateUtil.getDayEnd(yesterday))
            );
        }

        if (endValue.equals(todayValue)) {
            Date yesterday = MyDateUtil.getDayValueDate(yesterdayValue);
            return Optional.of(new DateScope()
                    .setStartDate(DateUtil.getDayStart(yesterday))
                    .setEndDate(DateUtil.getDayEnd(today))
            );
        }

        return Optional.empty();
    }

    /**
     * 时间范围
     */
    @Data
    @Accessors(chain = true)
    private static class DateScope{
        private Date startDate;
        private Date endDate;
    }

}
