package fm.lizhi.ocean.wave.platform.app.platform.version.datastore.redis;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class WaveVersionRedisKey {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 获取灰度自动递增执行key, 用于标记灰度自动递增任务是否已经执行
     *
     * @param versionId 版本ID
     * @return 灰度自动递增执行key
     */
    public static String getGrayAutoIncrExecuteKey(long versionId) {
        String date = LocalDateTime.now().format(DATE_TIME_FORMATTER);
        return "WAVE_VERSION_GRAY_AUTO_INCR_EXECUTE_" + date + "_" + versionId;
    }
}
