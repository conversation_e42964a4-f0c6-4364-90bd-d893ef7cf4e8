package fm.lizhi.ocean.wave.platform.app.platform.manager.checkin;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.platform.api.platform.bean.*;
import fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInScheduledConstants;
import fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInStatusConstant;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestCheckInOperateCore;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestIncrIncomeAndTask;
import fm.lizhi.ocean.wave.platform.api.platform.response.ResponseGetEffectiveCheckInConfig;
import fm.lizhi.ocean.wave.platform.api.platform.service.WaveCheckInService;
import fm.lizhi.ocean.wave.platform.app.platform.config.PlatformConfig;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.dao.checkin.WaveCheckInDao;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.entity.checkin.WaveCheckInLightGiftRecord;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.entity.checkin.WaveCheckInRecord;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.entity.checkin.WaveCheckInSchedule;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.entity.checkin.WaveCheckInUserTask;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.entity.checkin.WaveCheckInAllMicGiftRecord;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.mapper.checkin.WaveCheckInScheduleMapper;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.redis.checkin.WaveCheckInRedisDao;
import fm.lizhi.ocean.wave.platform.app.platform.model.converter.checkin.WaveCheckInServiceConverter;
import fm.lizhi.ocean.wave.platform.app.platform.model.dto.checkin.*;
import fm.lizhi.ocean.wave.platform.common.utils.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 麦序福利表
 * <AUTHOR>
 */
@Slf4j
@Component
public class WaveCheckInManager{

    @Autowired
    private WaveCheckInServiceConverter checkInServiceConverter;

    @Autowired
    private WaveCheckInDao checkInDao;

    @Autowired
    private WaveCheckInRedisDao checkInRedisDao;

    @Autowired
    private WaveCheckInConfigManager waveCheckInConfigManager;

    @Autowired
    private WaveCheckInScheduleMapper waveCheckInScheduleMapper;

    @Autowired
    private WaveCheckInTaskManager checkInTaskManager;

    @Autowired
    private PlatformConfig platformConfig;

    @Autowired
    private WaveCheckInAllMicManager checkInAllMicManager;

    /**
     * 初始化用户记录和用户任务
     * @return 用户记录ID
     */
    public Long initRecordAndTask(long scheduleId, long userId, long roomId, int appId) {
        WaveCheckInSchedule checkInSchedule = checkInDao.getCheckInSchedule(scheduleId);
        Long familyId = checkInSchedule.getFamilyId();
        int taskRule = waveCheckInConfigManager.getEffectiveCheckInTaskType(appId, familyId, roomId);
        return checkInDao.initRecordAndTask(checkInSchedule, userId, roomId, taskRule);
    }


    /**
     * 增加收入和处理任务
     */
    public Boolean increaseIncomeAndTask(RequestIncrIncomeAndTask request) {
        try {
            log.info("increaseIncomeAndTask request: {}", JSON.toJSONString(request));
            Optional<WaveCheckInIncrIncomeAndTaskContext> context = getWaveCheckInIncrIncomeAndTaskContext(request);
            if (context.isPresent()){
                // 写入相关的数据
                checkInTaskManager.processor(context.get());

                // 发送全麦收礼待分配罗马推送
                checkInAllMicManager.sendAllMicGiftPush(context.get());
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("increaseIncomeAndTask fail, roomId:{}, scheduleId:{}, transactionId:{} ", request.getRoomId(), request.getScheduleId(), request.getTransactionId(), e);
            return false;
        }

    }


    private Optional<WaveCheckInIncrIncomeAndTaskContext> getWaveCheckInIncrIncomeAndTaskContext(RequestIncrIncomeAndTask request) {
        WaveCheckInSchedule schedule = checkInDao.getCheckInSchedule(request.getScheduleId());
        if (null == schedule){
            log.error("increaseIncomeAndTask schedule not found, scheduleId:{}", request.getScheduleId());
            return Optional.empty();
        }

        WaveCheckInRecord checkInRecord = checkInDao.getCheckInRecord(schedule.getId(), request.getRecTargetUserId(), true);
        if (null == checkInRecord){
            log.error("increaseIncomeAndTask checkInRecord not found, scheduleId:{}, userId:{}", request.getScheduleId(), request.getRecTargetUserId());
            return Optional.empty();
        }

        WaveCheckInUserTask userTask = checkInDao.getCheckInUserTask(checkInRecord.getId());
        if (null == userTask){
            log.error("increaseIncomeAndTask userTask not found, scheduleId:{}, userId:{}", request.getScheduleId(), request.getRecTargetUserId());
            return Optional.empty();
        }

        ResponseGetEffectiveCheckInConfig checkInConfig = waveCheckInConfigManager.getEffectiveCheckInConfig(request.getAppId(), request.getFamilyId(), request.getRoomId());

        // 收光
        List<WaveCheckInLightGiftRecord> lightGiftRecords = checkInTaskManager.processLightGift(request, checkInConfig.getLightGiftConfig());

        // 全麦
        Optional<WaveCheckInAllMicGiftRecord> allMicGiftRecord = checkInAllMicManager.processAllMicGift(request, checkInConfig.getAllMicGiftConfig());

        return Optional.ofNullable(checkInServiceConverter.buildWaveCheckInIncrIncomeAndTaskContext(request, schedule, checkInRecord, userTask, lightGiftRecords, allMicGiftRecord.orElse(null)));
    }

    /**
     * 调账核心
     */
    @Transactional
    public WaveCheckInOperateCoreDto checkInOperateCore(WaveCheckInOperateCoreContext context) {
        String contextJson = JsonUtil.dumps(context);
        log.info("checkInOperateCore context: {}", contextJson);
        RequestCheckInOperateCore request = context.getRequest();
        CheckInOperateBean checkInOperateBean = request.getCheckInOperateBean();

        try {
            CheckInScheduleBean schedule = context.getSchedule();

            // 更新及新增打卡记录
            List<CheckInRecordBean> allChangeRecords = checkInDao.checkInDataUpdate(context.getCheckInRecordList(),
                    checkInOperateBean, context.getScheduleCheckInStatus(), context.getTaskRule(), schedule.getNjId());

            // 任务计算结果，必须是基于有任务记录的前提，才会计算，同时因为调账涉及到新增和删除、修改的用户，因此只能依赖更新后的原子性列表
            List<WaveCheckInCalcTaskResultDto> calcTaskResultList = getCalcTaskResult(allChangeRecords, context);

            // 更新任务结果
            Boolean success = checkInDao.updateUserTask(calcTaskResultList, true);
            if (!success){
                log.warn("checkInOperateCore updateTask fail.");
                throw new RuntimeException("checkInOperateCore updateTask fail");
            }

            List<WaveCheckInCalcHistoryResultDto> historyResultList = checkInTaskManager.calcHistory(calcTaskResultList, context.getSchedule());
            Boolean historyTaskRes = checkInDao.updateHistoryTask(historyResultList);
            if (!historyTaskRes){
                log.warn("checkInOperateCore updateHistoryTask fail");
                throw new RuntimeException("checkInOperateCore updateHistoryTask fail");
            }

            return new WaveCheckInOperateCoreDto()
                    .setSuccess(true)
                    .setCalcTaskResultList(calcTaskResultList);

        }catch (Exception e){
            log.error("checkInOperateCore fail", e);
            throw new RuntimeException("checkInOperateCore fail", e);
        }
    }



    /**
     * 获取任务计算结果
     */
    private List<WaveCheckInCalcTaskResultDto> getCalcTaskResult(List<CheckInRecordBean> allChangeRecords, WaveCheckInOperateCoreContext context) {
        // 调账更新的用户
        Map<Long, CheckInOperateInfoBean> updateBeanMap = context.getCheckInUpdateBeanMap();
        // 调账新增的用户
        Map<Long, CheckInNewUserOperateBean> insertBeanMap = context.getCheckInInsertBeanMap();
        // 档期
        CheckInScheduleBean schedule = context.getSchedule();
        List<CheckInUserTaskBean> userTaskList = checkInDao.getCheckInUserTaskByRecordIds(
                allChangeRecords.stream().map(CheckInRecordBean::getId).collect(Collectors.toList()),
                true
        );

        // 用户任务
        Map<Long, CheckInUserTaskBean> userTaskByRecordIdMap = userTaskList.stream().collect(Collectors.toMap(CheckInUserTaskBean::getRecordId, Function.identity(), (k1, k2) -> k1));

        return allChangeRecords.stream().map(record -> {
            long charmDiff = 0;
            long taskProcessDiff = 0;
            long taskScoreDiff = 0;
            if (updateBeanMap.containsKey(record.getId())){
                // 更新用户
                CheckInOperateInfoBean operateInfoBean = updateBeanMap.get(record.getId());
                charmDiff = operateInfoBean.getCharmDiffValue();
                taskProcessDiff = Math.toIntExact(operateInfoBean.getTaskProgressDiff());
                taskScoreDiff = operateInfoBean.getTaskScoreDiff();
            }else if (insertBeanMap.containsKey(record.getUserId())){
                // 新增用户
                CheckInNewUserOperateBean newUserOperateBean = insertBeanMap.get(record.getUserId());
                charmDiff = newUserOperateBean.getCharmDiffValue();
                taskProcessDiff = newUserOperateBean.getTaskProgressDiff();
                taskScoreDiff = newUserOperateBean.getTaskScoreDiff();
            }

            // 无变更，跳过
            if (charmDiff == 0 && taskProcessDiff == 0 && taskScoreDiff == 0) {
                log.info("checkInOperateCore calc task skip, charmDiff or taskProcessDiff or taskScoreDiff is null. recordId:{}, userId: {}, roomId: {}, charmDiff:{}, taskProcessDiff:{}, taskScoreDiff:{}",
                        record.getId(), record.getUserId(), record.getRemark(), charmDiff, taskProcessDiff, taskScoreDiff);
                return null;
            }

            log.info("getCalcTaskResult recordId:{}, userId:{}, charmDiff:{}, taskProcessDiff:{}, taskScoreDiff:{}",
                    record.getId(), record.getUserId(), charmDiff, taskProcessDiff, taskScoreDiff);
            WaveCheckInCalcTaskParamDto calcTaskParamDto = checkInServiceConverter.buildWaveCheckInCalcTaskDto(
                    record, schedule, MapUtils.getObject(userTaskByRecordIdMap, record.getId()),
                    charmDiff, taskProcessDiff, taskScoreDiff, context.getRequest().getOperateUserId()
            );

            // 计算任务
            WaveCheckInCalcTaskResultDto calcTaskResult = checkInTaskManager.calcTask(calcTaskParamDto);
            if (calcTaskResult == null || calcTaskResult.getCalcChangeRecordDto() == null) {
                log.info("checkInOperateCore calc task skip, WaveCheckInCalcTaskResultDto or WaveCheckInCalcTaskDto is null. recordId:{}, userId: {}, roomId: {}", record.getId(), record.getUserId(), record.getRemark());
                return null;
            }

            return calcTaskResult;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }


    /**
     * 获取用户打卡记录
     */
    public List<CheckInRecordBean> getCheckInRecordByIds(List<Long> recordIds) {
        
        List<WaveCheckInRecord> checkInRecordList = checkInDao.getCheckInRecordByIds(recordIds);

        return checkInServiceConverter.convertCheckInRecordBeans(checkInRecordList);

    }

    /**
     * 获取版本号
     */
    public Long getCheckInVersion(int appId, long scheduleId) {
        return checkInRedisDao.getCheckInVersion(appId, scheduleId);
    }




    /**
     * 调账前的预处理
     */
    public Result<WaveCheckInOperateCoreContext> preOperateCore(RequestCheckInOperateCore request) {

        CheckInOperateBean checkInOperateBean = request.getCheckInOperateBean();
        boolean enableOperateBySettlement = platformConfig.getCheckInDefaultConfig().isEnableOperateBySettlement();

        WaveCheckInSchedule checkInSchedule = checkInDao.getCheckInSchedule(checkInOperateBean.getScheduleId());
        if (null == checkInSchedule){
            log.error("preOperateCore checkInSchedule is null, scheduleId:{}", checkInOperateBean.getScheduleId());
            throw new RuntimeException("preOperateCore checkInSchedule is null");
        }

        // 档期结算中，不允许调账
        Boolean undoneSettlementFlat = checkInRedisDao.getCheckInUndoneSettlementJobFlat(checkInSchedule.getStartTime());
        if (undoneSettlementFlat &&!enableOperateBySettlement) {
            log.warn("preOperateCore fail, settlement is running. cannot operate. scheduleId:{}", checkInSchedule.getId());
            return RpcResult.fail(WaveCheckInService.CHECK_IN_OPERATE_CORE_UNDONE_SETTLEMENT);
        }

        List<WaveCheckInRecord> waveCheckInRecords = checkInDao.getRecordsByScheduleId(checkInOperateBean.getScheduleId());

        // 预处理一下新增的用户是否已经存在(主持在保存过程中，可能会因为用户收礼导致档期和任务已经初始化)
        preDealWithOperateUsers(request, waveCheckInRecords);


        // 本次调账的用户 Map: recordId, bean
        Map<Long, CheckInOperateInfoBean> checkInUpdateBeanMap =
                Optional.ofNullable(checkInOperateBean.getInfoList()).filter(CollUtil::isNotEmpty)
                        .map(infoList -> infoList.stream().collect(Collectors.toMap(CheckInOperateInfoBean::getRecordId, bean -> bean)))
                        .orElse(new HashMap<>());

        // 本次新增的用户 Map: userId, bean
        Map<Long, CheckInNewUserOperateBean> checkInInsertBeanMap =
                Optional.ofNullable(checkInOperateBean.getNewUserCheckInList()).filter(CollUtil::isNotEmpty)
                        .map(newUserCheckInList -> newUserCheckInList.stream().collect(Collectors.toMap(CheckInNewUserOperateBean::getUserId, bean -> bean)))
                        .orElse(new HashMap<>());

        boolean alreadyCheckIn = false;
        Iterator<WaveCheckInRecord> iterator = waveCheckInRecords.iterator();
        while (iterator.hasNext()) {
            WaveCheckInRecord record = iterator.next();
            CheckInOperateInfoBean checkInBean = checkInUpdateBeanMap.get(record.getId());
            if (checkInBean == null) {
                // 删除掉，本次没更新
                iterator.remove();
                continue;
            }
            //只有可以调账的时候才需要修改这个值
            record.setStatus(checkInBean.getStatus());
            record.setScheduled(CheckInScheduledConstants.getScheduled(checkInBean.getScheduled()));
            record.setCharmValue(record.getOriginalValue() + checkInBean.getCharmDiffValue());
            record.setCharmDiffValue(Math.toIntExact(checkInBean.getCharmDiffValue()));
            record.setRemark(checkInBean.getRemark());
            //只有一个是打卡状态，就是已打卡
            if (record.getStatus() == CheckInStatusConstant.CHECK_IN) {
                alreadyCheckIn = true;
            }
            Boolean undoneSettlementFlatByUser = checkInRedisDao.getCheckInUserUndoneSettlementFlat(new Date(checkInOperateBean.getScheduleStartTime())
                    , record.getUserId(), checkInSchedule.getId());


            if (undoneSettlementFlatByUser && !enableOperateBySettlement) {
                log.warn("preOperateCore fail, settlement is running. cannot operate. userId:{}, recordId:{}, scheduleId:{}",
                        record.getUserId(), record.getId(), checkInSchedule.getId());
                return RpcResult.fail(WaveCheckInService.CHECK_IN_OPERATE_CORE_UNDONE_SETTLEMENT);
            }
        }

        waveCheckInRecords = CollUtil.isEmpty(checkInOperateBean.getInfoList()) ? new ArrayList<>() : waveCheckInRecords;
        log.info("preOperateCore update, roomId:{}, scheduleId:{}, updateUserIds:{}, insertUserIds:{}, deleteUserIds:{}",
                checkInOperateBean.getRoomId(), checkInOperateBean.getScheduleId(),
                waveCheckInRecords.stream().map(WaveCheckInRecord::getUserId).collect(Collectors.toList()),
                checkInInsertBeanMap.keySet(), request.getCheckInOperateBean().getWaitDelRecordIds()
        );

        // 构建上下文
        WaveCheckInOperateCoreContext coreContext = new WaveCheckInOperateCoreContext();
        coreContext.setRequest(request);
        coreContext.setTaskRule(waveCheckInConfigManager.getEffectiveCheckInTaskType(request.getAppId(), checkInSchedule.getFamilyId(), checkInOperateBean.getRoomId()));
        coreContext.setCheckInUpdateBeanMap(checkInUpdateBeanMap);
        coreContext.setCheckInInsertBeanMap(checkInInsertBeanMap);
        coreContext.setSchedule(checkInServiceConverter.convertCheckInScheduleBean(checkInSchedule));
        coreContext.setCheckInRecordList(checkInServiceConverter.convertCheckInRecordBeans(waveCheckInRecords));
        coreContext.setScheduleCheckInStatus(alreadyCheckIn ? CheckInStatusConstant.CHECK_IN : CheckInStatusConstant.NO_CHECK_IN);
        return RpcResult.success(coreContext);
    }


    private void preDealWithOperateUsers(RequestCheckInOperateCore request, List<WaveCheckInRecord> existCheckInRecord) {
        CheckInOperateBean checkInOperateBean = request.getCheckInOperateBean();
        List<CheckInNewUserOperateBean> newUserCheckInList = checkInOperateBean.getNewUserCheckInList();
        if (CollUtil.isEmpty(newUserCheckInList)) {
            return;
        }

        List<CheckInOperateInfoBean> infoList = checkInOperateBean.getInfoList();
        Map<Long, WaveCheckInRecord> existRecordMap = existCheckInRecord.stream().collect(Collectors.toMap(WaveCheckInRecord::getUserId, Function.identity(), (e1, e2) -> e1));

        Iterator<CheckInNewUserOperateBean> newUserOperateBeanIterator = newUserCheckInList.iterator();
        while (newUserOperateBeanIterator.hasNext()) {
            CheckInNewUserOperateBean next = newUserOperateBeanIterator.next();
            // 已经存在记录
            if (existRecordMap.containsKey(next.getUserId())) {
                WaveCheckInRecord record = existRecordMap.get(next.getUserId());
                CheckInOperateInfoBean operateInfoBean = new CheckInOperateInfoBean();
                operateInfoBean.setRecordId(record.getId());
                operateInfoBean.setOriginalValue(next.getOriginalValue());
                operateInfoBean.setStatus(next.getStatus());
                operateInfoBean.setCharmDiffValue(next.getCharmDiffValue());
                operateInfoBean.setTaskScore(next.getTaskScore());
                operateInfoBean.setTaskScoreDiff(next.getTaskScoreDiff());
                operateInfoBean.setTaskProgress(next.getTaskProgress());
                operateInfoBean.setTaskProgressDiff(next.getTaskProgressDiff());
                operateInfoBean.setScheduled(next.getScheduled());
                operateInfoBean.setRemark(next.getRemark());
                infoList.add(operateInfoBean);
                // 移除
                newUserOperateBeanIterator.remove();
                log.info("preDealWithInsertUser checkInRecord is exist :{}, newUserCheckInList is remove user:{}", operateInfoBean, next.getUserId());
            }
        }

        // 写回去请求参数
        checkInOperateBean.setInfoList(infoList);
        checkInOperateBean.setNewUserCheckInList(newUserCheckInList);
    }

    /**
     * 自增调账版本号
     */
    public Long incrCheckInVersion(int appId, Long scheduleId) {
        return checkInRedisDao.incrCheckInVersion(appId, scheduleId);
    }


    /**
     * 修改档期表主持
     *
     * @param scheduleId 档期ID
     * @param hostId     主持人ID
     * @return 结果
     */
    public boolean updateScheduleHost(long scheduleId, Long hostId) {
        if (null == hostId || hostId <= 0){
            log.warn("updateScheduleHost hostId is null or <= 0, scheduleId:{}, hostId:{}", scheduleId, hostId);
            return false;
        }

        WaveCheckInSchedule schedule = checkInDao.getCheckInSchedule(scheduleId);
        if (schedule == null) {
            log.warn("updateScheduleHost schedule is null, scheduleId:{}", scheduleId);
            return false;
        }

        Long oldHostId = schedule.getHostId();
        schedule.setHostId(hostId);
        boolean success = waveCheckInScheduleMapper.updateByPrimaryKey(schedule) > 0;
        if (success){
            HashSet<Long> sendUserIds = CollUtil.newHashSet();
            Optional.ofNullable(oldHostId).ifPresent(sendUserIds::add);
            Optional.of(hostId).ifPresent(sendUserIds::add);
            checkInAllMicManager.sendAllMicGiftPush(checkInServiceConverter.convertCheckInScheduleBean(schedule), sendUserIds);
        }
        return success;
    }


}
