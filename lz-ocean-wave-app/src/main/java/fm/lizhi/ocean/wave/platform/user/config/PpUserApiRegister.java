package fm.lizhi.ocean.wave.platform.user.config;

import fm.lizhi.ocean.lamp.common.generic.annotation.ScanBusinessProviderAPI;
import fm.lizhi.pp.content.assistant.api.HotWordsManagementService;
import fm.lizhi.pp.mall.api.BubbleMallService;
import fm.lizhi.pp.medal.api.MedalNewService;
import fm.lizhi.pp.oss.api.CommentStyleManagerService;
import fm.lizhi.pp.security.api.ban.PpUserBanService;
import fm.lizhi.pp.security.api.sms.H5TokenService;
import fm.lizhi.pp.social.api.GrowRelationService;
import fm.lizhi.pp.social.api.RelationShipService;
import fm.lizhi.pp.user.account.common.api.PpNewCommonService;
import fm.lizhi.pp.user.account.relation.api.PpUserRelationService;
import fm.lizhi.pp.user.account.user.api.PpNewUserService;
import fm.lizhi.pp.user.account.verify.api.UserVerifyProxyService;
import fm.lizhi.pp.vip.api.AnchorGrowthSystemService;
import fm.lizhi.pp.vip.api.AvatarWidgetService;
import fm.lizhi.pp.vip.api.PpVipService;
import fm.lizhi.pp.wealth.api.PpWealthService;
import fm.pp.family.api.ContractService;
import fm.pp.family.api.FamilyService;
import fm.pp.family.api.PlayerSignService;
import org.springframework.context.annotation.Configuration;
import pp.fm.lizhi.live.data.api.UserDataService;
import pp.fm.lizhi.live.pp.player.api.PpPlayerAuthService;
import pp.fm.lizhi.live.usergroup.api.UserGroupService;
import pp.fm.lizhi.pp.glory.api.PpUserGloryService;
import pp.fm.lizhi.pp.relation.api.PpRelationService;
import pp.fm.lizhi.pp.visitor.api.PpUserVisitorService;

@Configuration
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpNewUserService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = pp.fm.lizhi.live.data.api.UserBehaviorService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpRelationService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpUserBanService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = H5TokenService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = UserVerifyProxyService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpNewCommonService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpPlayerAuthService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = FamilyService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = AnchorGrowthSystemService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpWealthService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = MedalNewService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpUserVisitorService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = GrowRelationService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = RelationShipService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpUserGloryService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = UserDataService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = ContractService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = HotWordsManagementService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpUserRelationService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PpVipService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = fm.lizhi.pp.security.api.ban.PpUserBanService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = AvatarWidgetService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = CommentStyleManagerService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = BubbleMallService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = UserGroupService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PlayerSignService.class),
})
public class PpUserApiRegister {

}
