package fm.lizhi.ocean.wave.platform.app.common.model.result;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import fm.lizhi.ocean.wave.platform.app.common.model.dto.FeiShuPersonDTO;
import lombok.Data;

import java.beans.Transient;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 飞书多维表格搜索记录结果. 参考
 * <a href="https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/bitable-v1/app-table-record/search">
 * 查询记录
 * </a>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeiShuBiTableSearchRecordResult {

    /**
     * 错误码, 非 0 取值表示失败
     */
    private Integer code;

    /**
     * 错误描述
     */
    private String msg;

    /**
     * 数据
     */
    private Data data;

    /**
     * 是否成功
     */
    @Transient
    public boolean isSuccess() {
        return code != null && code.equals(0);
    }

    /**
     * 是否失败
     */
    @Transient
    public boolean isFailure() {
        return !isSuccess();
    }

    /**
     * 数据
     */
    @lombok.Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Data {

        /**
         * record 结果列表
         */
        private List<Item> items;

        /**
         * 是否还有更多项
         */
        @JsonProperty("has_more")
        private Boolean hasMore;

        /**
         * 分页标记，当 has_more 为 true 时，会同时返回新的 page_token，否则不返回 page_token
         */
        @JsonProperty("page_token")
        private String pageToken;

        /**
         * 记录总数
         */
        private Integer total;

        /**
         * record 结果
         */
        @lombok.Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Item {

            /**
             * 记录字段
             */
            private Map<String, Object> fields;

            /**
             * 记录 ID
             */
            @JsonProperty("record_id")
            private String recordId;

            /**
             * 创建人
             */
            @JsonProperty("created_by")
            private FeiShuPersonDTO createdBy;

            /**
             * 创建时间
             */
            @JsonProperty("created_time")
            private Date createdTime;

            /**
             * 修改人
             */
            @JsonProperty("last_modified_by")
            private FeiShuPersonDTO lastModifiedBy;

            /**
             * 最近更新时间
             */
            @JsonProperty("last_modified_time")
            private Date lastModifiedTime;

            /**
             * 记录分享链接(批量获取记录接口将返回该字段)
             */
            @JsonProperty("shared_url")
            private String sharedUrl;

            /**
             * 记录链接(检索记录接口将返回该字段)
             */
            @JsonProperty("record_url")
            private String recordUrl;
        }
    }
}
