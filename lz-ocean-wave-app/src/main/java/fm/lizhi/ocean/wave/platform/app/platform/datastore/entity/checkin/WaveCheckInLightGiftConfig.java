package fm.lizhi.ocean.wave.platform.app.platform.datastore.entity.checkin;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 麦序福利收光奖励配置
 *
 * @date 2024-12-30 10:57:09
 */
@Table(name = "`wave_check_in_light_gift_config`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WaveCheckInLightGiftConfig {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 房间ID
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 是否开启收光奖励规则, 0-不启用, 1-启用
     */
    @Column(name= "`enabled`")
    private Boolean enabled;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", familyId=").append(familyId);
        sb.append(", roomId=").append(roomId);
        sb.append(", enabled=").append(enabled);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}