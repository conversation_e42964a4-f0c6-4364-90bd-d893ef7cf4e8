package fm.lizhi.ocean.wave.platform.app.platform.manager.checkin.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.ocean.wave.platform.api.platform.bean.CheckInRecordBean;
import fm.lizhi.ocean.wave.platform.api.platform.bean.CheckInScheduleBean;
import fm.lizhi.ocean.wave.platform.api.platform.bean.CheckInUserTaskBean;
import fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInTaskEnum;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.entity.checkin.WaveCheckInUserTask;
import fm.lizhi.ocean.wave.platform.app.platform.model.dto.checkin.WaveCheckInCalcHistoryResultDto;
import fm.lizhi.ocean.wave.platform.app.platform.model.dto.checkin.WaveCheckInCalcTaskParamDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TaskRuleStrategyExecutor {

    private final Map<Integer, TaskRuleStrategy> taskRuleStrategyMap = new HashMap<>();

    @Autowired
    private ScoreBasedTaskStrategy scoreBasedTaskStrategy;

    @Autowired
    private InsufficientScoreDeductionStrategy insufficientScoreDeductionStrategy;

    @Autowired
    private FullTaskCompletionStrategy fullTaskCompletionStrategy;


    @PostConstruct
    public void init() {
        taskRuleStrategyMap.put(CheckInTaskEnum.SCORE_BASED_TASKS.getType(), scoreBasedTaskStrategy);
        taskRuleStrategyMap.put(CheckInTaskEnum.INSUFFICIENT_SCORE_DEDUCTION.getType(), insufficientScoreDeductionStrategy);
        taskRuleStrategyMap.put(CheckInTaskEnum.FULL_TASK_COMPLETION.getType(), fullTaskCompletionStrategy);
    }


    /**
     * 任务计算
     * @param userTask
     * @param dto
     * @return
     */
    public CheckInUserTaskBean execute(CheckInUserTaskBean userTask, WaveCheckInCalcTaskParamDto dto) {
        Integer taskRuleType = userTask.getTaskRule();
        TaskRuleStrategy strategy = taskRuleStrategyMap.get(taskRuleType);
        if (strategy != null) {
            return strategy.applyRule(userTask, dto);
        } else {
            throw new IllegalArgumentException("execute No strategy found for task rule: " + taskRuleType);
        }
    }


    /**
     * 计算今日未完成分数
     */
    public List<Long> calcTaskDailyUnDone(WaveCheckInUserTask userTask, List<Long> currentTaskDailyUnDone){
        if (CollUtil.isEmpty(currentTaskDailyUnDone)){
            log.info(" calcTaskDailyUnDone currentTaskDailyUnDone is empty. recordId: {}, taskId: {}, userId:{} ", userTask.getRecordId(), userTask.getId(), userTask.getUserId());
            currentTaskDailyUnDone = CollUtil.newArrayList(0L);
        }

        Integer taskRuleType = userTask.getTaskRule();
        TaskRuleStrategy strategy = taskRuleStrategyMap.get(taskRuleType);
        if (strategy != null) {
            return strategy.calcTaskDailyUnDone(userTask, currentTaskDailyUnDone);
        } else {
            throw new IllegalArgumentException("calcTaskDailyUnDone No strategy found for task rule: " + taskRuleType);
        }
    }


    /**
     * 计算历史记录
     *
     */
    public WaveCheckInCalcHistoryResultDto calcHistory(CheckInUserTaskBean userTask, CheckInRecordBean checkInRecord, CheckInScheduleBean schedule) {
        boolean isCurrentSchedule = DateUtil.isIn(schedule.getStartTime(), DateUtil.beginOfHour(new Date()), DateUtil.endOfHour(new Date()));
        if (isCurrentSchedule){
            // 当前档期，不需要处理
            return null;
        }

        Integer taskRuleType = userTask.getTaskRule();
        TaskRuleStrategy strategy = taskRuleStrategyMap.get(taskRuleType);
        if (strategy != null) {
            WaveCheckInCalcHistoryResultDto resultDto = strategy.calcHistory(userTask, checkInRecord, schedule);
            if (resultDto == null){
                log.warn("calcHistory fail, taskRuleType:{}, userId:{}, scheduleId:{}, roomId:{}", taskRuleType, userTask.getId(), schedule.getId(), checkInRecord.getRoomId());
                return null;
            }
            resultDto.setSchedule(schedule);
            resultDto.setCheckInRecord(checkInRecord);
            log.info("calcHistory success, resultDto:{}", JSONObject.toJSONString(resultDto));
            return resultDto;
        } else {
            throw new IllegalArgumentException("calcHistory No strategy found for task rule: " + taskRuleType);
        }
    }


    /**
     * 是否扣除麦序分
     */
    public Boolean isDeductSeatScore(WaveCheckInUserTask userTask) {

        Integer taskRuleType = userTask.getTaskRule();
        TaskRuleStrategy strategy = taskRuleStrategyMap.get(taskRuleType);
        if (strategy != null) {
            return strategy.isDeductSeatScore(userTask.getTaskScore(), userTask.getTaskProgress());
        } else {
            throw new IllegalArgumentException("isDeductSeatScore No strategy found for task rule: " + taskRuleType);
        }
    }


    /**
     * 预计算任务分变动（预期抵扣分数）
     * 只是档期进行中的预计算提供给前端展示，不代表最终结算结果，不能以这个方法为最终计算结果
     */
    public String preCalcTaskDailyScoreChange(WaveCheckInUserTask userTask){
        Integer taskRuleType = userTask.getTaskRule();
        TaskRuleStrategy strategy = taskRuleStrategyMap.get(taskRuleType);
        if (strategy != null) {
            return strategy.preCalcTaskDailyScoreChange(userTask);
        } else {
            throw new IllegalArgumentException("preCalcTaskDailyScoreChange No strategy found for task rule: " + taskRuleType);
        }
    }
}