package fm.lizhi.ocean.wave.platform.user.remote.service.hy;

import fm.hy.family.api.FamilyService;
import fm.hy.family.protocol.FamilyServiceProto;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.lamp.common.util.JsonUtils;
import fm.lizhi.ocean.wave.platform.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.platform.common.config.BusinessConfig;
import fm.lizhi.ocean.wave.platform.common.utils.RpcResult;
import fm.lizhi.ocean.wave.platform.user.remote.adapter.hy.HyFamilyAdapter;
import fm.lizhi.ocean.wave.platform.user.remote.bean.FamilyInfo;
import fm.lizhi.ocean.wave.platform.user.remote.service.IUserFamilyServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HyUserFamilyServiceRemote implements IUserFamilyServiceRemote {


    @Autowired
    private FamilyService familyService;

    @Autowired
    private HyFamilyAdapter familyAdapter;




    @Override
    public Result<FamilyInfo> getUserInFamily(long userId) {
        Result<FamilyServiceProto.ResponseGetUserInFamily> result = familyService.getUserInFamily(userId);
        if (RpcResult.isFail(result)){
            if (result.rCode() == FamilyService.GET_USER_IN_FAMILY_NO_DATA){
                return new Result<>(GET_USER_IN_FAMILY_NO_DATA, null);
            }
            return new Result<>(GET_USER_IN_FAMILY_FAIL, null);
        }

        String familyJson = result.target().getFamily();
        fm.hy.family.bean.FamilyInfo familyInfo = JsonUtils.fromJsonString(familyJson, fm.hy.family.bean.FamilyInfo.class);
        FamilyInfo bean = familyAdapter.familyProtoToBean(familyInfo);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, bean);
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.HEI_YE;
    }
}
