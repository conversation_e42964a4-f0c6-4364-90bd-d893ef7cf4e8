package fm.lizhi.ocean.wave.platform.app.platform.job.checkin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInStatusConstant;
import fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInTaskEnum;
import fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInTaskStatusConstant;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.dao.checkin.WaveCheckInDao;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.entity.checkin.*;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.mapper.checkin.WaveCheckInRecordMapper;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.mapper.checkin.WaveCheckInScheduleMapper;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.mapper.checkin.WaveCheckInUserTaskMapper;
import fm.lizhi.ocean.wave.platform.app.platform.datastore.redis.checkin.WaveCheckInRedisDao;
import fm.lizhi.ocean.wave.platform.app.platform.manager.checkin.WaveCheckInTaskManager;
import fm.lizhi.ocean.wave.platform.app.platform.manager.checkin.strategy.TaskRuleStrategyExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 麦序福利，计算档期今日历史分
 * <AUTHOR>
 */
@Slf4j
@Component
public class CheckInCalcHistoryScoreJob implements JobHandler {


    @Autowired
    private WaveCheckInTaskManager checkInTaskManager;

    @Autowired
    private WaveCheckInScheduleMapper checkInScheduleMapper;

    @Autowired
    private WaveCheckInRecordMapper checkInRecordMapper;

    @Autowired
    private WaveCheckInUserTaskMapper checkInUserTaskMapper;

    @Autowired
    private WaveCheckInDao checkInDao;

    @Autowired
    private WaveCheckInRedisDao checkInRedisDao;

    @Autowired
    private TaskRuleStrategyExecutor taskRuleStrategyExecutor;

    @Override
    public void execute(JobExecuteContext jobExecuteContext) {
        // 默认找到上一个小时
        DateTime lastHour = DateUtil.offsetHour(new Date(), -1);

        if (null != jobExecuteContext) {
            String param = jobExecuteContext.getParam();
            if (StrUtil.isNotBlank(param)) {
                log.info("CheckInCalcHistoryScoreJob execute param:{}", param);
                lastHour = DateUtil.parse(param, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }

        DateTime lastStartTime = DateUtil.beginOfHour(lastHour);
        // wave_check_in_schedule.end_time 是下一个档期的开始时间
        DateTime lastEndTime = DateUtil.beginOfHour(DateUtil.offsetHour(lastHour, 1));
        int pageNo = 1;
        int pageSize = 50;
        int calcCount = 0;

        try {
            // 添加标记，用户查询历史任务分时展示 “正在计算中”
            checkInRedisDao.setCheckInUndoneSettlementJobFlat(lastHour, true);

            log.info("CheckInCalcHistoryScoreJob start. lastHour:{}, lastStartTime:{}, lastEndTime:{}", lastHour, lastStartTime, lastEndTime);
            WaveCheckInScheduleExample example = new WaveCheckInScheduleExample();
            example.createCriteria()
                    .andStartTimeEqualTo(lastStartTime)
                    .andEndTimeEqualTo(lastEndTime);
            example.setOrderByClause("create_time desc");

            // 分页找到档期 ID 列表
            PageList<WaveCheckInSchedule> schedulePageList = checkInScheduleMapper.pageByExample(example, pageNo, pageSize);

            while (CollUtil.isNotEmpty(schedulePageList)){
                try {
                    calcCount += calcHistoryScoreByScheduleList(schedulePageList, lastHour);
                }catch (Exception e){
                    log.error("CheckInCalcHistoryScoreJob calcHistoryScoreByScheduleList is fail. pageNo:{}, startTime:{}, endTime:{}", pageNo, lastStartTime, lastEndTime, e);
                }
                pageNo++;
                schedulePageList = checkInScheduleMapper.pageByExample(example, pageNo, pageSize);
            }

        }finally {
            // 添加标记，用户查询历史任务分时展示 “正在计算中”
            checkInRedisDao.setCheckInUndoneSettlementJobFlat(lastHour, false);
            log.info("CheckInCalcHistoryScoreJob end. lastHour:{}, lastStartTime:{}, lastEndTime:{}, calcCount:{}", lastHour, lastStartTime, lastEndTime, calcCount);
        }
    }

    private int calcHistoryScoreByScheduleList(PageList<WaveCheckInSchedule> schedulePageList, DateTime lastHour) {
        if (CollUtil.isEmpty(schedulePageList)){
            log.warn("calcHistoryScoreByScheduleList get schedule list is empty.");
            return 0;
        }

        // 根据档期 ID 列表找到用户相关的记录
        List<Long> scheduleIds = schedulePageList.stream().map(WaveCheckInSchedule::getId).collect(Collectors.toList());
        WaveCheckInRecordExample recordExample = new WaveCheckInRecordExample();
        recordExample.createCriteria().andScheduleIdIn(scheduleIds);
        List<WaveCheckInRecord> recordList = checkInRecordMapper.selectByExample(recordExample);
        if (CollUtil.isEmpty(recordList)){
            log.warn("get record list is empty. scheduleIds:{}", scheduleIds);
            return 0;
        }

        // 找到用户相关的任务
        List<Long> recordIds = recordList.stream().map(WaveCheckInRecord::getId).collect(Collectors.toList());
        WaveCheckInUserTaskExample taskExample = new WaveCheckInUserTaskExample();
        taskExample.createCriteria().andRecordIdIn(recordIds);
        List<WaveCheckInUserTask> userTaskList = checkInUserTaskMapper.selectByExample(taskExample);

        if (CollUtil.isEmpty(userTaskList)){
            log.warn("get user task list is empty. recordIds:{}", recordIds);
            return 0;
        }
        return calcHistoryScoreByScheduleList(schedulePageList, recordList, userTaskList, lastHour);
    }

    public Integer calcHistoryScoreByScheduleList(PageList<WaveCheckInSchedule> schedulePageList, List<WaveCheckInRecord> recordList, List<WaveCheckInUserTask> userTaskList, DateTime lastHour) {

        Map<Long, WaveCheckInRecord> recordMap = recordList.stream().collect(Collectors.toMap(WaveCheckInRecord::getId, Function.identity(), (e1, e2) -> e1));
        Map<Long, WaveCheckInSchedule> scheduleMap = schedulePageList.stream().collect(Collectors.toMap(WaveCheckInSchedule::getId, Function.identity(), (e1, e2) -> e1));

        AtomicInteger count = new AtomicInteger();

        userTaskList.forEach(userTask -> {

            WaveCheckInRecord record = recordMap.get(userTask.getRecordId());
            if (record == null) {
                // record 不存在，跳过
                log.warn("calcHistoryScoreByScheduleList get record is empty. recordId:{}", userTask.getRecordId());
                return;
            }
            WaveCheckInSchedule schedule = scheduleMap.get(record.getScheduleId());

            try {
                if (CheckInTaskStatusConstant.SETTLED == userTask.getStatus()){
                    // 已经结算，跳过
                    log.info("calcHistoryScoreByScheduleList userTask has settled. scheduleId:{}, taskId:{}, userId:{}", schedule.getId(), userTask.getId(), userTask.getUserId());
                    return;
                }

                // 添加用户结算标记
                checkInRedisDao.setCheckInUserUndoneSettlementFlat(lastHour, userTask.getUserId(), schedule.getId(), true);

                // 同步一下 userTask 的魅力值，避免 userTask 和 userRecord 魅力值不一致
                userTask.setCharm(record.getCharmValue());
                userTask.setCharmDiff(Long.valueOf(record.getCharmDiffValue()));


                List<Long> undoneScoreList = checkInDao.getUnDoneScoreList(schedule.getAppId(), userTask.getUserId(), record.getRoomId(), schedule.getStartTime());

                // 计算今日未完成
                List<Long> currentUndoneScore = taskRuleStrategyExecutor.calcTaskDailyUnDone(userTask, undoneScoreList);
                Long sumUnDoneScore = currentUndoneScore.stream().reduce(0L, Long::sum);
                userTask.setTaskDailyUnDone(sumUnDoneScore);
                if (CheckInTaskEnum.FULL_TASK_COMPLETION.getType().equals(userTask.getTaskRule())) {
                    String currentUndoneScoreDetail = CollUtil.join(currentUndoneScore, StrPool.COMMA);
                    userTask.setTaskDailyUnDoneDetail(StrUtil.isEmpty(currentUndoneScoreDetail) ? "0" : currentUndoneScoreDetail);
                } else {
                    userTask.setTaskDailyUnDoneDetail(String.valueOf(sumUnDoneScore));
                }

                // 更新为已打卡 && 已结算
                schedule.setStatus(CheckInStatusConstant.CHECK_IN);
                userTask.setStatus(CheckInTaskStatusConstant.SETTLED);

                // 更新分数
                checkInTaskManager.updateHistoryScore(userTask, schedule, record, currentUndoneScore);
                count.getAndIncrement();
            }catch (Exception e){
                log.warn("calcHistoryScoreByScheduleList is fail. scheduleId:{}, taskId:{}, userId:{}",
                        schedule.getId(), userTask.getId(), userTask.getUserId(), e);

                try {
                    WaveCheckInUserTask task = new WaveCheckInUserTask();
                    task.setId(userTask.getId());
                    task.setStatus(CheckInTaskStatusConstant.SETTLED_FAIL);
                    checkInUserTaskMapper.updateByPrimaryKey(task);
                }catch (Exception e1){
                    log.warn("calcHistoryScoreByScheduleList update status is fail. scheduleId:{}, taskId:{}, userId:{}",
                            schedule.getId(), userTask.getId(), userTask.getUserId(), e1);
                }

            }finally {
                // 删除统计标记
                checkInRedisDao.setCheckInUserUndoneSettlementFlat(lastHour, userTask.getUserId(), schedule.getId(), false);
            }

        });

        return count.get();
    }

}
