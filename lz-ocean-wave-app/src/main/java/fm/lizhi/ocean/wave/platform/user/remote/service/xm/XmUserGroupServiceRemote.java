package fm.lizhi.ocean.wave.platform.user.remote.service.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.platform.user.remote.service.IUserGroupServiceRemote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.usergroup.api.UserGroupService;
import xm.fm.lizhi.live.usergroup.protocol.UserGroupProto;

/**
 * <AUTHOR>
 * @date 2023/9/21 17:57
 */
@Slf4j
@Component
public class XmUserGroupServiceRemote implements IUserGroupServiceRemote {

    @Autowired
    private UserGroupService userGroupService;

    @Override
    public Result<Boolean> isUserInGroup(long groupId, long userId, int userIdType) {
        Result<UserGroupProto.ResponseIsUserInGroup> resp = userGroupService.isUserInGroup(groupId, userId, userIdType);
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("XmUserGroupServiceRemote isUserInGroup groupId={},userId={},userIdType={},rCode={}", groupId, userId, userIdType, resp.rCode());
            return new Result<>(IS_USER_IN_GROUP_FAIL, null);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.target().getIsUserInGroup());
    }

    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.XIMI;
    }
}
