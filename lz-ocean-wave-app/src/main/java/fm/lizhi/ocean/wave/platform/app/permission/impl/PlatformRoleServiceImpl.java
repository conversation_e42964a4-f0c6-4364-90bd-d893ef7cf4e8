package fm.lizhi.ocean.wave.platform.app.permission.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.platform.api.permission.bean.PlatformRoleBean;
import fm.lizhi.ocean.wave.platform.api.permission.bean.PlatformRoleListBean;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestGetPlatformRole;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestGetPlatformRoleDetailList;
import fm.lizhi.ocean.wave.platform.api.permission.request.RequestSavePlatformRole;
import fm.lizhi.ocean.wave.platform.api.permission.service.PlatformRoleService;
import fm.lizhi.ocean.wave.platform.app.common.util.ResultUtils;
import fm.lizhi.ocean.wave.platform.app.permission.datastore.entity.WavePermission;
import fm.lizhi.ocean.wave.platform.app.permission.manager.PermissionManager;
import fm.lizhi.ocean.wave.platform.common.utils.RpcResult;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/2/21 17:20
 */
@ServiceProvider
public class PlatformRoleServiceImpl implements PlatformRoleService {

    @Autowired
    private PermissionManager permissionManager;

    @Override
    public Result<List<PlatformRoleListBean>> getPlatformRoleDetailList(RequestGetPlatformRoleDetailList request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));

        List<PlatformRoleListBean> list = permissionManager.getPlatformRoleDetailList(request);
        LogContext.addResLog("list={}", JsonUtil.dumps(list));
        return ResultUtils.success(list);
    }

    @Override
    public Result<Void> savePlatformRole(RequestSavePlatformRole request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));

        //检查权限是否都存在
        List<WavePermission> wavePermissions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getPermissions())) {
            Set<String> permissions = new HashSet<>(request.getPermissions());
            wavePermissions = permissionManager.selectPermissionByCode(new ArrayList<>(permissions));
            if (wavePermissions.size() != permissions.size()) {
                return RpcResult.fail(SAVE_PLATFORM_ROLE_PERMISSION_NOT_EXIST);
            }
        }

        return permissionManager.savePlatformRole(request, wavePermissions);
    }

    @Override
    public Result<List<PlatformRoleBean>> getPlatformRole(RequestGetPlatformRole request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        List<PlatformRoleBean> platformRole = permissionManager.getPlatformRole(request);
        return ResultUtils.success(platformRole);
    }
}
