package fm.lizhi.ocean.wave.platform.app.platform.version.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.regex.Pattern;

/**
 * 创作者版本工具类
 */
public class WaveVersionUtils {

    /**
     * 版本号正则表达式, 即三段数字组成的版本号, xxx.yyy.zzz
     */
    private static final Pattern VERSION_PATTERN = Pattern.compile("^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$");

    private static final BigDecimal BIG_DECIMAL_ONE_HUNDRED = new BigDecimal("100");

    /**
     * 将版本号转换为比较值
     *
     * @param clientVersion 版本号
     * @return 比较值
     */
    public static int toComparison(String clientVersion) {
        if (clientVersion == null || !VERSION_PATTERN.matcher(clientVersion).matches()) {
            throw new IllegalArgumentException("版本号格式错误: " + clientVersion);
        }
        String[] parts = clientVersion.split("\\.");
        int comparisonValue = 0;
        for (String part : parts) {
            comparisonValue = comparisonValue * 1000 + Integer.parseInt(part);
        }
        return comparisonValue;
    }

    /**
     * 比例百分比转比例整数, 只保留整数部分. 例如: 0.5 -> 50
     *
     * @param ratio 比例百分比
     * @return 比例整数
     */
    public static Integer ratioPercentToInteger(BigDecimal ratio) {
        if (ratio == null) {
            return null;
        }
        return ratio.multiply(BIG_DECIMAL_ONE_HUNDRED).intValue();
    }

    /**
     * 比例整数转百分比. 例如: 50 -> 0.5
     *
     * @param ratio 比例整数
     * @return 比例百分比
     */
    public static BigDecimal ratioIntegerToPercent(Integer ratio) {
        if (ratio == null) {
            return null;
        }
        return new BigDecimal(ratio).divide(BIG_DECIMAL_ONE_HUNDRED, 2, RoundingMode.HALF_UP);
    }
}
