package fm.lizhi.ocean.wave.platform.user.constant.pp;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 声线枚举
 */
@Getter
public enum PpVoiceLineEnum {
    //粉色
    YU_JIE("御姐音", "#FF50C8"),
    SHAO_NV("少女音", "#FF50C8"),
    XUE_MEI("学妹音", "#FF50C8"),
    SHAO_YU("少御音", "#FF50C8"),
    SHAO_LUO("少萝音", "#FF50C8"),
    MENG_MEI("萌妹音", "#FF50C8"),

    //紫色
    QIN_SHU("青叔音", "#875AFF"),
    WEN_QING("温青音", "#875AFF"),
    YAN_ZAO("烟嗓音", "#875AFF"),
    DI_YIN_PAO("低音炮", "#875AFF"),
    DIAN_LIU("电流音", "#875AFF"),
    MING_XING("明星音", "#875AFF"),
    QI_PAO("气泡音", "#875AFF"),
    NAI_GOU("奶狗音", "#875AFF"),
    DA_SHU("大叔音", "#875AFF"),
    QING_NIAN("青年音", "#875AFF"),

    //中性的颜色,代码中单独判断
    ZHONG_XING("中性音", "#FF50C8"),
    ;

    private String name;
    private String color;

    PpVoiceLineEnum(String name, String color) {
        this.name = name;
        this.color = color;
    }

    private static Map<String, PpVoiceLineEnum> map = new HashMap<>();

    static {
        for (PpVoiceLineEnum object : PpVoiceLineEnum.values()) {
            map.put(object.getName(), object);
        }
    }

    public static PpVoiceLineEnum from(String value) {
        PpVoiceLineEnum homeCategoryEnum = map.get(value);
        if (homeCategoryEnum == null) {
            return PpVoiceLineEnum.QIN_SHU;
        }
        return map.get(value);
    }

}