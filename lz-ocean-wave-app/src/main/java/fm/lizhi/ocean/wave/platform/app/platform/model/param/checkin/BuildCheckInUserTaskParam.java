package fm.lizhi.ocean.wave.platform.app.platform.model.param.checkin;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BuildCheckInUserTaskParam {

    private long userId;

    private long roomId;

    private Long recordId;

    private long scheduleId;

    private int taskRule;

    private Long njId;

    private Date scheduleStartTime;

}
