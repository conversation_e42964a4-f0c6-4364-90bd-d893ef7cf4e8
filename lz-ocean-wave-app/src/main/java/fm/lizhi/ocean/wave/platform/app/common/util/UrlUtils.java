package fm.lizhi.ocean.wave.platform.app.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Properties;

/**
 * URL工具类
 */
public class UrlUtils {

    /**
     * 移除url的scheme、host和port, 保留path、query和fragment, 返回ASCII形式. 如果输入url为空则返回空白字符串. 该方法适用于数据库可选的URL字段.
     *
     * @param url 输入的url地址
     * @return 移除host后的路径
     */
    public static String removeHostOrEmpty(String url) {
        if (StringUtils.isBlank(url)) {
            return StringUtils.EMPTY;
        }
        try {
            URI uri = new URI(url);
            // 非斜杆开头的相对路径url, 保留非斜杆开头
            if (StringUtils.isBlank(uri.getScheme()) && StringUtils.isBlank(uri.getHost()) && !url.startsWith("/")) {
                return uri.toASCIIString();
            }
            // 移除scheme和host, 其中移除host的同时会忽略port
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.setScheme(null);
            uriBuilder.setHost(null);
            return uriBuilder.build().toASCIIString();
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("输入的url不是合法的URI格式: " + url, e);
        }
    }

    /**
     * 匹配给定的url和host配置, 移除url的scheme、host和port, 保留path、query和fragment, 返回ASCII形式. 如果不匹配则返回原url,
     * 如果输入url为空则返回空白字符串. 该方法适用于数据库可选的URL字段, 希望存储相对路径但已经存在部分带host的url的情况.
     *
     * @param url  输入的url地址
     * @param host host地址
     * @return 移除host后的路径
     */
    public static String removeHostOrEmpty(String url, String host) {
        if (StringUtils.isBlank(url)) {
            return StringUtils.EMPTY;
        }
        URI uri;
        try {
            uri = new URI(url);
        } catch (URISyntaxException e) {
            throw new RuntimeException("输入的url不是合法的URI格式: " + url, e);
        }
        if (StringUtils.isBlank(host)) {
            return uri.toASCIIString();
        }
        URI hostUri;
        try {
            hostUri = new URI(host);
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("输入的host不是合法的URI格式: " + host, e);
        }
        if (Objects.equals(uri.getHost(), hostUri.getHost()) && Objects.equals(uri.getPort(), hostUri.getPort())) {
            return removeHostOrEmpty(url);
        } else {
            return uri.toASCIIString();
        }
    }

    /**
     * 添加host到url中, 返回ASCII形式. 如果url已有host, 则不添加. 如果输入的url为空则返回空白字符串. 该方法适用于数据库可选的URL字段.
     *
     * @param path url路径
     * @param host host地址
     * @return 添加host后的url
     */
    public static String addHostOrEmpty(String path, String host) {
        return addHostOrEmpty(path, host, StringUtils.EMPTY);
    }

    /**
     * 添加host和pathPrefix到url中, 返回ASCII形式. 如果url已有host, 则不添加. 如果输入的url为空则返回空白字符串. 该方法适用于数据库可选的URL字段.
     *
     * @param path       url路径
     * @param host       host地址
     * @param pathPrefix path前缀
     * @return 添加host和pathPrefix后的url
     */
    public static String addHostOrEmpty(String path, String host, String pathPrefix) {
        if (StringUtils.isBlank(path)) {
            return StringUtils.EMPTY;
        }
        String mergedPath;
        if (StringUtils.isBlank(pathPrefix)) {
            mergedPath = path;
        } else if (StringUtils.endsWith(pathPrefix, "/") && StringUtils.startsWith(path, "/")) {
            mergedPath = pathPrefix + path.substring(1);
        } else if (!StringUtils.endsWith(pathPrefix, "/") && !StringUtils.startsWith(path, "/")) {
            mergedPath = pathPrefix + "/" + path;
        } else {
            mergedPath = pathPrefix + path;
        }
        URI pathUri;
        URIBuilder pathUriBuilder;
        try {
            pathUri = new URI(mergedPath);
            pathUriBuilder = new URIBuilder(mergedPath);
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("输入的path不是合法的URI格式, path: " + path + ", pathPrefix: " + pathPrefix, e);
        }
        // 如果path已有scheme和host, 则不添加scheme和host, 只返回path
        if (StringUtils.isNotBlank(pathUri.getScheme()) && StringUtils.isNotBlank(pathUri.getHost())) {
            return pathUri.toASCIIString();
        }
        URI hostUri;
        try {
            hostUri = new URI(host);
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("输入的host不是合法的URI格式: " + host, e);
        }
        if (StringUtils.isBlank(hostUri.getScheme()) || StringUtils.isBlank(hostUri.getHost())) {
            throw new IllegalArgumentException("输入的host没有scheme或hostName: " + host);
        }
        // 使用替换scheme、host和port的方式, 会自动为path补全斜杠开头
        try {
            pathUriBuilder.setScheme(hostUri.getScheme());
            pathUriBuilder.setHost(hostUri.getHost());
            pathUriBuilder.setPort(hostUri.getPort());
            return pathUriBuilder.build().toASCIIString();
        } catch (URISyntaxException e) {
            throw new IllegalStateException("添加host失败, path: " + path + ", host: " + host + ", pathPrefix: " + pathPrefix, e);
        }
    }

    /**
     * 将bean平铺作为查询参数添加到url中, 返回ASCII形式. 如果输入的bean为空则返回原url. 该方法适用于GET请求需要添加查询参数的场景.
     *
     * @param url  url地址
     * @param bean 查询参数对象, 支持jackson注解
     * @return 添加查询参数后的url
     */
    public static String addFlatQueryParams(String url, Object bean) {
        URIBuilder uriBuilder;
        try {
            uriBuilder = new URIBuilder(url);
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("输入的url不是合法的URI格式: " + url, e);
        }
        try {
            if (bean == null) {
                return uriBuilder.build().toASCIIString();
            }
            Properties queryParams = JsonUtils.toProperties(bean);
            for (String paramName : queryParams.stringPropertyNames()) {
                String paramValue = queryParams.getProperty(paramName);
                if (paramValue != null) {
                    uriBuilder.addParameter(paramName, paramValue);
                }
            }
            return uriBuilder.build().toASCIIString();
        } catch (URISyntaxException e) {
            throw new IllegalStateException("添加查询参数失败, url: " + url + ", bean: " + bean, e);
        }
    }
}
