package fm.lizhi.ocean.wave.platform.live.processor.kickout;


import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.platform.api.live.param.CheckKickOutPermissionNotConfigReq;
import fm.lizhi.ocean.wave.platform.api.live.param.KickOutReq;
import fm.lizhi.ocean.wave.platform.common.processor.BusinessEnvAwareProcessor;
import fm.lizhi.ocean.wave.platform.live.model.dto.KickOutConfigDTO;

/**
 * <AUTHOR>
 */
public interface KickOutUserProcessor extends BusinessEnvAwareProcessor {



    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return KickOutUserProcessor.class;
    }


    /**
     * 校验权限
     * @param req
     * @return
     */
    Result<Boolean> checkKickOutPermission(CheckKickOutPermissionNotConfigReq req);


    /**
     * 获取剩余踢人次数
     * @param liveRoomId
     * @return
     */
    Integer getRemainingKickCount(Long liveRoomId);


    /**
     * 获取踢人配置
     *
     * @param liveRoomId
     * @return
     */
    KickOutConfigDTO getKickOutConfig(Long liveRoomId);

    /**
     * 罗马推送到APP
     * @param userId
     */
    void pushMessageRoma(long userId, long liveId);

    void sendSystemComment(KickOutReq req);
}
