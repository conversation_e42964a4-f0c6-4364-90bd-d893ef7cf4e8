package fm.lizhi.ocean.wave.platform.checkin;

import fm.lizhi.ocean.wave.platform.app.platform.job.checkin.CheckInCalcDayMicJob;
import fm.lizhi.ocean.wave.platform.test.AbstractDataCenterTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class CheckInJobTest extends AbstractDataCenterTest {


    @Autowired
    private CheckInCalcDayMicJob checkInCalcDayMicJob;


    @Test
    public void checkInCalcDayMicJob() throws Exception {
        checkInCalcDayMicJob.execute(null);
    }
}
