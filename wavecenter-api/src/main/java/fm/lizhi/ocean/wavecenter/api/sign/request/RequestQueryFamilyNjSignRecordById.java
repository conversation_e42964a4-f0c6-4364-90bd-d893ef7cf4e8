package fm.lizhi.ocean.wavecenter.api.sign.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/28 15:06
 */
@Getter
@Builder
public class RequestQueryFamilyNjSignRecordById implements IContextRequest {

    private Long id;

    private int appId;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
