package fm.lizhi.ocean.wavecenter.api.anchor.singer.request;

import fm.lizhi.ocean.wavecenter.api.common.bean.IContextRequest;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RequestSingerTotalCountInHall implements IContextRequest {

    private Integer appId;

    /**
     * 厅主ID
     */
    private Long njId;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
