package fm.lizhi.ocean.wavecenter.api.message.constant;

import lombok.Getter;

/**
 * 端内跳转链接类型
 * <AUTHOR>
 */
@Getter
public enum MessageTargetLinkEnum {

    /**
     * 外部跳转
     */
    EXTERNAL_JUMP,

    /**
     * 首页
     */
    HOME,

    /**
     * 公会数据
     */
    FAMILY_DATA,

    /**
     * 签约厅数据
     */
    ROOM_DATA,

    /**
     * 主播数据
     */
    PLAYER_DATA,

    /**
     * 签约厅排行榜
     */
    ROOM_RANK,

    /**
     * 主播排行榜
     */
    PLAYER_RANK,

    /**
     * 公会收益
     */
    FAMILY_INCOME,

    /**
     * 签约厅收益
     */
    ROOM_INCOME,

    /**
     * 个人收益
     */
    PERSONAL_INCOME,

    /**
     * 公会信息
     */
    GUILD_INFO,

    /**
     * 公会成员
     */
    GUILD_MEMBER,

    /**
     * 签约厅成员
     */
    ROOM_MEMBER,

    /**
     * 违规统计
     */
    ILLEGAL_STATISTIC,

    /**
     * 违规记录
     */
    ILLEGAL_RECORD,

    /**
     * 私信拓客
     */
    PRIVATE_MSG_ANALYSIS,

    /**
     * 厅打卡汇总
     */
    ROOM_ARRANGEMENT_SUMMARY,

    /**
     * 厅打卡月统计
     */
    ROOM_ARRANGEMENT_STATISTIC,

    /**
     * 厅打卡明细
     */
    ROOM_ARRANGEMENT_DETAIL,

    /**
     * 主播打卡明细
     */
    PLAYER_ARRANGEMENT_DETAIL,

    /**
     * 签约管理
     */
    SIGN_MANAGEMENT,

    /**
     * 角色授权
     */
    ROLE_AUTHORIZATION,

    /**
     * 服务中心签约管理
     */
    SERVICE_CENTER_SIGN,

    /**
     * 活动日历
     */
    ACTIVITY_CALENDAR,

    /**
     * 活动模板列表
     */
    ACTIVITY_TEMPLATE_LIST,

    ;


    public static Boolean contains(String name) {
        for (MessageTargetLinkEnum value : MessageTargetLinkEnum.values()) {
            if (value.name().equals(name)) {
                return true;
            }
        }
        return false;
    }
}
