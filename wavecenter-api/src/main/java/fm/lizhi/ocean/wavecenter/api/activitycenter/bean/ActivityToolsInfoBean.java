package fm.lizhi.ocean.wavecenter.api.activitycenter.bean;

import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 *
 * 玩法工具库
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ActivityToolsInfoBean {

    private Long id;

    /**
     * 业务ID
     */
    private Integer appId;

    /**
     * 工具名称
     */
    private String name;

    /**
     * 玩法工具值，用于关联其他表
     */
    private Integer toolValue;

    /**
     * 类型, 1:玩法; 2: 工具
     */
    private Integer type;

    /**
     * 玩法工具描述
     */
    private String toolDesc;

    /**
     * 状态 0: 下架; 1: 上架
     */
    private Integer status;

    /**
     * 操作人
     */
    private String operator;


    /**
     * 修改时间
     */
    private Long modifyTime;


}