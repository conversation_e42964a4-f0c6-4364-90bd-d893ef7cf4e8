package fm.lizhi.ocean.wavecenter.api.anchor.singer.service;

import javax.validation.Valid;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.request.RequestSingerVerifyApply;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerEntranceInfo;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.response.ResponseGetSingerStatus;

/**
 * 歌手认证服务
 */
public interface SingerVerifyService {

    /**
     * 歌手认证申请
     * @param request 歌手认证申请请求
     * @return 结果
     */
    Result<Void> singerVerifyApply(@Valid RequestSingerVerifyApply request);

    /**
     * 获取歌手状态
     * @param appId 应用ID
     * @param userId 用户ID
     * @return 结果
     */
    Result<ResponseGetSingerStatus> getSingerStatus(Integer appId, Long userId);

    /**
     * 获取歌手入口权限和状态信息
     */
    Result<ResponseGetSingerEntranceInfo> getSingerEntranceInfo(Integer appId, Long userId);


    /**
     * 用户被拉黑，无法申请
     */
    int SINGER_VERIFY_APPLY_IN_BLACK_LIST = 20400001;

    /**
     * 预审核不通过
     */
    int SINGER_VERIFY_APPLY_PRE_AUDIT_NO_PASS = 20400002;

    /**
     * 申请失败
     */
    int SINGER_VERIFY_APPLY_FAIL = 20400003;

    /**
     * 已是歌手
     */
    int SINGER_VERIFY_APPLY_ALREADY_SINGER = 20400004;

    /**
     * 操作频繁
     */
    int SINGER_VERIFY_APPLY_TOO_FAST = 20400005;

    /**
     * 存在审核中的记录
     */
    int SINGER_VERIFY_APPLY_EXIST_AUDITING_RECORD = 20400006;

    /**
     * 入口已关闭
     */
    int SINGER_VERIFY_APPLY_MENU_IS_CLOSE_FAIL = 20400008;



}
