package fm.lizhi.ocean.wavecenter.api.common.bean;

import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/11 14:43
 */
@Getter
@Builder
public class PageParamBean {

    private int pageNo;
    private int pageSize;

    public static class PageParamBeanBuilder{
        public PageParamBean build(){
            if (pageNo <= 0) {
                pageNo = 1;
            }
            if (pageSize <= 0) {
                pageSize = 20;
            }
            return new PageParamBean(pageNo, pageSize);
        }
    }
}
