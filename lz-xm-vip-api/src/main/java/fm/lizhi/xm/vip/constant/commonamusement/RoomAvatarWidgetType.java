package fm.lizhi.xm.vip.constant.commonamusement;

/**
 * 房间外显头像框图片类型
 *
 * <AUTHOR>
 */
public enum RoomAvatarWidgetType {

    /**
     * 0-没有头像框图片，1-png，2-svga
     */
    DEFAULT(0, "默认，没有头像框图片"),
    IMAGE_PNG(1, "png"),
    IMAGE_SVGA(2, "svga");

    /**
     * 枚举索引值
     */
    private final int index;

    /**
     * 枚举描述
     */
    private final String description;

    RoomAvatarWidgetType(int index, String description) {
        this.index = index;
        this.description = description;
    }

    /**
     * 获取枚举的索引值
     *
     * @return
     */
    public int getIndex() {
        return index;
    }

    /**
     * 获取枚举的描述
     *
     * @return
     */
    public String getDescription() {
        return description;
    }

    /**
     * 查询枚举
     *
     * @param index    索引值
     * @param defaultV 默认枚举
     * @return
     */
    public static RoomAvatarWidgetType lookup(int index, RoomAvatarWidgetType defaultV) {
        for (RoomAvatarWidgetType vo : RoomAvatarWidgetType.values()) {
            if (vo.index == index) {
                return vo;
            }
        }
        return defaultV;
    }
}
