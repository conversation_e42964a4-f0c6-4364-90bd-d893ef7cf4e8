package fm.lizhi.xm.vip.constant.medal;

import java.util.HashMap;
import java.util.Map;

/**
 * 发放勋章类型枚举
 *
 * <AUTHOR>
 */
public enum IssueMedalTypeEnum {
    /**
     * 自动发放
     */
    AUTO_ISSUANCE(1, "自动发放"),
    /**
     * 人工发放
     */
    MANUAL_ISSUANCE(2, "人工发放");

    private int typeCode;

    private String desc;

    IssueMedalTypeEnum(int typeCode, String desc) {
        this.typeCode = typeCode;
        this.desc = desc;
    }

    private static Map<Integer, IssueMedalTypeEnum> map = new HashMap<>();

    static {
        for (IssueMedalTypeEnum object : IssueMedalTypeEnum.values()) {
            map.put(object.getTypeCode(), object);
        }
    }

    public int getTypeCode() {
        return typeCode;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据值类型找枚举
     *
     * @param typeCode 值
     * @return
     */
    public static IssueMedalTypeEnum from(int typeCode) {
        return map.get(typeCode);
    }
}
