package fm.lizhi.xm.vip.bean.medal.medalwalll;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 勋章墙-后台-配置勋章请求参数
 *
 * <AUTHOR>
 * @Date 2022-12-03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MedalParam {

    /**
     * 勋章id
     */
    private long id;

    /**
     * 勋章名
     */
    private String name;

    /**
     * 页码
     */
    private int pageNo;

    /**
     * 查询条数
     */
    private int pageSize;
}
