import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto;
import io.github.cfgametech.Response;
import io.github.cfgametech.SDK;
import io.github.cfgametech.beans.GetChannelTokenRequest;
import io.github.cfgametech.beans.GetChannelTokenResponse;
import io.github.cfgametech.sign.SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import tech.sud.mgp.auth.api.SudCode;
import tech.sud.mgp.auth.api.SudMGPAuth;
import tech.sud.mgp.auth.api.SudSSToken;

/**
 */
@Slf4j
public class LukTest {

    static {
//        -Dmetadata.region=cn
//                -Dmetadata.deploy.env=test
//                -Dmetadata.business.env=lizhi
//                -Dmetadata.service.name=lz_ocean_seal
//                -Dconf.env=office
//                -Dconf.key=lz_ocean_seal
//                -Dapp.name=lz_ocean_seal
//                -Dregion=cn
//                -DbusinessEnv=lizhi
//                -DCAT_HOME=/tmp
        System.setProperty("metadata.region", "cn");
        System.setProperty("metadata.deploy.env", "test");
        System.setProperty("metadata.service.name", "lz_ocean_seal_local");
        System.setProperty("conf.env", "cn");
        System.setProperty("region", "cn");
        System.setProperty("businessEnv", "lizhi");

    }

    public static final GameAuthService gameAuthService = ProxyBuilderContainer.PROXY_BUILDER.buildProxy(GameAuthService.class);

    public static final String APP_ID = "1045111";

    public static final String APP_SECRET = "55428f6455ef4b1810c9ecb8be6a";

    @Test
    public void getSsToken() throws Exception {

        String code = "xxx";

        Result<GameAuthServiceProto.ResponseGetServerToken> ssTokenResult = gameAuthService.getServerToken(code, GameChannel.LUK, "13592841");
        System.out.println("rcode:"+ssTokenResult.rCode());
        if (ssTokenResult.rCode() != 0) {
            throw new RuntimeException(ssTokenResult.getAttachment());
        }
        GameAuthServiceProto.ServerToken sudSSToken = ssTokenResult.target().getToken();
        if (sudSSToken.getErrorCode() != 0) {
            log.error("获取token失败：{}", sudSSToken);
            throw new RuntimeException(ssTokenResult.getAttachment());
        }
        log.info("结果token：{}", sudSSToken.getToken());
    }



    @Test
    public void getCode() throws Exception {

        SudMGPAuth sudMGPAuth = new SudMGPAuth("1922112172167983104", "uHA7ZWxa6CWmPZvK7LDHXg4nVKgqbKqO");
        SudCode code = sudMGPAuth.getCode("1258483457768698626");
        log.info("结果code：{}", code.getCode());
    }

    @Test
    public void getLukSsToken() throws Exception {

        SDK sdk = new SDK(APP_ID, APP_SECRET);

        // 来自 SDK 请求的参数结构
        GetChannelTokenRequest request = new GetChannelTokenRequest();

        request.setChannelId(1000);
        request.setUserId("1234567890123");
        request.setTimestamp(1750839747503L);
        request.setSign(sdk.generateSignature(request));

        // 处理请求
        Response<GetChannelTokenResponse> resp = sdk.getChannelToken(request, getChannelTokenRequest -> {
            // 业务逻辑
            GetChannelTokenResponse response = new GetChannelTokenResponse();

            // 生成 Token
            response.setToken("token");

            // 设置 Token 过期时间
            response.setLeftTime(7200);

            return response;
        });

        SignUtils.signature(sdk.generateSignature(request), resp);

        // 将 resp 作为 JSON 写入 HTTP 响应
        System.out.println(resp.getCode());
        System.out.println(resp.getMessage());
        System.out.println(resp.getData().getToken());

    }
}
