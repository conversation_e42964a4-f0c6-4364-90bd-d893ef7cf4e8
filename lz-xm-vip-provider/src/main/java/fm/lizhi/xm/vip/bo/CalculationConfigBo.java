package fm.lizhi.xm.vip.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CalculationConfigBo implements Comparable<CalculationConfigBo> {

    /**
     * 起始财富等级
     */
    private Integer startLevel;

    /**
     * 结束财富等级
     */
    private Integer endLevel;

    /**
     * 金币数量（起始财富等级）
     */
    private Long startLevelCoinAmount;

    /**
     * 金币数量（点亮起始财富等级）
     */
    private Long litUpCoinAmount;

    /**
     * 图标Aspect
     */
    private Double iconAspect;

    @Override
    public int compareTo(CalculationConfigBo other) {
        if (other == null) {
            return 1;
        }
        if (this == other) {
            return 0;
        }
        if (startLevel == null) {
            return -1;
        }
        return startLevel.compareTo(other.getStartLevel());
    }
}
