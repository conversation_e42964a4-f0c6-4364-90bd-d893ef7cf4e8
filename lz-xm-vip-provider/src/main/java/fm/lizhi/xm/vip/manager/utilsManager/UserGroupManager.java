package fm.lizhi.xm.vip.manager.utilsManager;

import com.google.common.collect.Lists;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.live.usergroup.api.UserGroupBgService;
import fm.lizhi.live.usergroup.api.UserGroupService;
import fm.lizhi.live.usergroup.protocol.UserGroupBgProto;
import fm.lizhi.live.usergroup.protocol.UserGroupProto;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> wenwei
 */
@Slf4j
@AutoBindSingleton
public class UserGroupManager {
    @Inject
    private UserGroupService userGroupService;
    @Inject
    private UserGroupBgService userGroupBgService;

    /**
     * 用户是否处于用户组
     * @param groupId
     * @param userId
     * @return
     */
    public boolean isUserInGroup(long groupId, long userId) {
        if (groupId == 0 || userId == 0) {
            return false;
        }
        try {
            Result<UserGroupProto.ResponseIsUserInGroup> resp = userGroupService.isUserInGroup(groupId, userId);
            if (resp.rCode() != 0) {
                log.warn("UserGroupManager isUserInGroup error rCode:{},groupId:{},userId:{}", resp.rCode(), groupId,
                        userId);
                return false;
            }
            boolean isUserInGroup = resp.target().getIsUserInGroup();
            return isUserInGroup;
        } catch (Exception e) {
            log.warn("UserGroupManager isUserInGroup error groupId:" + groupId + ",userId:" + userId, e);
        }
        return false;
    }

    /**
     * 获取用户组的用户Id
     * @param groupId
     * @return
     */
    public List<Long> getGroupUserIds(long groupId) {
        try {
            Result<UserGroupProto.ResponseGetGroupUserIds> resp = userGroupService.getGroupUserIds(groupId);
            if (resp.rCode() != 0) {
                log.warn("UserGroupManager getGroupUserIds error rCode:{},groupId:{}", resp.rCode(), groupId);
                return Lists.newArrayList();
            }
            return resp.target().getUserIdList();
        } catch (Exception e) {
            log.warn("UserGroupManager getGroupUserIds error groupId:" + groupId, e);
        }
        return Lists.newArrayList();
    }

    /**
     * 获取用户组
     *
     * @param groupId
     * @return
     */
    public UserGroupBgProto.UserGroup getUserGroup(long groupId) {
        Result<UserGroupBgProto.ResponseGetGroups> res = userGroupBgService.getGroups(UserGroupBgProto.UserGroupQueryParam.newBuilder().addId(groupId).build());
        log.info("userGroupBgService.getGroups code={},groupId={}", res.rCode(), groupId);
        return res.target().getUserGroup(0);
    }

    /**
     * 更新用户组
     *
     * @param group
     * @param userIds
     * @return
     */
    public int saveUserGroup(UserGroupBgProto.UserGroup group, List<Long> userIds) {

        UserGroupBgProto.UserGroup.Builder builder = UserGroupBgProto.UserGroup.newBuilder();
        builder.setId(group.getId())
                .setTaskId(group.getTaskId())
                .setActivityId(group.getActivityId())
                .setLogo(group.getLogo())
                .setName(group.getName())
                .setRemark(group.getRemark())
                .setType(group.getType())
                .setModifyTime(group.getModifyTime())
                .setTimely(group.getTimely())
                .addAllUserId(userIds);
        Result<UserGroupBgProto.ResponseSaveUserGroup> res = userGroupBgService.saveUserGroup(builder.build());
        if (res.rCode() != 0) {
            log.warn("saveUserGroup fail`groupId={}`userIds={}", group.getId(), userIds);
            return -1;
        }
        return res.rCode();
    }

    /**
     * 获取指定ids的用户组信息列表
     *
     * @param userGroupIds 用户组ids
     * @return
     */
    public List<UserGroupBgProto.UserGroup> getUserGroups(List<Long> userGroupIds) {
        UserGroupBgProto.UserGroupQueryParam.Builder request = UserGroupBgProto.UserGroupQueryParam.newBuilder();
        request.addAllId(userGroupIds);
        request.setLimit(9999);
        request.setOffset(0);
        Result<UserGroupBgProto.ResponseGetGroups> result = userGroupBgService.getGroups(request.build());
        if (result.rCode() != 0 || result.target() == null) {
            log.warn("UserGroupManager.getGroups failed. rCode={}", result.rCode());
            return new ArrayList<>();
        }
        return result.target().getUserGroupList();

    }
}
