package fm.lizhi.xm.vip.xxl.migrate;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.xm.vip.bean.decorate.DecorateTypeEnum;
import fm.lizhi.xm.vip.conf.LzConfig;
import fm.lizhi.xm.vip.dao.entity.decorate.DecorateStockPO;
import fm.lizhi.xm.vip.dao.entity.medal.MedalWallUserMedalPO;
import fm.lizhi.xm.vip.dao.mapper.decorate.DecorateStockPOMapper;
import fm.lizhi.xm.vip.dao.mapper.medal.MedalWallUserMedalPOMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Date;

/**
 * 用户勋章墙关系过期数据迁移
 *
 * <AUTHOR>
 * @date 2023/12/6 下午16:08
 * @description
 */
@Slf4j
@JobHandler("TransferUserMedalWallExpiredJobHandler")
@AutoBindSingleton(baseClass = IJobHandler.class, multiple = true)
public class TransferUserMedalWallExpiredJobHandler extends IJobHandler {

    @Inject
    private MedalWallUserMedalPOMapper medalWallUserMedalPOMapper;
    @Inject
    private DecorateStockPOMapper decorateStockPOMapper;
    @Inject
    private LzConfig lzConfig;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        if (StringUtils.isNotBlank(s) && s.equals("true")) {
            avatarDataMigration();
        }
        return null;
    }

    public void avatarDataMigration() {
        log.info("[TransferUserMedalWallExpiredJobHandler] user medal wall data migration begin. ====================================================");

        boolean hasNextPage = false;
        int pageNumber = 1;
        int pageSize = 200;
        Date endTime = DateUtil.parse(lzConfig.getHistoryMedalWallEndTime(), DatePattern.NORM_DATETIME_PATTERN);
        do {
            PageList<MedalWallUserMedalPO> userMedalWallPOS = medalWallUserMedalPOMapper.getPageUserExpiredMedal(endTime, pageNumber, pageSize);
            if (CollectionUtils.isEmpty(userMedalWallPOS)) {
                log.info("[TransferUserMedalWallExpiredJobHandler] user medal wall no data to transfer !");
                return;
            }
            log.info("[TransferUserMedalWallExpiredJobHandler] user medal wall page from db. size={}`total={}", userMedalWallPOS.size(), userMedalWallPOS.getTotal());

            for (MedalWallUserMedalPO userMedalWallPO : userMedalWallPOS) {
                // 查询用户是否已经有该勋章墙勋章
                DecorateStockPO example = new DecorateStockPO();
                example.setOwnerId(userMedalWallPO.getUserId());
                example.setDecorateId(userMedalWallPO.getMedalId());
                DecorateStockPO historyStock = decorateStockPOMapper.selectOne(example);
                if (historyStock != null) {
                    log.info("[TransferUserMedalWallExpiredJobHandler] user medal wall historyStock is existed. userId={}`decoratedId={}", userMedalWallPO.getUserId(), userMedalWallPO.getMedalId());
                    historyStock.setReceiveNum(historyStock.getReceiveNum() + 1);
                    historyStock.setModifyTime(new Date());
                    decorateStockPOMapper.updateByPrimaryKey(historyStock);
                    continue;
                }

                DecorateStockPO decorateStockPOExample = new DecorateStockPO();
                decorateStockPOExample.setId(userMedalWallPO.getId());
                decorateStockPOExample.setType(DecorateTypeEnum.MEDAL_WALL.getType());
                decorateStockPOExample.setOwnerId(userMedalWallPO.getUserId());
                decorateStockPOExample.setDecorateId(userMedalWallPO.getMedalId());
                decorateStockPOExample.setStartTime(userMedalWallPO.getStartTime());
                decorateStockPOExample.setEndTime(userMedalWallPO.getEndTime());
                decorateStockPOExample.setCreateTime(userMedalWallPO.getCreateTime());
                decorateStockPOExample.setModifyTime(userMedalWallPO.getModifyTime());
                decorateStockPOExample.setIsUsing(0);
                // 过期
                decorateStockPOExample.setStatus(0);
                decorateStockPOExample.setReceiveNum(1);

                DecorateStockPO stockPO = decorateStockPOMapper.selectByPrimaryKey(decorateStockPOExample);
                if (stockPO != null) {
                    decorateStockPOMapper.deleteByPrimaryKey(stockPO);
                }
                decorateStockPOMapper.insert(decorateStockPOExample);
            }
            // 查询下一页
            pageNumber = userMedalWallPOS.getNextPageNumber();
            hasNextPage = userMedalWallPOS.isHasNextPage();
        } while (hasNextPage);

        log.info("[TransferUserMedalWallExpiredJobHandler] user medal wall data migration end. ====================================================");
    }

}
