package fm.lizhi.xm.vip.manager.wealth.sync;

import com.google.common.base.Optional;
import fm.lizhi.xm.vip.bean.wealth.common.WealthLevelBean;
import fm.lizhi.xm.vip.component.Component;

public interface Synchronizer extends Component {

    /**
     * 尝试同步（递归），然后获取
     *
     * @param userId 用户ID
     * @return
     */
    Optional<WealthLevelBean> trySyncThenGet(long userId);

    /**
     * 获取（缓存）
     *
     * @param userId 用户ID
     * @return
     */
    Optional<WealthLevelBean> getWithCache(long userId);

    /**
     * 同步（为了校正数据）
     *
     * @param userId 用户ID
     */
    void syncForCorrectData(long userId);
}
