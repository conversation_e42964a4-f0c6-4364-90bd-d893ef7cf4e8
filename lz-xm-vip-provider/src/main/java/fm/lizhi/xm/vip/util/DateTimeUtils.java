package fm.lizhi.xm.vip.util;

import org.joda.time.DateTime;

/**
 * <AUTHOR>
 * @date 2018/7/11
 */
public class DateTimeUtils {

    /**
     * 根据单位获取时间，如果要减amount为负数即可
     *
     * @param amount +用正数，-用负数
     * @param unit   单位 年月日时分秒 y M d h m s
     * @return
     */
    public static DateTime getDateTimeByUnit(int amount, String unit) {
        return getDateTimeByUnit(DateTime.now(), amount, unit);
    }

    /**
     * 根据单位获取时间，如果要减amount为负数即可
     *
     * @param dateTime 用于计算的日期
     * @param amount   +用正数，-用负数
     * @param unit     单位 年月日时分秒 y M d h m s
     * @return
     */
    public static DateTime getDateTimeByUnit(DateTime dateTime, int amount, String unit) {
        switch(unit) {
            case "y":
                return dateTime.plusYears(amount);
            case "M":
                return dateTime.plusMonths(amount);
            case "d":
                return dateTime.plusDays(amount);
            case "h":
                return dateTime.plusHours(amount);
            case "m":
                return dateTime.plusMinutes(amount);
            case "s":
                return dateTime.plusSeconds(amount);
            default:
                return dateTime;
        }
    }
}
