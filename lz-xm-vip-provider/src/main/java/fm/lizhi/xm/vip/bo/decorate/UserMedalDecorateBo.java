package fm.lizhi.xm.vip.bo.decorate;

import fm.lizhi.xm.vip.bean.medal.MedalTypeBean;
import fm.lizhi.xm.vip.dao.entity.decorate.DecoratePO;
import fm.lizhi.xm.vip.dao.entity.decorate.DecorateStockPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserMedalDecorateBo {

    private MedalTypeBean medalTypeBean;

    private DecoratePO decoratePO;

    private DecorateStockPO decorateStockPO;
}
