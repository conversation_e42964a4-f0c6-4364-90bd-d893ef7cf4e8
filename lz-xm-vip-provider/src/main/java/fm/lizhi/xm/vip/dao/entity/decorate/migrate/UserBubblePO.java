package fm.lizhi.xm.vip.dao.entity.decorate.migrate;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 用户与气泡关联表
 *
 * @date 2023-09-06 05:38:24
 */
@Table(name = "`user_bubble`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserBubblePO {
    /**
     * 逻辑主键
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 气泡ID
     */
    @Column(name= "`bubble_id`")
    private Long bubbleId;

    /**
     * 状态 0.可用 -1不可用
     */
    @Column(name= "`state`")
    private Integer state;

    /**
     * 生效时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 创建日期
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改日期
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", bubbleId=").append(bubbleId);
        sb.append(", state=").append(state);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}