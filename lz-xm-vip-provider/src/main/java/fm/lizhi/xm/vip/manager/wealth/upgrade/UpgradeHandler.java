package fm.lizhi.xm.vip.manager.wealth.upgrade;

import com.google.common.base.Optional;
import fm.lizhi.xm.vip.bo.UpgradeContextBo;
import fm.lizhi.xm.vip.component.Component;

public interface UpgradeHandler extends Component {

    /**
     * 处理
     *
     * @param contextBo 上下文
     */
    void handle(UpgradeContextBo contextBo);

    /**
     * 获取进房（进场效果）的URL
     *
     * @param userId 用户ID
     * @return
     */
    Optional<String> getEnterUrl(long userId);

    /**
     * 获取背景（资料卡）的URL
     *
     * @param userId 用户ID
     * @return
     */
    Optional<String> getBackgroundUrl(long userId);
}
