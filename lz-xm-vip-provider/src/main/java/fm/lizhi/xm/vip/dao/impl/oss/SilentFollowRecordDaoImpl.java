package fm.lizhi.xm.vip.dao.impl.oss;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.datastore.client.DataStoreAckMsg;
import fm.lizhi.xm.vip.bean.oss.SilentFollowRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@AutoBindSingleton(SilentFollowRecordDao.class)
public class SilentFollowRecordDaoImpl implements SilentFollowRecordDao {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Inject
    private GuidGenerator guidGenerator;

    @Override
    public SilentFollowRecord selectById(long id) {
        SilentFollowRecord selectSilentFollowRecord = new SilentFollowRecord();
        selectSilentFollowRecord.setId(id);
        DataStoreAckMsg ackMsg = selectSilentFollowRecord.loadFromMaster();
        int rCode = ackMsg.getRcode();
        switch (rCode) {
            case DataStoreAckMsg.RCodeOK: {
                return selectSilentFollowRecord;
            }
            case DataStoreAckMsg.RCodeNoResult: {
                logger.info("[selectById] load SilentFollowRecord by id no result, id={}", id);
                return null;
            }
            default: {
                logger.error("[selectById] load SilentFollowRecord by id error, id={}, rCode={}", id, rCode);
                return null;
            }
        }
    }

    @Override
    public SilentFollowRecord selectByUserAndTargetUser(long userId, long targetUserId) {
        SilentFollowRecord selectSilentFollowRecord = new SilentFollowRecord();
        selectSilentFollowRecord.setUserId(userId);
        selectSilentFollowRecord.setTargetUserId(targetUserId);
        DataStoreAckMsg ackMsg = selectSilentFollowRecord.loadFromMaster();
        int rCode = ackMsg.getRcode();
        switch (rCode) {
            case DataStoreAckMsg.RCodeOK: {
                return selectSilentFollowRecord;
            }
            case DataStoreAckMsg.RCodeNoResult: {
                logger.info("[selectByUserAndTargetUser] load SilentFollowRecord by user and targetUser no result, userId={}, targetUserId={}", userId, targetUserId);
                return null;
            }
            default: {
                logger.error("[selectByUserAndTargetUser] load SilentFollowRecord by user and targetUser error, userId={}, targetUserId={}, rCode={}", userId, targetUserId, rCode);
                return null;
            }
        }
    }

    @Override
    public long insert(long userId, Long targetUserId, String triggleCondition) {
        long id = guidGenerator.genId();
        SilentFollowRecord insertSilentFollowRecord = new SilentFollowRecord();
        insertSilentFollowRecord.setId(id);
        insertSilentFollowRecord.setUserId(userId);
        insertSilentFollowRecord.setTargetUserId(targetUserId);
        insertSilentFollowRecord.setTriggleCondition(triggleCondition);
        DataStoreAckMsg ackMsg = insertSilentFollowRecord.insert();
        int rCode = ackMsg.getRcode();
        if (DataStoreAckMsg.RCodeOK != rCode) {
            logger.error("[insert] insert SilentFollowRecord error, id={}, userId={}, targetUserId={}, triggleCondition={}, rCode={}, msg={}", id, userId, targetUserId, triggleCondition, ackMsg.getRcode(), ackMsg.getData());
            return -1;
        }
        return id;
    }

    @Override
    public boolean delete(long id) {
        SilentFollowRecord selectSilentFollowRecord = selectById(id);
        if (null == selectSilentFollowRecord) {
            return false;
        }
        DataStoreAckMsg ackMsg = selectSilentFollowRecord.delete();
        int rCode = ackMsg.getRcode();
        if (DataStoreAckMsg.RCodeOK != rCode) {
            logger.error("[delete] delete SilentFollowRecord by id error, id={}, rCode={}", id, rCode);
            return false;
        }
        return true;
    }
}
