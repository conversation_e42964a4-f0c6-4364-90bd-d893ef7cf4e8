package fm.lizhi.xm.vip.util.oss;

import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Calendar;
import java.util.Random;

/**
 * 时间工具类
 */
public class TimeUtils {
    private static Logger logger = LoggerFactory.getLogger(TimeUtils.class);

    /**
     * 高峰开始时间，晚上19点
     */
    private static int PEAK_START = 19;

    /**
     * 高峰结束时间，凌晨1点
     */
    private static int PEAK_END = 1;

    private static Random random = new Random();

    /**
     * 获取过期时间<br>
     *     非高峰期会偏移到高峰过期的情况，暂时不考虑.
     *
     * @param originTTL 初始的过期时间(以秒为单位)
     * @return 初始的过期时间 + 高峰偏移 + 随机值
     * <AUTHOR>
     */
    public static int getExpireExcursionPeak(int originTTL) {

        // 高峰时段: 加上偏移值，避开高峰
        int offset = 0;
        int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
        if (hour >= PEAK_START || hour <= PEAK_END) {
            offset = 3600 * 7;
        }

        // 加上随机值，分散过期，1小时范围内
        int number = random.nextInt(3600);

        int result = originTTL + offset + number;

        logger.debug("expectTime:{}, offset:{}, number:{}, result:{}", originTTL, offset, number, result);

        return result;
    }

    /**
     * 获取过期时间
     * @param originTTL 初始的过期时间(以秒/毫秒为单位, 对应redis的expire/pexpire)
     * @param randomStart   随机数的起始值
     * @param randomEnd     随机数的截至值
     * @return 初始的过期时间 + 随机值
     */
    public static int getExpireWithRandom(int originTTL, int randomStart, int randomEnd) {
        return originTTL + RandomUtils.nextInt(randomStart, randomEnd);
    }
}
