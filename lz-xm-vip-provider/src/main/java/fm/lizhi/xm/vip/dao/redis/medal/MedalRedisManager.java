package fm.lizhi.xm.vip.dao.redis.medal;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.xm.vip.bo.medal.UserMedalBo;
import fm.lizhi.xm.vip.manager.medal.bo.UserMedalBoManager;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import java.util.List;

@Slf4j
@AutoBindSingleton
public class MedalRedisManager {

    @Inject
    private RedisKeyGenerator redisKeyGenerator;

    @Inject
    private MedalRedis medalRedis;

    @Inject
    private UserMedalBoManager userMedalBoManager;

    /**
     * 加锁
     *
     * @param lockName       锁名
     * @param expectedLockMs 预期锁定时间，单位毫秒
     * @returnl
     */
    public boolean lock(String lockName, long expectedLockMs) {
        String key = redisKeyGenerator.genLockKey(lockName);
        String result = medalRedis.set(key, String.valueOf(System.currentTimeMillis()), "NX", "PX", expectedLockMs);
        log.info("lock, lockKey={}, expectedLockMs={}, result={}", key, expectedLockMs, result);
        return "OK".equalsIgnoreCase(result);
    }

    /**
     * 解锁
     *
     * @param lockName 锁名
     */
    public void unLock(String lockName) {
        String key = redisKeyGenerator.genLockKey(lockName);
        Integer delCount = medalRedis.del(key);
        log.info("unLock, lockKey={}, delCount={}", key, delCount);
    }

    /**
     * 获取勋章列表
     *
     * @param userId 用户ID
     * @return
     */
    public List<UserMedalBo> getMedalList(final long userId) {
        return userMedalBoManager.getList(userId);
    }
}
