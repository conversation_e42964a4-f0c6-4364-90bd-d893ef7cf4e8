package fm.lizhi.xm.vip.calculator;

import com.google.common.base.Optional;
import fm.lizhi.xm.vip.bean.wealth.WealthStatisticsBean;
import fm.lizhi.xm.vip.bo.CalculationConfigBo;
import fm.lizhi.xm.vip.manager.wealth.bean.WealthStatisticsBeanManager;
import fm.lizhi.xm.vip.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import java.util.Date;
import java.util.List;

@Slf4j
public abstract class AbstractCalculator implements Calculator {

    @Inject
    private WealthStatisticsBeanManager wealthStatisticsBeanManager;

    /**
     * 获取金币数量数组
     *
     * @return
     */
    protected abstract long[] getCoinAmountArray();

    /**
     * 获取计算配置
     *
     * @return
     */
    protected abstract List<CalculationConfigBo> getCalculationConfigs();

    /**
     * 获取等级图片模板
     *
     * @return
     */
    protected abstract String getLevelImgTemplate();

    @Override
    public long calCoinAmount(int level) {
        long[] coinAmountArray = getCoinAmountArray();
        if (coinAmountArray == null || level >= coinAmountArray.length) {
            log.error("calCoinAmount fail, coinArray is null or level >= coinArray.length, level={}, coinArray.length={}", level, coinAmountArray == null ? -1 : coinAmountArray.length);
            return Long.MAX_VALUE;
        }
        return coinAmountArray[level];
    }

    @Override
    public int calLevel(long coinAmount) {
        long[] coinAmountArray = getCoinAmountArray();
        for (int level = 0; level < coinAmountArray.length; level++) {
            if (coinAmountArray[level] > coinAmount) {
                return level - 1;
            }
        }
        return 0;
    }

    @Override
    public long calLitUpCoinAmount(int level) {
        for (CalculationConfigBo configBo : getCalculationConfigs()) {
            if (configBo.getStartLevel() <= level && level <= configBo.getEndLevel()) {
                return configBo.getLitUpCoinAmount();
            }
        }
        return Long.MAX_VALUE;
    }

    @Override
    public Date calExtinguishTime(long userId, Date currentExtinguishTime) {
        Optional<WealthStatisticsBean> preStatOpt = wealthStatisticsBeanManager.get(userId, TimeUtil.calStartOfPreviousMonth());
        Optional<WealthStatisticsBean> currStatOpt = wealthStatisticsBeanManager.get(userId, TimeUtil.calStartOfCurrentMonth());
        // 熄灭时间
        Date extinguishTime = new Date();
        // 判断上个月的财富统计
        if (preStatOpt.isPresent()) {
            WealthStatisticsBean wealthStatisticsBean = preStatOpt.get();
            int startLevel = calLevel(wealthStatisticsBean.getStartCoinAmount());
            if (startLevel == 0) {
                extinguishTime = TimeUtil.calStartOfNextMonth();
            } else {
                //todo pujinbin
                long lightUpCoinAmount = calLitUpCoinAmount(startLevel);
                long coinAmount = wealthStatisticsBean.getEndCoinAmount() - wealthStatisticsBean.getStartCoinAmount();
                if (lightUpCoinAmount > 0 && coinAmount >= lightUpCoinAmount) {
                    // 上个月消费的金币数满足点亮条件
                    extinguishTime = TimeUtil.calStartOfNextMonth();
                }
            }
        }
        // 判断当月的财富统计
        if (currStatOpt.isPresent()) {
            WealthStatisticsBean wealthStatisticsBean = currStatOpt.get();
            // 按起始等级计算
            int startLevel = calLevel(wealthStatisticsBean.getStartCoinAmount());
            if (startLevel == 0) {
                extinguishTime = TimeUtil.calStartOfNextMonth();
            }
            long coinAmount = wealthStatisticsBean.getEndCoinAmount() - wealthStatisticsBean.getStartCoinAmount();
            long startLightUpCoinAmount = calLitUpCoinAmount(startLevel);
            if (startLightUpCoinAmount > 0 && coinAmount >= startLightUpCoinAmount) {
                // 当月消费的金币数满足点亮条件
                extinguishTime = TimeUtil.calStartOfNextTwoMonth();
            }
            // 按结束等级计算
            int endLevel = calLevel(wealthStatisticsBean.getEndCoinAmount());
            long endLightUpCoinAmount = calLitUpCoinAmount(endLevel);
            if (endLightUpCoinAmount > 0 && coinAmount >= endLightUpCoinAmount) {
                // 当月消费的金币数满足点亮条件
                extinguishTime = TimeUtil.calStartOfNextTwoMonth();
            }
        }
        if (currentExtinguishTime.compareTo(extinguishTime) >= 0) {
            return currentExtinguishTime;
        }
        return extinguishTime;
    }

    @Override
    public double calAspect(int level) {
        for (CalculationConfigBo configBo : getCalculationConfigs()) {
            if (configBo.getStartLevel() <= level && level <= configBo.getEndLevel()) {
                return configBo.getIconAspect();
            }
        }
        return DEFAULT_ASPECT;
    }

    @Override
    public String calLevelImg(int level) {
        return getLevelImgTemplate().replaceAll(LEVEL_REGEX, String.valueOf(level));
    }
}
