package fm.lizhi.xm.vip.dao.entity.decorate.migrate;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 座驾和用户的关联表
 *
 * @date 2023-08-31 04:42:16
 */
@Table(name = "`mount_user_relevance`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MountUserRelevancePO {
    /**
     * 逻辑主键
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 座驾ID
     */
    @Column(name= "`mount_id`")
    private Long mountId;

    /**
     * 座驾等级
     */
    @Column(name= "`mount_level`")
    private Integer mountLevel;

    /**
     * svga素材地址
     */
    @Column(name= "`svga_material_url`")
    private String svgaMaterialUrl;

    /**
     * 用户是否正在使用
     */
    @Column(name= "`is_using`")
    private Integer isUsing;

    /**
     * 生效时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 进入房间的公告
     */
    @Column(name= "`enter_msg`")
    private String enterMsg;

    /**
     * 创建日期
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改日期
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    /**
     * appId
     */
    @Column(name= "`app_id`")
    private Long appId;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", mountId=").append(mountId);
        sb.append(", mountLevel=").append(mountLevel);
        sb.append(", svgaMaterialUrl=").append(svgaMaterialUrl);
        sb.append(", isUsing=").append(isUsing);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", enterMsg=").append(enterMsg);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", appId=").append(appId);
        sb.append("]");
        return sb.toString();
    }
}