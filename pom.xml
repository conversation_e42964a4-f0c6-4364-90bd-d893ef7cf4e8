<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>fm.lizhi.ocean.wavecenter</groupId>
    <artifactId>lz-ocean-wavecenter</artifactId>
    <packaging>pom</packaging>
    <version>1.5.7</version>

    <modules>
        <module>wavecenter-start</module>
        <module>wavecenter-common</module>
        <module>wavecenter-base</module>
        <module>wavecenter-api</module>
        <module>wavecenter-domain</module>
        <module>wavecenter-infrastructure</module>
        <module>wavecenter-service</module>
    </modules>

    <properties>
        <!-- ====================        项目构建的属性        ==================== -->

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
        <maven.compiler.parameters>true</maven.compiler.parameters>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
        <maven-install-plugin.version>3.1.0</maven-install-plugin.version>
        <maven-deploy-plugin.version>3.0.0</maven-deploy-plugin.version>
        <maven-assembly-plugin.version>2.6</maven-assembly-plugin.version>
        <maven-site-plugin.version>3.12.1</maven-site-plugin.version>

        <!-- ====================        基础架构的属性        ==================== -->

        <!-- 基础架构BOM -->
        <lz-common-dependencies-bom.version>2.0.45</lz-common-dependencies-bom.version>
        <lz-commons-queue.version>3.0.23</lz-commons-queue.version>

        <autoapi-maven-plugin.version>1.0.0.RC</autoapi-maven-plugin.version>
        <mybatis-generator-maven-plugin.version>1.3.5</mybatis-generator-maven-plugin.version>
        <datastore-generator.version>1.4.5</datastore-generator.version>
        <!-- xxl-job -->
        <dispatcher-executor.version>2.0.11</dispatcher-executor.version>

        <!-- ====================        基础架构服务依赖        ==================== -->

        <apm-toolkit-trace.version>5.0.0-RC2</apm-toolkit-trace.version>
        <lz-common-romefs-javasdk.version>2.4</lz-common-romefs-javasdk.version>
        <lz-commons-rome-push-cm-api.version>1.2.0</lz-commons-rome-push-cm-api.version>

        <!-- ====================        第二方框架属性        ==================== -->

        <lamp-common.version>1.1.17</lamp-common.version>
        <lz-common-trade-query-center-api.version>1.4.7</lz-common-trade-query-center-api.version>
        <wave-server-common-context.version>1.0.9</wave-server-common-context.version>
        <!-- ====================        第三方框架属性        ==================== -->

        <jakarta.validation-api.version>2.0.2</jakarta.validation-api.version>
        <hibernate-validator.version>6.0.16.Final</hibernate-validator.version>
        <mapstruct.version>1.6.2</mapstruct.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <lombok.version>1.18.30</lombok.version>
        <hutool.version>5.8.27</hutool.version>
        <okhttp.version>4.9.3</okhttp.version>
        <kotlin-stdlib.version>1.3.70</kotlin-stdlib.version>

        <!-- ====================        平台服务依赖        ==================== -->

        <lz-commons-verify-api.version>1.0.9</lz-commons-verify-api.version>
        <lz-common-accountcenter-api.version>1.0.14</lz-common-accountcenter-api.version>

        <!-- ====================        风控服务依赖        ==================== -->

        <lz-account-security-api.version>2.2.3</lz-account-security-api.version>
        <lz-content-review-api.version>4.3.6</lz-content-review-api.version>

        <!-- ====================        交易服务依赖        ==================== -->
        <lz-common-trade-query-center-api.version>1.4.7</lz-common-trade-query-center-api.version>

        <!-- ====================        创作者服务依赖        ==================== -->

        <wave-api.version>1.3.5</wave-api.version>

        <!-- ====================        西米服务依赖        ==================== -->
        <lz-xm-user-account-api.version>1.3.0</lz-xm-user-account-api.version>
        <lz-xm-family-api-vserion>1.3.0</lz-xm-family-api-vserion>
        <lz-xm-security-api.version>1.0.7</lz-xm-security-api.version>
        <lz-xm-room-api.version>1.3.5</lz-xm-room-api.version>
        <lz-xm-vip-api.version>1.6.7</lz-xm-vip-api.version>
        <lz-xm-content-api.version>1.1.3</lz-xm-content-api.version>
        <lz-xm-social-api.version>1.1.4</lz-xm-social-api.version>

        <!-- ====================        黑叶服务依赖        ==================== -->
        <lz-hy-family-api.version>1.3.33</lz-hy-family-api.version>
        <lz-hy-user-account-api.version>1.0.20-SNAPSHOT</lz-hy-user-account-api.version>
        <lz-hy-security-api.version>1.0.0-SNAPSHOT</lz-hy-security-api.version>
        <!--    黑叶没有打正式包    -->
        <lz-hy-activity-api.version>2.2.6-SNAPSHOT</lz-hy-activity-api.version>
        <lz-hy-room-api.version>3.6.6-SNAPSHOT</lz-hy-room-api.version>
        <!--    黑叶没有打正式包    -->
        <lz-hy-vip-api.version>1.0.27</lz-hy-vip-api.version>
        <lz-hy-social-api.version>1.2.2</lz-hy-social-api.version>
        <lz-hy-content-api.version>1.0.25</lz-hy-content-api.version>
        <lz-hy-common-util.version>1.5.25-SNAPSHOT</lz-hy-common-util.version>
        <lz-hy-core-api.version>1.9.9</lz-hy-core-api.version>

        <!-- ====================        PP服务依赖        ==================== -->

        <lz-pp-family-api.version>1.5.6</lz-pp-family-api.version>
        <lz-pp-user-account-api.version>1.7.0</lz-pp-user-account-api.version>
        <lz-trade-contract-api.version>2.1.4</lz-trade-contract-api.version>
        <lz-pp-security-api.version>1.0.8</lz-pp-security-api.version>
        <lz-pp-common-util.version>1.9.6</lz-pp-common-util.version>
        <lz-pp-room-api.version>3.4.2</lz-pp-room-api.version>
        <lz-pp-vip-api.version>2.9.6</lz-pp-vip-api.version>
        <lz-pp-content-api.version>1.6.7</lz-pp-content-api.version>
        <lz-pp-social-api.version>1.11.1</lz-pp-social-api.version>


        <!-- ====================        支付服务依赖        ==================== -->
        <lz-common-pay-settle.version>2.1.20</lz-common-pay-settle.version>

        <!-- ====================        重定位API依赖        ==================== -->

        <wave-relocation.version>2.0.8</wave-relocation.version>


    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- ====================        基础架构的依赖        ==================== -->

            <!-- 基础架构bom -->
            <dependency>
                <groupId>fm.lizhi.common</groupId>
                <artifactId>lz-common-dependencies-bom</artifactId>
                <version>${lz-common-dependencies-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.commons</groupId>
                <artifactId>lz-commons-queue</artifactId>
                <scope>compile</scope>
            </dependency>

            <!-- ====================        基础架构服务依赖        ==================== -->

            <dependency>
                <groupId>fm.lizhi.common</groupId>
                <artifactId>dispatcher-executor</artifactId>
                <version>${dispatcher-executor.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.common</groupId>
                <artifactId>lz-common-romefs-javasdk</artifactId>
                <version>${lz-common-romefs-javasdk.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.commons</groupId>
                <artifactId>lz-commons-rome-push-cm-api</artifactId>
                <version>${lz-commons-rome-push-cm-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-all</artifactId>
                <version>1.7.2.15</version>
            </dependency>

            <!-- ====================        第二方框架依赖        ==================== -->

            <!-- 神灯通用组件 -->
            <dependency>
                <groupId>fm.lizhi.ocean.lamp</groupId>
                <artifactId>lamp-common-config</artifactId>
                <version>${lamp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.ocean.lamp</groupId>
                <artifactId>lamp-common-context</artifactId>
                <version>${lamp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.ocean.lamp</groupId>
                <artifactId>lamp-common-util</artifactId>
                <version>${lamp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.ocean.lamp</groupId>
                <artifactId>lamp-common-logging</artifactId>
                <version>${lamp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.ocean.lamp</groupId>
                <artifactId>lamp-common-generic</artifactId>
                <version>${lamp-common.version}</version>
            </dependency>
            <!-- 创作者通用上下文 -->
            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>wave-server-common-context</artifactId>
                <version>${wave-server-common-context.version}</version>
            </dependency>


            <!-- ====================        第三方框架依赖        ==================== -->

            <!-- bean校验api, 支持泛型注解 -->
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${jakarta.validation-api.version}</version>
            </dependency>
            <!-- hibernate校验实现, 移除掉validation-api, 采用jakarta.validation-api -->
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>validation-api</artifactId>
                        <groupId>javax.validation</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 运行时类型映射 -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <!-- 灯塔支持跨线程路由 -->
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${apm-toolkit-trace.version}</version>
            </dependency>
            <!-- 支持ThreadLocal的线程池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <!-- apache集合类新版, 推荐 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <!-- apache集合类, 不推荐 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!-- Okhttp是罗马上传要求的4.x版本 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <!-- kotlin是罗马上传要求的版本 -->
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>${kotlin-stdlib.version}</version>
            </dependency>

            <!-- ====================        平台服务依赖        ==================== -->

            <dependency>
                <groupId>fm.lizhi.common.verify</groupId>
                <artifactId>lz-commons-verify-api</artifactId>
                <version>${lz-commons-verify-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-common-accountcenter-api</artifactId>
                <version>${lz-common-accountcenter-api.version}</version>
            </dependency>

            <!-- ====================        风控服务依赖        ==================== -->

            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-account-security-api</artifactId>
                <version>${lz-account-security-api.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-content-review-api</artifactId>
                <version>${lz-content-review-api.version}</version>
            </dependency>

            <!-- ====================        交易服务依赖        ==================== -->
            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-common-trade-query-center-api</artifactId>
                <version>${lz-common-trade-query-center-api.version}</version>
            </dependency>

            <!-- ====================        创作者服务依赖        ==================== -->

            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>user-export-api</artifactId>
                <version>${wave-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>user-export-pojo-api</artifactId>
                <version>${wave-api.version}</version>
                <scope>compile</scope>
            </dependency>


            <!-- ====================        西米服务依赖        ==================== -->
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-user-account-api</artifactId>
                <version>${lz-xm-user-account-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-vip-api</artifactId>
                <version>${lz-xm-vip-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-family-api</artifactId>
                <version>${lz-xm-family-api-vserion}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-security-api</artifactId>
                <version>${lz-xm-security-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>wave-relocation-xm-api</artifactId>
                <version>${wave-relocation.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-room-api</artifactId>
                <version>${lz-xm-room-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-content-api</artifactId>
                <version>${lz-xm-content-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-social-api</artifactId>
                <version>${lz-xm-social-api.version}</version>
            </dependency>


            <!-- ====================        黑叶服务依赖        ==================== -->
            <dependency>
                <groupId>fm.hy.family</groupId>
                <artifactId>lz-hy-family-api</artifactId>
                <version>${lz-hy-family-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-user-account-api</artifactId>
                <version>${lz-hy-user-account-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.security</groupId>
                <artifactId>lz-hy-security-api</artifactId>
                <version>${lz-hy-security-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-activity-api</artifactId>
                <version>${lz-hy-activity-api.version}</version>
                <scope>compile</scope>
                <exclusions>
                    <exclusion>
                        <groupId>fm.lizhi.security</groupId>
                        <artifactId>lz-hy-security-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-room-api</artifactId>
                <version>${lz-hy-room-api.version}</version>
                <scope>compile</scope>
                <exclusions>
                    <exclusion>
                        <groupId>fm.lizhi.hy</groupId>
                        <artifactId>lz-hy-content-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>wave-relocation-hy-api</artifactId>
                <version>${wave-relocation.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-common-util</artifactId>
                <version>${lz-hy-common-util.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-vip-api</artifactId>
                <version>${lz-hy-vip-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-content-api</artifactId>
                <version>${lz-hy-content-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-social-api</artifactId>
                <version>${lz-hy-social-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-core-api</artifactId>
                <version>${lz-hy-core-api.version}</version>
            </dependency>


            <!-- ====================        pp服务依赖        ==================== -->
            <dependency>
                <groupId>fm.pp.family</groupId>
                <artifactId>lz-pp-family-api</artifactId>
                <version>${lz-pp-family-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-user-account-api</artifactId>
                <version>${lz-pp-user-account-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.trade</groupId>
                <artifactId>lz-trade-contract-api</artifactId>
                <version>${lz-trade-contract-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-security-api</artifactId>
                <version>${lz-pp-security-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-common-util</artifactId>
                <version>${lz-pp-common-util.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.live</groupId>
                <artifactId>lz-pp-room-api</artifactId>
                <version>${lz-pp-room-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-vip-api</artifactId>
                <version>${lz-pp-vip-api.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-content-api</artifactId>
                <version>${lz-pp-content-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-social-api</artifactId>
                <version>${lz-pp-social-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>wave-relocation-pp-api</artifactId>
                <version>${wave-relocation.version}</version>
                <scope>compile</scope>
            </dependency>

            <!-- ====================        支付服务依赖        ==================== -->
            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-common-pay-settle-api</artifactId>
                <version>${lz-common-pay-settle.version}</version>
                <scope>compile</scope>
            </dependency>


        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <!-- 编译插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <!-- 源码插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>make-source</id>
                            <phase>package</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!-- 安装插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${maven-install-plugin.version}</version>
                </plugin>
                <!-- 部署插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                </plugin>
                <!-- 装配插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>${maven-assembly-plugin.version}</version>
                    <configuration>
                        <descriptors>
                            <descriptor>src/main/assembly/assembly.xml</descriptor>
                        </descriptors>
                    </configuration>
                    <executions>
                        <execution>
                            <id>make-assembly</id>
                            <phase>package</phase>
                            <goals>
                                <goal>single</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!-- 站点插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${maven-site-plugin.version}</version>
                </plugin>
                <!-- protobuf dc 插件编译 -->
                <plugin>
                    <groupId>fm.lizhi.commons</groupId>
                    <artifactId>autoapi-maven-plugin</artifactId>
                    <version>${autoapi-maven-plugin.version}</version>
                </plugin>
                <!-- mybatis生成插件 -->
                <plugin>
                    <groupId>org.mybatis.generator</groupId>
                    <artifactId>mybatis-generator-maven-plugin</artifactId>
                    <version>${mybatis-generator-maven-plugin.version}</version>
                    <configuration>
                        <verbose>true</verbose>
                        <overwrite>true</overwrite>
                        <configurationFile>generator/mybatisGeneratorConfig.xml</configurationFile>
                    </configuration>
                    <dependencies>
                        <!-- datastore生成插件 -->
                        <dependency>
                            <groupId>fm.lizhi.common</groupId>
                            <artifactId>datastore-generator</artifactId>
                            <version>${datastore-generator.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <!-- 源码插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <!-- 安装插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
            </plugin>
            <!-- 部署插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>central</id>
            <name>Central</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
        </repository>
        <repository>
            <id>codehaus-snapshots</id>
            <name>Codehaus Snapshots</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>false</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Maven Plugin Repository</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
            <layout>default</layout>
            <snapshots></snapshots>
            <releases>
                <updatePolicy>never</updatePolicy>
            </releases>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>release</id>
            <url>http://maven.lizhi.fm:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://maven.lizhi.fm:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

</project>
