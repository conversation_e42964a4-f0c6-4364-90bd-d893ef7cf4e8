package fm.lizhi.pp.vip.manager.validate;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.pp.vip.bean.VipUserInfo;
import fm.lizhi.pp.vip.config.PpVipConfig;
import fm.lizhi.pp.vip.constant.VipEnum;
import fm.lizhi.pp.vip.protocol.PpVipBaseProto;

import com.google.inject.Inject;

import static fm.lizhi.pp.vip.utils.TimeUtil.ONE_DAY_MILLS_SECONDS;

@AutoBindSingleton
public class PurchaseVipValidator {

    @Inject
    private PpVipConfig ppVipConfig;

    /**
     * 校验购买类型
     *
     * @param purchaseType 购买类型
     * @param vipUserInfo  用户贵族信息
     * @param purchaseVip
     * @return
     */
    public boolean validatePurchaseType(PpVipBaseProto.PurchaseType purchaseType, VipUserInfo vipUserInfo, VipEnum purchaseVip) {
        boolean isVip = false;
        if (null != vipUserInfo && (vipUserInfo.getExpireTime().getTime() + ppVipConfig.getProtectionDay() * ONE_DAY_MILLS_SECONDS) >= System.currentTimeMillis()) {
            isVip = true;
        }

        if (purchaseType.equals(PpVipBaseProto.PurchaseType.RENEW) && !isVip) {
            // 续费，但是没有贵族信息
            return false;
        }

        if (purchaseType.equals(PpVipBaseProto.PurchaseType.RENEW) && purchaseVip.getId().intValue() != vipUserInfo.getVipId()) {
            // 续费 & 贵族有效或保护期 & 贵族不同
            return false;
        }

        if (purchaseType.equals(PpVipBaseProto.PurchaseType.PURCHASE)) {
            // 购买 & 贵族有效期内
            if (null != vipUserInfo && vipUserInfo.getExpireTime().getTime() >= System.currentTimeMillis()) {
                // & 购买的贵族大于等于当前贵族
                if (purchaseVip.getLevel() <= VipEnum.getVipEnum(vipUserInfo.getVipId()).getLevel()) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 校验购买的财富等级和当前等级的关系
     *
     * @param vipType     购买的贵族信息
     * @param vipUserInfo 用户的贵族信息
     */
    public boolean validateVipType(PpVipBaseProto.VipType vipType, VipUserInfo vipUserInfo) {
        // 非贵族或贵族过期，可购买任意等级
        if (null == vipUserInfo || vipUserInfo.getExpireTime().getTime() < System.currentTimeMillis()) {
            return true;
        }

        VipEnum purchaseVip = VipEnum.getVipEnum(vipType.getNumber());
        VipEnum userVip = VipEnum.getVipEnum(vipUserInfo.getVipId());
        if (purchaseVip.getLevel() < userVip.getLevel()) {
            // 购买的等级小于当前等级
            return false;
        }
        return true;
    }
}
