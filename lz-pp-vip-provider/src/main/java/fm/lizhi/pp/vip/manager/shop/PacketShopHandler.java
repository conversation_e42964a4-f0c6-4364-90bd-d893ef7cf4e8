package fm.lizhi.pp.vip.manager.shop;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.live.pp.constant.MenuTypeEnum;
import fm.lizhi.pp.social.constant.chat.ChatType;
import fm.lizhi.pp.user.account.device.protocol.PpUserDeviceBaseProto;
import fm.lizhi.pp.util.utils.ConfigUtil;
import fm.lizhi.pp.util.utils.UrlUtils;
import fm.lizhi.pp.util.utils.VersionUtil;
import fm.lizhi.pp.vip.bean.shop.ShopExtInfoDto;
import fm.lizhi.pp.vip.bean.shop.ShopInfoVo;
import fm.lizhi.pp.vip.config.PpVipConfig;
import fm.lizhi.pp.vip.constant.ShopGoodsType;
import fm.lizhi.pp.vip.dao.po.ShopGoodsInfo;
import fm.lizhi.pp.vip.dao.po.ShopSaleRecord;
import fm.lizhi.pp.vip.manager.ChatManager;
import fm.lizhi.pp.vip.manager.UserDeviceManager;
import fm.lizhi.pp.vip.manager.decorate.DecorateManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/12 11:08
 */
@Component
public class PacketShopHandler implements ShopHandler {
    @Autowired
    private ShopManager shopManager;
    @Autowired
    private ChatManager chatManager;
    @Autowired
    private PpVipConfig ppVipConfig;
    @Autowired
    private DecorateManager decorateManager;
    @Autowired
    private ShopGoodsManager shopGoodsManager;
    @Autowired
    private UserDeviceManager userDeviceManager;

    @Override
    public int getType() {
        return ShopGoodsType.PACKET_SHOP.getType();
    }
    @Override
    public void sendPersonMsgAfterGive(ShopGoodsInfo goodsInfo, ShopSaleRecord record) {
        long upTime = shopManager.getNewestShopUpTime();
        long buyUserId = record.getBuyUserId();
        long targetUserId = record.getTargetUserId();
        chatManager.sendCostomMsg(
                buyUserId, targetUserId, ChatType.DECORATION_MALL_V2.getValue(),
                buildPacketJson(upTime, buyUserId, targetUserId, goodsInfo).toJSONString()
        );


        List<ShopInfoVo> filterList = null;
        // 解析套装中的商品
        List<ShopExtInfoDto> extInfoDtoList = JSONArray.parseArray(goodsInfo.getExtra(), ShopExtInfoDto.class);
        if (!CollectionUtils.isEmpty(extInfoDtoList)) {
            // 过滤出资料卡背景、消息尾灯商品
            filterList = shopGoodsManager.composeSubList(extInfoDtoList).stream()
                    .filter(e -> e.getProductType() == ShopGoodsType.CARD_BG.getType() || e.getProductType() == ShopGoodsType.TAILLIGHT.getType())
                    .collect(Collectors.toList());
        }
        // 套装不含有资料卡背景、消息尾灯
        if (CollectionUtils.isEmpty(filterList)) {
            return;
        }
        PpUserDeviceBaseProto.UserLoginDevice userLoginDevice = userDeviceManager.getUserLoginDevice(record.getTargetUserId());
        if (userLoginDevice == null) {
            return;
        }
        // 新版本
        if (VersionUtil.meetMinVersion(userLoginDevice.getDeviceName(), userLoginDevice.getAppVersion(), ConfigUtil.bizUtilsConf.getShopV3IOSMinVersion(), ConfigUtil.bizUtilsConf.getShopV3AndroidMinVersion(), Integer.MAX_VALUE)) {
            return;
        }
        String name = filterList.stream().map(ShopInfoVo::getName).collect(Collectors.joining("、"));
        chatManager.sendInfoNotifyMsg(buyUserId, targetUserId,
                ppVipConfig.getShopVersionTips().replace("#{version}", "V7.11.0").replace("#{name}", name), 1);
    }

    /**
     * 构造装扮通知内容json
     *
     * @param upTime
     * @param buyUserId
     * @param targetUserId
     * @param goodsInfo
     * @return
     */
    private JSONObject buildPacketJson(Long upTime, long buyUserId, long targetUserId, ShopGoodsInfo goodsInfo) {
        Integer productType = goodsInfo.getProductType();
        JSONObject contentJson = new JSONObject();
        contentJson.put("senderUserId", buyUserId);
        contentJson.put("receiveUserId", targetUserId);
        contentJson.put("decorationName", goodsInfo.getProductName());
        Optional.ofNullable(decorateManager.getCacheDecorateInfoById(goodsInfo.getProductId())).ifPresent(e -> {
            contentJson.put("decorationName", goodsInfo.getProductName());
            contentJson.put("decorationPic", UrlUtils.appendRomaCdnPrefix(e.getIconUrl()));
        });
        ShopGoodsType typeEnum = ShopGoodsType.getByType(productType);
        if (typeEnum != null) {
            contentJson.put("decorationCateName", typeEnum.getDisplayName());
        }
        contentJson.put("decorationType", productType);
        contentJson.put("mallAction", ppVipConfig.getShopAction().replace("#{source}", "2"));
        String extra = goodsInfo.getExtra();
        String decoBottomDesc = ppVipConfig.getDecoPacketDesc();
        StringBuilder sb = new StringBuilder();
        List<ShopExtInfoDto> extInfoDtoList = JSONArray.parseArray(extra, ShopExtInfoDto.class);
        if (!CollectionUtils.isEmpty(extInfoDtoList)) {
            List<ShopInfoVo> shopInfoVos = shopGoodsManager.composeSubList(extInfoDtoList);
            JSONArray array = new JSONArray();
            for (ShopInfoVo shopInfoVo : shopInfoVos) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("decorationType", shopInfoVo.getProductType());
                shopGoodsManager.composeExtImgInfo(shopInfoVo, shopInfoVo.getProductType());
                jsonObject.put("decorationPic", shopInfoVo.getImg());
                array.add(jsonObject);
                sb.append("「").append(shopInfoVo.getName()).append("」、");
            }
            contentJson.put("packList", array);
        }
        String sbString = sb.toString();
        if (StringUtils.isNotBlank(sbString)) {
            decoBottomDesc = String.format(decoBottomDesc, sbString.substring(0, sbString.length() - 1));
        }
        contentJson.put("bottomDesc", decoBottomDesc);
        contentJson.put("redPointTimeStamp", upTime);
        contentJson.put("menuType", MenuTypeEnum.SHOP.getType());
        return contentJson;
    }
}
