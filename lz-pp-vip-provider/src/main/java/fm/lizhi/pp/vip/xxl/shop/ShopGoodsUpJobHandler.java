package fm.lizhi.pp.vip.xxl.shop;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import fm.lizhi.pp.vip.constant.ShopGoodsReceiveEnum;
import fm.lizhi.pp.vip.constant.ShopGoodsType;
import fm.lizhi.pp.vip.constant.ShopSaleStatusType;
import fm.lizhi.pp.vip.constant.ShopStatusType;
import fm.lizhi.pp.vip.dao.ShopGoodsInfoDao;
import fm.lizhi.pp.vip.dao.ShopSaleRecordDao;
import fm.lizhi.pp.vip.dao.po.ShopGoodsInfo;
import fm.lizhi.pp.vip.dao.po.ShopSaleRecord;
import fm.lizhi.pp.common.manager.user.PpNewUserManager;
import fm.lizhi.pp.vip.manager.shop.ShopSaleRecordManager;
import fm.lizhi.pp.vip.manager.shop.ShopSaleStatisticsManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 商品自动上架
 *
 * <AUTHOR>
 * @date 2023/7/14 上午9:51
 * @description
 */
@Slf4j
@JobHandler("ShopGoodsUpJobHandler")
@AutoBindSingleton(baseClass = IJobHandler.class, multiple = true)
public class ShopGoodsUpJobHandler extends IJobHandler {

    private static final long THREE_Mil_SECOND = 3000;

    @Inject
    private ShopSaleRecordManager shopSaleRecordManager;
    @Inject
    private ShopSaleRecordDao shopSaleRecordDao;
    @Inject
    private ShopGoodsInfoDao shopGoodsInfoDao;
    @Inject
    private PpNewUserManager ppNewUserManager;
    @Inject
    private ShopSaleStatisticsManager shopSaleStatisticsManager;
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("execute...");
        int pageNo =1;
        int pageSize = 300;
        int dbSize =  pageSize;
        Date now = new Date();
        DateTime startTime = DateUtil.offsetMinute(now, -5);
        do {
            //获取需要上架的商品
            List<ShopGoodsInfo> list = shopGoodsInfoDao.getNeedToUpList(startTime,now, pageNo, pageSize);
            if(CollectionUtils.isEmpty(list)){
                log.info("no data to handler,pageNo:{},pageSize:{}",pageNo,pageSize);
                break;
            }
            for (ShopGoodsInfo shopGoodsInfo : list) {
                shopGoodsInfo.setUpdateTime(now);
                shopGoodsInfo.setStatus(ShopStatusType.UP_STATUS.getType());
                shopGoodsInfo.setProductSaleStatus(ShopSaleStatusType.ON_SALE.getType());
                if(shopGoodsInfo.getProductType().equals(ShopGoodsType.BAND.getType())){
                    //操作上架，需要判断是否有用户购买了并且生效中的
                    ShopSaleRecord userEffectiveRecord = shopSaleRecordDao.getEffectiveProductRecord(shopGoodsInfo.getProductId());
                    if(Objects.nonNull(userEffectiveRecord)){
                        Integer receiveType = userEffectiveRecord.getReceiveType();
                        if(receiveType.equals(ShopGoodsReceiveEnum.BUY_EFFECTIVE.getValue() )
                                || receiveType.equals(ShopGoodsReceiveEnum.GIVE_EFFECTIVE.getValue() )){
                            shopGoodsInfo.setProductSaleStatus(ShopSaleStatusType.ON_EFFECT.getType());
                        }else{
                            shopGoodsInfo.setProductSaleStatus(ShopSaleStatusType.GIVE_WAIT_EFFECT.getType());
                        }
                    }
                }
                shopGoodsInfoDao.update(shopGoodsInfo);
                shopSaleStatisticsManager.saveShopSaleStatistics(shopGoodsInfo);
            }

            dbSize = list.size();
            //查询下一页
            pageNo++;
        }while (dbSize  == pageSize);
        return SUCCESS;
    }

}
