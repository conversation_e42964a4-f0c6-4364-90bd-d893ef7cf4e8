package fm.lizhi.pp.vip.manager.adaptor;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.pp.vip.bean.PurchaseVipInfo;
import fm.lizhi.pp.vip.constant.PurchaseVipStatusEnum;
import fm.lizhi.pp.vip.protocol.PpVipBaseProto;
import fm.lizhi.pp.vip.protocol.PpVipRequestProto;

import java.util.Date;

@AutoBindSingleton
public class PurchaseVipInfoAdaptor {

    /**
     * bean 转 proto
     * @param purchaseVipInfo 数据库结果对象
     * @return
     */
    public PpVipRequestProto.PurchaseVipRecord covertBean2Proto(PurchaseVipInfo purchaseVipInfo) {
        Date now = new Date();
        PpVipRequestProto.PurchaseVipRecord.Builder builder = PpVipRequestProto.PurchaseVipRecord.newBuilder();
        builder.setActualPrice(purchaseVipInfo.getActualPrice());
        builder.setDiscount(purchaseVipInfo.getDiscount());
        builder.setCreateTime(purchaseVipInfo.getCreateTime().getTime());
        Date endTime = purchaseVipInfo.getEndTime();
        if (null != endTime) {
            builder.setEndTime(endTime.getTime());
        } else {
            builder.setEndTime(0);
        }
        builder.setExpireTime(purchaseVipInfo.getExpireTime().getTime());
        builder.setNjId(purchaseVipInfo.getNjId());
        builder.setPurchaseType(PpVipBaseProto.PurchaseType.valueOf(purchaseVipInfo.getPurchaseType()));
        builder.setStartTime(purchaseVipInfo.getStartTime().getTime());
        if (PurchaseVipStatusEnum.INVALID.getStatus() == purchaseVipInfo.getStatus()) {
            builder.setStatus(purchaseVipInfo.getStatus());
        } else {
            if (purchaseVipInfo.getExpireTime().before(now)) {
                builder.setStatus(PurchaseVipStatusEnum.INVALID.getStatus());
            } else if (null != endTime && endTime.before(now)) {
                builder.setStatus(PurchaseVipStatusEnum.INVALID.getStatus());
            } else {
                builder.setStatus(PurchaseVipStatusEnum.VALID.getStatus());
            }
        }
        builder.setUserId(purchaseVipInfo.getUserId());
        builder.setVipType(PpVipBaseProto.VipType.valueOf(purchaseVipInfo.getVipType()));
        return builder.build();
    }
}
