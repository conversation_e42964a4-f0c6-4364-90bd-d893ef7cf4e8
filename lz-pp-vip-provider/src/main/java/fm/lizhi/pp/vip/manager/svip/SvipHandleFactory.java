package fm.lizhi.pp.vip.manager.svip;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.pp.svip.constant.SvipEnum;
import fm.lizhi.pp.util.factory.BaseHandleFactory;
import fm.lizhi.pp.vip.manager.svip.handler.*;

import com.google.inject.Inject;

/**
 * <AUTHOR>
 * @since 2021-06-29 10:05
 */
@AutoBindSingleton
public class SvipHandleFactory extends BaseHandleFactory<Integer, SVipAwardHandler> {
    @Inject
    private SVip1LevelHandler sVip1LevelHandler;
    @Inject
    private SVip2LevelHandler sVip2LevelHandler;
    @Inject
    private SVip3LevelHandler sVip3LevelHandler;
    @Inject
    private SVip4LevelHandler sVip4LevelHandler;
    @Inject
    private SVip5LevelHandler sVip5LevelHandler;
    @Override
    protected void registerAllHandler() {
        registerHandler(SvipEnum.SvipLevel.LEVEL_1.getLevel(),sVip1LevelHandler);
        registerHandler(SvipEnum.SvipLevel.LEVEL_2.getLevel(),sVip2LevelHandler);
        registerHandler(SvipEnum.SvipLevel.LEVEL_3.getLevel(),sVip3LevelHandler);
        registerHandler(SvipEnum.SvipLevel.LEVEL_4.getLevel(),sVip4LevelHandler);
        registerHandler(SvipEnum.SvipLevel.LEVEL_5.getLevel(),sVip5LevelHandler);
    }
}
