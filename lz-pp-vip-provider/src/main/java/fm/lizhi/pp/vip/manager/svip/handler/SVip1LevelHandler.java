package fm.lizhi.pp.vip.manager.svip.handler;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.pp.svip.constant.AwardType;
import fm.lizhi.pp.svip.constant.SvipEnum.SvipLevel;
import fm.lizhi.pp.vip.manager.award.AwardDto;
import fm.lizhi.pp.vip.manager.dto.PrivateMsgAwardDto;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * <AUTHOR>
 * svip等级1处理器
 * @since 2021-05-21 15:49
 */
@Slf4j
@AutoBindSingleton
public class SVip1LevelHandler extends AbstractSVipHandler {


    @Override
    public void doAward(long userId, Date awardEndDate) {
        if (!ppVipConfig.isOpenSvipAward()) {
            log.info("svip award not open");
            return;
        }

        medal(userId, SvipLevel.LEVEL_1, awardEndDate);

        // todo 第一次成为vip才会发送
        if (superVipManager.existAwardPrivateMsg(userId)) {
            if (ppVipConfig.isOpenSvipMsg()) {
                privateMessage(userId);
            }
        }
    }


    @Override
    public boolean privateMessage(long userId) {
        AwardDto dto = new AwardDto();
        PrivateMsgAwardDto msg = new PrivateMsgAwardDto();
        msg.setUserId(userId);
        msg.setMsg(ppVipConfig.getSvipPrivateMsg());
        boolean res = awardHandleFactory.getHandler(AwardType.PRIVATE_MSG.name()).sendAward(dto.setPrivateMsgAwardDto(msg));

        if (res) {
            awardRecord(userId, SvipLevel.LEVEL_1.getLevel(), AwardType.PRIVATE_MSG.name(), 0L);
        }

        return res;
    }

}
