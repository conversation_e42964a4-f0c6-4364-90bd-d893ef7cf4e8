package fm.lizhi.pp.vip.api.impl;

import com.google.inject.Inject;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.pp.mall.api.BubbleManageService;
import fm.lizhi.pp.util.utils.ConfigUtil;
import fm.lizhi.pp.vip.bean.BubbleForSaleBean;
import fm.lizhi.pp.vip.manager.Bubble4SaleManager;
import fm.lizhi.pp.vip.manager.utils.LogManager;
import fm.lizhi.pp.mall.protocol.BubbleManageProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/7/12
 */
@ServiceProvider
@Slf4j
public class BubbleManageServiceImpl implements BubbleManageService {

    @Inject
    private Bubble4SaleManager bubble4SaleManager;

    @Override
    public Result<BubbleManageProto.ResponseAddBubble4Sale> addBubble4Sale(BubbleManageProto.AddOrEditBubble bubble) {

        if (ConfigUtil.bizUtilsConf.getBubbleSwitch()) {
            log.error("bubble switch is off addBubble4Sale");
            return new Result<>(1, null);
        }


        LogManager.addServiceLog(bubble.toString());
        if (bubble.getBubbleId() <= 0) {
            return new Result<>(10, null);
        }
        BubbleForSaleBean bean = setBean(bubble);
        return new Result<>(bubble4SaleManager.addBubble4Sale(bean), null);
    }

    @Override
    public Result<BubbleManageProto.ResponseEditBubble4Sale> editBubble4Sale(BubbleManageProto.AddOrEditBubble bubble) {
        if (ConfigUtil.bizUtilsConf.getBubbleSwitch()) {
            log.error("bubble switch is off editBubble4Sale");
            return new Result<>(1, null);
        }
        LogManager.addServiceLog(bubble.toString());
        if (bubble.getBubbleId() <= 0) {
            return new Result<>(10, null);
        }
        BubbleForSaleBean bean = setBean(bubble);
        return new Result<>(bubble4SaleManager.editBubble4Sale(bean), null);
    }

    @Override
    public Result<BubbleManageProto.ResponseDeleteBubble4Sale> deleteBubble4Sale(long bubbleId) {
        if (ConfigUtil.bizUtilsConf.getBubbleSwitch()) {
            log.error("bubble switch is off deleteBubble4Sale");
            return new Result<>(1, null);
        }

        LogManager.addServiceLog("bubbleId={}", bubbleId);
        bubble4SaleManager.deleteBubble(bubbleId);
        return new Result<>(0, null);
    }

    @Override
    public Result<BubbleManageProto.ResponseSortBubble> sortBubble(List<BubbleManageProto.BubbleSort> bubbleSort) {
        if (ConfigUtil.bizUtilsConf.getBubbleSwitch()) {
            log.error("bubble switch is off sortBubble");
            return new Result<>(1, null);
        }
        Map<Long, Integer> map = new HashMap<>(bubbleSort.size());
        for (BubbleManageProto.BubbleSort item : bubbleSort) {
            map.put(item.getBubbleId(), item.getSortNum());
        }
        bubble4SaleManager.sortBubble(map);
        return new Result<>(0, null);
    }

    /**
     * 设置bean
     *
     * @return
     */
    private BubbleForSaleBean setBean(BubbleManageProto.AddOrEditBubble addOrEditBubble) {

        BubbleForSaleBean bean = new BubbleForSaleBean();
        bean.setBubbleId(addOrEditBubble.getBubbleId());
        if (StringUtils.isNotBlank(addOrEditBubble.getName())) {
            bean.setName(addOrEditBubble.getName());
        }
        bean.setUsefulLife(addOrEditBubble.getUsefulLife());
        if (StringUtils.isNotBlank(addOrEditBubble.getUsefulLifeUnit())) {
            bean.setUsefulLifeUnit(addOrEditBubble.getUsefulLifeUnit());
        }
        if (addOrEditBubble.getPrice() >= 0) {
            bean.setPrice(addOrEditBubble.getPrice());
        }
        if (addOrEditBubble.getGiveCrystals() >= 0) {
            bean.setGiveCrystals(addOrEditBubble.getGiveCrystals());
        }
        bean.setSortNum(addOrEditBubble.getSortNum());
        if (StringUtils.isNotBlank(addOrEditBubble.getDescription())) {
            bean.setDescription(addOrEditBubble.getDescription());
        }
        if (StringUtils.isNotBlank(addOrEditBubble.getPreviewImage())) {
            bean.setPreviewImage(addOrEditBubble.getPreviewImage());
        }
        if (StringUtils.isNotBlank(addOrEditBubble.getBackgroundColor())) {
            bean.setBackgroundColor(addOrEditBubble.getBackgroundColor());
        }
        bean.setState((short) addOrEditBubble.getState());
        return bean;
    }

}
