package fm.lizhi.pp.vip.manager.message;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.queue.service.Consumer;
import fm.lizhi.payment.event.InnerBuyCompleteEvent;
import fm.lizhi.pp.util.constant.AppIdConstant;
import fm.lizhi.pp.vip.config.PpVipConfig;
import fm.lizhi.pp.vip.manager.executor.AsyncTaskExecutor;
import fm.lizhi.trade.kylin.protocol.GiftBagEvent;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import com.google.inject.Inject;

/**
 * 充值消费kafka
 *
 * <AUTHOR>
 * @date 2019-11-22
 */
@Slf4j
@AutoBindSingleton
public class InnerBuyCompleteMsgReceiver implements BaseMessageReceiver {
    @Inject
    private PpVipConfig ppVipConfig;

    @Inject
    private AsyncTaskExecutor asyncTaskExecutor;

    @Override
    public boolean isProduct() {
        return ppVipConfig.isProduct();
    }

    @Override
    public String getKafkaKey() {
        return ppVipConfig.getConsumeGiftKafkaKey();
    }

    @Override
    public String getTopic() {
        return ppVipConfig.getInnerBuyCompleteEventTopics();
    }

    @Override
    public String getGroupId() {
        return ppVipConfig.getInnerBuyCompleteEventGroupId();
    }

    @Override
    @PostConstruct
    public void init() {
        boolean isPropProduct = isProduct();
        String kafkaKey = getKafkaKey();
        String groupId = getGroupId();
        String topic = getTopic();
        log.info("isProduct={}, kafkaKey={}, groupId={}, topic={}", isPropProduct, kafkaKey, groupId, topic);

        try {

            new Consumer(isPropProduct, kafkaKey, groupId, topic, DEFAULT_THREAD_COUNT, DEFAULT_THREAD_COUNT,
                    (key, msg, timestamp, partition, offset) -> handleMessage(msg)).start();

        } catch (Exception e) {
            log.error("init error", e);
        }
    }

    @Override
    public boolean handleMessage(@NonNull String msg) {
        InnerBuyCompleteEvent innerBuyCompleteEvent = null;
        try {
            innerBuyCompleteEvent = JSON.parseObject(msg, InnerBuyCompleteEvent.class);
        } catch (JSONException e) {
            log.error("innerBuyCompleteEvent msg format error, gift msg={}", msg);
            return true;
        }
        if (null == innerBuyCompleteEvent) {
            log.warn("innerBuyCompleteEvent msg format error, gift msg={}", msg);
            return true;
        }
        // 不是pp的消息
        if (!AppIdConstant.isValidApp(innerBuyCompleteEvent.getAppId())) {
            return true;
        }
        //异步贵族购买
        asyncTaskExecutor.addGiftBagEvent(toGiftBagEvent(innerBuyCompleteEvent));
        return true;
    }


    /**
     * 转换GiftBagEvent对象
     *
     * @param innerBuyCompleteEvent
     * @return
     */
    private GiftBagEvent toGiftBagEvent(InnerBuyCompleteEvent innerBuyCompleteEvent) {
        GiftBagEvent giftBagEvent = new GiftBagEvent();
        giftBagEvent.setAppId(innerBuyCompleteEvent.getAppId());
        giftBagEvent.setOrderAmount(innerBuyCompleteEvent.getAmount());
        giftBagEvent.setRechargeQuantity(String.valueOf(innerBuyCompleteEvent.getCoinAmount()));
        giftBagEvent.setUserId(innerBuyCompleteEvent.getUserId());
        giftBagEvent.setOrderTime(innerBuyCompleteEvent.getCreateTime());
        giftBagEvent.setTradeOrderId(innerBuyCompleteEvent.getOrderId().toString());
        return giftBagEvent;
    }

}
