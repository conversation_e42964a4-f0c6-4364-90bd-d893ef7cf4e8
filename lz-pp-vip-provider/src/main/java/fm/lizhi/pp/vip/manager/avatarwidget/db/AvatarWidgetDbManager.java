package fm.lizhi.pp.vip.manager.avatarwidget.db;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.datastore.client.DataStoreAckMsg;
import fm.lizhi.datastore.query.Compare;
import fm.lizhi.datastore.query.Condition;
import fm.lizhi.datastore.query.Query;
import fm.lizhi.pp.vip.dbbean.AvatarWidgetBean;
import fm.lizhi.pp.vip.utils.EnvUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述：
 * 头像挂件DB操作
 *
 * <AUTHOR>
 * @date 2018-07-23 15:26
 */
@AutoBindSingleton
public class AvatarWidgetDbManager {

    public static final int TYPE_ACT = 1; // type = 1 活动头像挂件类型
    public static final int TYPE_GIFT = 2; // type = 2 礼物头像挂件类型

    public AvatarWidgetBean get(long id) {
        AvatarWidgetBean bean = new AvatarWidgetBean();
        bean.setId(id);
        if (EnvUtils.isOnline()){
            bean.setEnv(0);
        }
        DataStoreAckMsg ack = bean.load();
        if (ack.getRcode() == 0) {
            return bean;
        }
        return null;
    }
}
