package fm.lizhi.pp.vip.dao.po;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 商品售卖记录表
 *
 * @date 2024-01-26 02:27:13
 */
@Table(name = "`shop_sale_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShopSaleRecord {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 购买用户ID
     */
    @Column(name= "`buy_user_id`")
    private Long buyUserId;

    /**
     * 受益人用户ID
     */
    @Column(name= "`target_user_id`")
    private Long targetUserId;

    /**
     * 商城商品ID
     */
    @Column(name= "`goods_id`")
    private Long goodsId;

    /**
     * 业务ID，如：装扮ID、靓号
     */
    @Column(name= "`product_id`")
    private Long productId;

    /**
     * 装扮商品类型，0：靓号，1：气泡，2：头像框，3：座驾。。。
     */
    @Column(name= "`product_type`")
    private Integer productType;

    /**
     * 时间类型
     */
    @Column(name= "`time_type`")
    private Integer timeType;
    /**
     * 状态，0：过期，1：未过期
     */
    @Column(name= "`time_status`")
    private Integer timeStatus;
    /**
     * 开始时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 购买折扣价格
     */
    @Column(name= "`buy_discount_price`")
    private Long buyDiscountPrice;

    /**
     * 购买原价格
     */
    @Column(name= "`buy_org_price`")
    private Long buyOrgPrice;

    /**
     * 购买数量
     */
    @Column(name= "`buy_count`")
    private Long buyCount;

    /**
     * 购买总价
     */
    @Column(name= "`buy_total_price`")
    private Long buyTotalPrice;

    /**
     * 支付状态，参照ShopGoodsRecordPayStatus枚举
     */
    @Column(name= "`pay_status`")
    private Integer payStatus;

    /**
     * 交易引擎冻结ID
     */
    @Column(name= "`frozen_id`")
    private Long frozenId;

    /**
     * 接收时间
     */
    @Column(name= "`receive_time`")
    private Date receiveTime;

    /**
     * 0:待生效，1:购买生效，2：赠送待生效(靓号)，3：赠送生效，4：赠送失败(靓号)
     */
    @Column(name= "`receive_type`")
    private Integer receiveType;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 购买渠道  1:百货 2：礼物栏
     */
    @Column(name= "`buy_source`")
    private Integer buySource;

    /**
     * 直播节目id
     */
    @Column(name= "`live_id`")
    private Long liveId;

    /**
     * 附加信息
     */
    @Column(name= "`ext_info`")
    private String extInfo;
}