package fm.lizhi.pp.oss.dao;


import fm.lizhi.pp.oss.pojo.Copywriting;

import java.util.List;

/**
 * 分享文案，开播文案Dao
 * Created in 2017-12-19 18:01.
 * <p>
 *
 * <AUTHOR>
 */
public interface CopywritingDao {

    /**
     * @param offset   起始索引
     * @param pageSize 页容量
     * @param type     类型  0 分享文案 1 开播文案（消息推送文案）
     * @param delAble  是否可删除 0 否 1 是
     * @param status   状态 0 删除 1 可用
     * @return
     */
    List<Copywriting> find(int offset, int pageSize, int type, int delAble, int status);

    Copywriting findById(int id);

    void add(Copywriting cw);

    void update(Copywriting cw);

    void updateStatus(int id, int status);

    /**
     * 查询总数
     *
     * @param status
     * @return
     */
    long findTotal(int type, int delAble, int status);

    /**
     * 根据状态查询数据
     *
     * @param status
     * @return
     */
    List<Copywriting> findByStatus(int status);

    /**
     * 获取所有文案标签
     *
     * @param type
     * @return
     */
    List<String> findAllTags(int type);
}
