package fm.lizhi.pp.oss.adapter;

import com.alibaba.fastjson.JSONArray;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.pp.oss.config.LzOssConfig;
import fm.lizhi.pp.oss.pojo.CommentStyle;
import fm.lizhi.pp.oss.protocol.CommentStyleProto;
import fm.lizhi.pp.util.utils.UrlUtils;
import fm.lizhi.pp.vip.bean.dto.DecorateExtInfoDto;
import fm.lizhi.pp.vip.bean.enums.DecorateExtInfoKeyEnum;
import fm.lizhi.pp.vip.constant.DecorateTypeEnum;
import fm.lizhi.pp.vip.dao.po.decorate.DecoratePO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created in 2018-05-15 11:23.
 *
 * <AUTHOR>
 */
@AutoBindSingleton
public class CommentStyleAdapter {

    @Inject
    private LzOssConfig lzOssConfig;

    /**
     * 转换成Protocol buffer对象
     *
     * @param commentStyles
     * @return
     */
    public List<CommentStyleProto.CommentStyle> adapter(List<CommentStyle> commentStyles) {
        List<CommentStyleProto.CommentStyle> result = new ArrayList<>();
        if (commentStyles != null && commentStyles.size() > 0) {
            for (CommentStyle commentStyle : commentStyles) {
                result.add(this.adaptation(commentStyle));
            }
        }
        return result;
    }


    public List<CommentStyleProto.CommentStyle> adapterDecorate(List<DecoratePO> decoratePOS) {
        List<CommentStyleProto.CommentStyle> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(decoratePOS)) {
            return result;
        }
        for (DecoratePO commentStyle : decoratePOS) {
            CommentStyleProto.CommentStyle adaptation = this.adaptation(commentStyle);
            if (Objects.nonNull(adaptation)){
                result.add(adaptation);
            }
        }
        return result;
    }
    /**
     * 将CommentStyle转换为Protocol Buffer对象
     *
     * @param commentStyle
     * @return
     */
    public CommentStyleProto.CommentStyle adaptation(DecoratePO commentStyle) {
        CommentStyleProto.CommentStyle.Builder builder = CommentStyleProto.CommentStyle.newBuilder();
        builder.setCreateTime(commentStyle.getCreateTime().getTime());
        builder.setModifyTime(commentStyle.getModifyTime().getTime());
        builder.setId(commentStyle.getId());
        builder.setStatus(commentStyle.getDeleteFlag() == 1 ? 0: 1);
        builder.setIosImage(commentStyle.getThumbUrl());
        builder.setAndImage(commentStyle.getThumbUrl());

        String extInfo = commentStyle.getExtInfo();
        if (StringUtils.isBlank(extInfo)){
            return  null;
        }
        List<DecorateExtInfoDto> decorateExtInfoDtos = JSONArray.parseArray(extInfo, DecorateExtInfoDto.class);
        if (CollectionUtils.isEmpty(decorateExtInfoDtos)) {
            return  null;
        }
        for (DecorateExtInfoDto decorateExtInfoDto : decorateExtInfoDtos) {
            if (decorateExtInfoDto.getType().equals(DecorateTypeEnum.BUBBLE.getType())
                    && decorateExtInfoDto.getKey().equals(DecorateExtInfoKeyEnum.bubbleColor.getKey())) {
                builder.setAndColor(decorateExtInfoDto.getValue());
                builder.setIosColor(decorateExtInfoDto.getValue());
                break;
            }
        }

        return builder.build();
    }
    /**
     * 将CommentStyle转换为Protocol Buffer对象
     *
     * @param commentStyle
     * @return
     */
    public CommentStyleProto.CommentStyle adaptation(CommentStyle commentStyle) {
        CommentStyleProto.CommentStyle.Builder builder = CommentStyleProto.CommentStyle.newBuilder();
        builder.setCreateTime(commentStyle.getCreateTime().getTime());
        builder.setModifyTime(commentStyle.getModifyTime().getTime());
        builder.setId(commentStyle.getId());
        builder.setStatus(commentStyle.getStatus());
        if (!commentStyle.getIosImage().startsWith("http") && 1 == lzOssConfig.getCommentStyleImageUrlRenderingEnable()) {
            builder.setIosImage(UrlUtils.appendRomaCdnPrefix(commentStyle.getIosImage()));
        } else {
            builder.setIosImage(commentStyle.getIosImage());
        }
        if (!commentStyle.getAndImage().startsWith("http") && 1 == lzOssConfig.getCommentStyleImageUrlRenderingEnable()) {
            builder.setAndImage(UrlUtils.appendRomaCdnPrefix(commentStyle.getAndImage()));
        } else {
            builder.setAndImage(commentStyle.getAndImage());
        }
        builder.setAndColor(commentStyle.getAndColor());
        builder.setIosColor(commentStyle.getIosColor());
        return builder.build();
    }

    public CommentStyleProto.CommentStyle adaptationDecorate(DecoratePO commentStyle) {
        CommentStyleProto.CommentStyle.Builder builder = CommentStyleProto.CommentStyle.newBuilder();
        builder.setCreateTime(commentStyle.getCreateTime().getTime());
        builder.setModifyTime(commentStyle.getModifyTime().getTime());
        builder.setId(commentStyle.getId());
        builder.setStatus(commentStyle.getDeleteFlag() == 1 ? 0: 1);
        builder.setIosImage(UrlUtils.appendRomaCdnPrefix(commentStyle.getThumbUrl()));
        builder.setAndImage(UrlUtils.appendRomaCdnPrefix(commentStyle.getThumbUrl()));
        String extInfo = commentStyle.getExtInfo();
        if (StringUtils.isBlank(extInfo)){
            return  null;
        }
        List<DecorateExtInfoDto> decorateExtInfoDtos = JSONArray.parseArray(extInfo, DecorateExtInfoDto.class);
        if (CollectionUtils.isEmpty(decorateExtInfoDtos)) {
            return  null;
        }
        for (DecorateExtInfoDto decorateExtInfoDto : decorateExtInfoDtos) {
            if (decorateExtInfoDto.getType().equals(DecorateTypeEnum.BUBBLE.getType())
                    && decorateExtInfoDto.getKey().equals(DecorateExtInfoKeyEnum.bubbleColor.getKey())) {
                builder.setAndColor(decorateExtInfoDto.getValue());
                builder.setIosColor(decorateExtInfoDto.getValue());
            }
        }
        return builder.build();
    }


}
