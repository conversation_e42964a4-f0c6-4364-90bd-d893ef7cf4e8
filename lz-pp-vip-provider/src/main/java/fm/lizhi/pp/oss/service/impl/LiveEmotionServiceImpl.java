package fm.lizhi.pp.oss.service.impl;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.pp.oss.config.LzOssConfig;
import fm.lizhi.pp.oss.service.LiveEmotionService;
import fm.lizhi.pp.util.bean.EmotionInfo;
import fm.lizhi.pp.util.utils.ConfigUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * Created in 2018-04-04 10:40.<br/>
 *
 * <AUTHOR>
 */
@AutoBindSingleton(LiveEmotionService.class)
public class LiveEmotionServiceImpl implements LiveEmotionService {
    private static Logger LOGGER = LoggerFactory.getLogger(LiveEmotionServiceImpl.class);

    @Inject
    private LzOssConfig lzOssConfig;

    /**
     * 获取表情列表
     *
     * @return
     */
    @Override
    public List<EmotionInfo> getEmotionList() {
        return JsonUtil.loadsArray(ConfigUtil.bizUtilsConf.getEmotionConfig(), EmotionInfo.class);
    }

    /**
     * 根据表情id获取表情信息
     *
     * @param emotionId 表情id
     * @return 表情信息
     */

    @Override
    public EmotionInfo getEmotionById(Long emotionId) {
        for (EmotionInfo emotionInfo : getEmotionList()) {
            if (emotionInfo.getEmotionId().longValue() == emotionId.longValue()) {
                return emotionInfo;
            }
        }
        return null;
    }
}
