package fm.lizhi.pp.oss.service.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.live.commons.utils.DefaultThreadFactory;
import fm.lizhi.pp.oss.config.LzOssConfig;
import fm.lizhi.pp.oss.service.LiveStatisticsDayLoader;
import fm.lizhi.pp.oss.service.LiveStatisticsDayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Live相关数据统计,供大数据调用进行推荐算法计算.
 * 存储的时间维度: 天为维度
 *
 * @Since 2018-08-06
 */
@AutoBindSingleton(LiveStatisticsDayService.class)
public class LiveStatisticsDayServiceImpl implements LiveStatisticsDayService {
    private static Logger LOGGER = LoggerFactory.getLogger(LiveStatisticsDayServiceImpl.class);

    private LoadingCache<String, Long> liveAmountCache;

    private ListeningExecutorService refreshPools = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(10, new DefaultThreadFactory("cron-update-live")));

    @Inject
    private LzOssConfig lzOssConfig;

    @Inject
    private LiveStatisticsDayLoader liveStatisticsDayLoader;

    @PostConstruct
    private void init() {
        this.liveAmountCache = CacheBuilder.newBuilder()
                .refreshAfterWrite(lzOssConfig.getLiveStatisticsCacheRefreshTime(), TimeUnit.SECONDS)
                .expireAfterAccess(5, TimeUnit.MINUTES)
                .maximumSize(100000)
                .build(new CacheLoader<String, Long>() {
                    @Override
                    public Long load(String key) throws Exception {
                        String[] params = key.split("_");
                        LOGGER.info("liveAmountCache roomId={}, day={}", params[0], params[1]);
                        return liveStatisticsDayLoader.findLiveIntervalAmount(Long.parseLong(params[0]), Integer.parseInt(params[1]));
                    }

                    @Override
                    public ListenableFuture<Long> reload(final String key, Long oldValue) throws Exception {
                        return refreshPools.submit(() -> {
                            String[] params = key.split("_");
                            LOGGER.info("refresh roomId={}, day={}", params[0], params[1]);
                            return liveStatisticsDayLoader.findLiveIntervalAmount(Long.parseLong(params[0]), Integer.parseInt(params[1]));
                        });
                    }
                });
    }

    /**
     * 获取X分钟内直播间内的总收入-Cache
     */
    @Override
    public long findLiveIntervalAmount(long roomId, int days) {
        if (lzOssConfig.isLiveCache()) {
            return liveAmountCache.getUnchecked(roomId + "_" + days);
        }

        return liveStatisticsDayLoader.findLiveIntervalAmount(roomId, days);
    }
}
