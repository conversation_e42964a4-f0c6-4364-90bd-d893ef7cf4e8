package fm.lizhi.pp.wealth.xxl;

import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import fm.lizhi.pp.wealth.bean.WealthUpgradeMessage;
import fm.lizhi.pp.wealth.message.KafkaMsgProducer;
import fm.lizhi.pp.wealth.redis.WealthRedis;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@JobHandler("CompensationWealthJobHandler")
@AutoBindSingleton(baseClass = IJobHandler.class, multiple = true)
public class CompensationWealthJobHandler extends IJobHandler {

    @Inject
    private KafkaMsgProducer kafkaMsgProducer;
    @Inject
    private WealthRedis wealthRedis;


    @Override
    public ReturnT<String> execute(String s) {
        log.info("execute...");
        String wealthUpgradeMessageStr = null;
        try {
            Set<String> wealthDatas = wealthRedis.smembers("WEALTH_DATA");
            for (String wealthData : wealthDatas) {
                wealthUpgradeMessageStr = wealthData;
                WealthUpgradeMessage wealthUpgradeMessage =
                        JSONObject.parseObject(wealthData, WealthUpgradeMessage.class);

                boolean sendResult = kafkaMsgProducer.sendWealthUpgradeMsg(wealthUpgradeMessage);
                if (!sendResult) {
                    log.error("sendWealthUpgradeMsg fail. msg={}", wealthData);
                    return ReturnT.FAIL;
                }

                Long sremResult = wealthRedis.srem("wealthDatas", wealthData);
                if (sremResult == null) {
                    log.error("sendWealthUpgradeMsg sremResult fail. msg={}", wealthData);
                    return ReturnT.FAIL;
                }

                log.info("sendWealthUpgradeMsg success. msg={}", wealthData);
            }
        } catch (Exception e) {
            log.error("sendWealthUpgradeMsg ex. msg=" + wealthUpgradeMessageStr, e);
        }

        return SUCCESS;
    }
}
