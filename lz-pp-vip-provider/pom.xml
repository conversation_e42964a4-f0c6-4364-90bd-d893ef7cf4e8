<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>fm.lizhi.pp</groupId>
        <artifactId>lz-pp-vip</artifactId>
        <version>2.9.7</version>
    </parent>

    <artifactId>lz-pp-vip-provider</artifactId>

    <properties>
        <!-- 默认环境，可以通过-D参数覆盖 -->
        <lz.env>online</lz.env>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <!-- DC服务升级 -->
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>lz-service-frame-guice-adapter</artifactId>
        </dependency>
        <!-- API -->
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-vip-api</artifactId>
            <version>2.9.7</version>
        </dependency>
        <!-- 基础组件 -->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-serviceclient</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-datastore</artifactId>
        </dependency>
        <!-- 替换datastore-guice为springboot的依赖包 -->
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>datastore-spring-boot-starter</artifactId>
        </dependency>
        <!-- 新增guice兼容性依赖包 -->
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>datastore-mysql-guice-compatibility-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-config</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-activity-api</artifactId>
            <version>2.8.5</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-util</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-queue</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.push</groupId>
            <artifactId>lz-push-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>
        <!-- 工具库 -->
        <dependency>
            <groupId>com.googlecode.protobuf-java-format</groupId>
            <artifactId>protobuf-java-format</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-unit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>
        <!-- 限流组件 -->
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-all</artifactId>
        </dependency>
        <!-- RPC -->
        <dependency>
            <groupId>fm.lizhi.live</groupId>
            <artifactId>lz-pp-room-api</artifactId>
            <version>3.2.5</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-user-account-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-pp-comment-api</artifactId>
            <version>2.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.app</groupId>
            <artifactId>lz-app-client-protobuf</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-push-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-social-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-common-trade-kylin-api</artifactId>
            <version>2.5.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-payment-api</artifactId>
            <version>6.9.8</version>
        </dependency>
        <dependency>
            <groupId>fm.pp.family</groupId>
            <artifactId>lz-pp-family-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
        </dependency>
        <!-- xxl-job-core -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.live</groupId>
            <artifactId>lz-pp-pp-core-api</artifactId>
            <version>1.10.17</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.live</groupId>
            <artifactId>lz-pp-pp-common-api</artifactId>
            <version>1.4.9</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-security-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-wealth-api</artifactId>
            <version>0.0.7-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius-core</artifactId>
            <version>0.5.12</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.pplive</groupId>
            <artifactId>lz-pp-gift-api</artifactId>
            <version>2.13.1</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.live</groupId>
            <artifactId>lz-pp-amusement-utils</artifactId>
            <version>2.6.9</version>
            <exclusions>
                <exclusion>
                    <groupId>fm.lizhi.live</groupId>
                    <artifactId>lz-pp-amusement-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>fm.lizhi.live</groupId>
                    <artifactId>lz-live-common-amusement-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.xuxueli</groupId>
                    <artifactId>xxl-job-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>fm.lizhi.live</groupId>
                    <artifactId>lz-live-data-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>fm.lizhi.live</groupId>
                    <artifactId>lz-pp-data-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.live.commons</groupId>
            <artifactId>lz-live-commons</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.amusement.commons</groupId>
            <artifactId>amusement-idempotent</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.live</groupId>
            <artifactId>lz-pp-amusement-task-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-pp-amusement-api</artifactId>
            <version>1.6.3</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-finance-accounting-engines-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptors>
                        <descriptor>config/${lz.env}/resources/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.5</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                    <configurationFile>generator/mybatisGeneratorConfig.xml</configurationFile>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>fm.lizhi.common</groupId>
                        <artifactId>datastore-generator</artifactId>
                        <version>1.4.5</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>
