package fm.lizhi.ocean.wave.heiye.activity.core.model.bean;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

@Data
public class VoteConfigDurationBean {

    /**
     * 时长ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private long id;

    /**
     * 时长title
     */
    private String title;
}
