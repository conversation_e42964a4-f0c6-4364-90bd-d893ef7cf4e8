package fm.lizhi.ocean.wave.common.util;

import fm.lizhi.commons.config.util.JsonUtil;

import java.util.*;

public class DemoUtils {
    public static void main(String[] args) {
        Map<String, Set<String>> providerMethods = new HashMap<>();
        Set<String> stringSet = new HashSet<>();
        stringSet.add("getUser");
        stringSet.add("delUser");

        providerMethods.put("lizhi_XXUserService", stringSet);

        providerMethods.put("ocean_XXUserService", stringSet);

        System.out.println(JsonUtil.dumps(providerMethods));


        Map<String, Set<String>> unImplementStandardAPIProviderMethodMap
                = JsonUtil.loads("{\"lizhi_XXUserService\":[\"delUser\",\"getUser\"],\"ocean_XXUserService\":[\"delUser\",\"getUser\"]}", Map.class);
        System.out.println(JsonUtil.dumps(unImplementStandardAPIProviderMethodMap));
    }
}
