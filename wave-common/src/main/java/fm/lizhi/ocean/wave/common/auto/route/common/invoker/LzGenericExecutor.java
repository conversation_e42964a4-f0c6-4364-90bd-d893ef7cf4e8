package fm.lizhi.ocean.wave.common.auto.route.common.invoker;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.dubbo.generic.GenericService;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.common.auto.route.common.ServiceType;
import fm.lizhi.ocean.wave.common.auto.route.common.ServiceRelocation;
import fm.lizhi.ocean.wave.common.auto.route.lizhigeneric.generic.*;
import fm.lizhi.ocean.wave.common.auto.route.common.chain.ChainInvokingParam;
import fm.lizhi.ocean.wave.common.auto.route.lizhigeneric.standard.generalization.*;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.context.ServiceContext;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import sun.reflect.generics.reflectiveObjects.ParameterizedTypeImpl;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 荔枝泛化调用执行器
 * <AUTHOR>
 * @date 2024/2/20 20:03
 */
@Slf4j
public class LzGenericExecutor implements IExecutor {

    private final int SUCCESS = 0;
    private final int FIRST = 0;

    /**
     * 泛化调用器接口实现列表
     */
    private final List<LzGenericInvoker> genericInvokers = Arrays.asList(
            new LzDomainOpInvoker(),
            new LzPojoInvoker()
    );

    /**
     * RPC接口的超时毫秒数
     */
    private int timeout = 5_000;

    private GenericService genericService;
    /**
     * 服务重定位处理类, 此处是临时方案直接传递, 后续应该修改为过滤器链实现
     */
    private ServiceRelocation serviceRelocation;

    public LzGenericExecutor(GenericService genericService, ServiceRelocation serviceRelocation) {
        this.genericService = genericService;
        this.serviceRelocation = serviceRelocation;
    }

    public LzGenericExecutor(int timeout, GenericService genericService, ServiceRelocation serviceRelocation) {
        this.timeout = timeout;
        this.genericService = genericService;
        this.serviceRelocation = serviceRelocation;
    }

    @Override
    public Result<?> execute(ExecuteContext context, ExecuteParam param) throws Exception {
        Class<?> serviceInterface = param.getServiceClass();
        Method method = param.getMethod();
        Object[] args = param.getArgs();

        //标准接口旧调用方式
        if (ServiceType.STANDARD == param.getServiceType()) {
            return invokeStandardAPI(serviceInterface, method, args);
        }

        String actualServiceName = serviceRelocation.getOriginalName(serviceInterface.getName());
        String methodName = method.getName();
        // 调用对应的代理逻辑
        LzInvokingContext gContext = new LzInvokingContext();
        gContext.setGenericService(genericService);
        gContext.setServiceInterface(serviceInterface);
        gContext.setRelocation(!serviceInterface.getName().equals(actualServiceName));
        gContext.setServiceRelocation(serviceRelocation);
        LzInvokingParam lzInvokingParam = new LzInvokingParam();
        lzInvokingParam.setMethod(method);
        lzInvokingParam.setArgs(args);
        lzInvokingParam.setBusinessEvnEnum(getBusinessEvnEnum());
        for (LzGenericInvoker genericInvoker : genericInvokers) {
            // 根据上下文判定是否匹配
            if (genericInvoker.supports(gContext, lzInvokingParam)) {
                return (Result<?>) genericInvoker.invoke(gContext, lzInvokingParam);
            }
        }
        throw new IllegalStateException("No supported generic invoker, service: " + actualServiceName + ", method: " + methodName);
    }

    private BusinessEvnEnum getBusinessEvnEnum() {
        BusinessEvnEnum businessEvnEnum = ContextUtils.getBusinessEvnEnum();
        Validate.notNull(businessEvnEnum, "businessEnvEnum is null");
        return businessEvnEnum;
    }

    private Result<?> invokeStandardAPI(Class<?> interfaceClass, Method method, Object[] args) throws Exception{
        ServiceContext serviceContext = ContextUtils.getContext();
        if (null == serviceContext) {
            log.error("service context empty");
            throw new RuntimeException("service context empty");
        }

        BusinessEvnEnum businessEvnEnum = serviceContext.getBusinessEvnEnum();
        ParseGeneralizationReflectMethodBO methodBO = ParseGeneralizationReflectMethodBO.getInstance(method, args);
        //判断是否是空实现的接口。是，直接返回空值
        if (GeneralizationSelectorUtil.isEmptyImplement(businessEvnEnum, interfaceClass, method.getName())) {
            return new Result(SUCCESS, null);
        }

        IMethodInvoker invoker = GeneralizationSelectorUtil.getMethodInvoker(businessEvnEnum, interfaceClass);

        log.info("start invoke remote standard api.`protoBuf={}`className={}`businessName={}`methodName={}`args={}`bo={}"
                , invoker.isProtoBufInvoker(), interfaceClass.getSimpleName(), businessEvnEnum.getName()
                , method.getName(), JsonUtil.dumps(args), JsonUtil.dumps(methodBO));

        //这里是需要调用泛化
        boolean isProtoBufInvoker = invoker.isProtoBufInvoker();
        Result<Map<String, Object>> result = invoker.isProtoBufInvoker()
                    ? invoker.invoke(getProtoBufGeneralizationBO(method, interfaceClass, businessEvnEnum, methodBO))
                    : invoker.invoke(getPOJOGeneralizationBO(method, interfaceClass, businessEvnEnum, methodBO));

        log.info("end invoke remote standard api.`protoBuf={}`className={}`businessName={}`methodName={}`bo={}`result={}"
                , invoker.isProtoBufInvoker(), interfaceClass.getSimpleName(), businessEvnEnum.getName()
                , method.getName(), JsonUtil.dumps(methodBO), JsonUtil.dumps(result));

        if (result.rCode() != SUCCESS || result.target() == null) {
            return result;
        }

        Type type = method.getGenericReturnType();
        //获取到泛性数组类型
        Type[] actualTypes = ((ParameterizedTypeImpl) type).getActualTypeArguments();

        Class<?> tClass = null;
        try {
            tClass = (Class) actualTypes[FIRST];
        } catch (Exception e) {
            log.error("not support return type.`protoBuf={}`className={}`businessName={}`methodName={}`bo={}`result={}"
                    , isProtoBufInvoker, interfaceClass.getSimpleName(), businessEvnEnum.getName()
                    , method.getName(), JsonUtil.dumps(methodBO), JsonUtil.dumps(result), e);
            throw new RuntimeException("not support return type");
        }

        return new Result(result.rCode(), JsonUtils.convertValue(result.target(), tClass));
    }

    private ProtoBufGeneralizationBO getProtoBufGeneralizationBO(Method method
            , Class<?> interfaceClass
            , BusinessEvnEnum businessEvnEnum
            , ParseGeneralizationReflectMethodBO methodBO) throws ClassNotFoundException {
        Method protoBufMethod = GeneralizationReflectUtil.getProtoBufMethodByPOJOClassNameAndMethodName(
                interfaceClass.getName(), method.getName());
        Service service = GeneralizationReflectUtil.getProtoBufMethodAnnotationByMethod(protoBufMethod);
        return ProtoBufGeneralizationBO.builder()
                .region(ConfigUtils.getRegion())
                .businessEnv(businessEvnEnum.getBusinessEnv())
                .domain(service.domain())
                .op(service.op())
                .serviceName(GeneralizationReflectUtil.getProtoBufClassNameByPOJOClassName(interfaceClass.getName()))
                .methodName(method.getName())
                .requestMap(methodBO.getParameterMap())
                .timeout(this.timeout)
                .build();
    }

    private POJOGeneralizationBO getPOJOGeneralizationBO(Method method
            , Class<?> interfaceClass
            , BusinessEvnEnum businessEvnEnum
            , ParseGeneralizationReflectMethodBO methodBO) {
        return POJOGeneralizationBO.builder()
                .region(ConfigUtils.getRegion())
                .businessEnv(businessEvnEnum.getBusinessEnv())
                .serviceName(interfaceClass.getName())
                .methodName(method.getName())
                .paramTypes(methodBO.getParameterTypeNames())
                .requestParams(methodBO.getParameterObjects())
                .timeout(this.timeout)
                .build();
    }
}
