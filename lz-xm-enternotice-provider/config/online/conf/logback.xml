<?xml version="1.0"?>
<configuration  scan="true" scanPeriod="10 seconds" debug="false">
	<!--定义属性-->
	<property name="PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"/>

	<!-- 控制台日志 -->
	<appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${PATTERN}</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>INFO</level>
		</filter>
	</appender>

	<!--INFO日志，接收外部请求的日志-->
	<appender name="infoAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_PATH}/info.log</file>
		<append>true</append>
		<encoder>
			<pattern>${PATTERN}</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/info.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
		</rollingPolicy>
	</appender>
	<appender name="asyncInfoAppender" class="ch.qos.logback.classic.AsyncAppender">
		<queueSize>10000</queueSize>
		<appender-ref ref="infoAppender"/>
	</appender>

	<!--访问日志，调用其他服务的日志-->
	<appender name="accessAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_PATH}/access.log</file>
		<append>true</append>
		<encoder>
			<pattern>${PATTERN}</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/access.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
		</rollingPolicy>
	</appender>
	<appender name="asyncAccessAppender" class="ch.qos.logback.classic.AsyncAppender">
		<queueSize>10000</queueSize>
		<appender-ref ref="accessAppender"/>
	</appender>

	<!--服务日志，项目里打印的日志都输出到这个文件中-->
	<appender name="serverAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_PATH}/server.log</file>
		<append>true</append>
		<encoder>
			<pattern>${PATTERN}</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/server.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
		</rollingPolicy>
	</appender>
	<appender name="asyncServerAppender" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="serverAppender"/>
		<queueSize>10000</queueSize>
	</appender>

	<!-- 错误日志 -->
	<appender name="errorAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_PATH}/error.log</file>
		<encoder>
			<pattern>${PATTERN}</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/error.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
		</rollingPolicy>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>WARN</level>
		</filter>
	</appender>
	<appender name="asyncErrorAppender" class="ch.qos.logback.classic.AsyncAppender">
		<queueSize>4096</queueSize>
		<appender-ref ref="errorAppender"/>
	</appender>

	<!--Cat埋点日志-->
	<appender name="catAppender" class="com.dianping.cat.logback.CatLogbackAppender"/>

	<!--传输日志-->
	<!--<appender name="transportAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
	<!--    <file>${LOG_PATH}/transport.log</file>-->
	<!--    <append>true</append>-->
	<!--    <encoder>-->
	<!--        <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>-->
	<!--    </encoder>-->
	<!--    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
	<!--        <fileNamePattern>${LOG_PATH}/transport.%d{yyyy-MM-dd_HH}.log</fileNamePattern>-->
	<!--    </rollingPolicy>-->
	<!--</appender>-->
	<!--<appender name="asyncTransportAppender" class="ch.qos.logback.classic.AsyncAppender">-->
	<!--    <queueSize>10000</queueSize>-->
	<!--    <appender-ref ref="transportAppender"/>-->
	<!--</appender>-->

	<!--关闭日志输出-->
	<logger name="fm.lizhi.commons.connector.async.transport" level="off" additivity="false"/>
	<logger name="fm.lizhi.commons.connector.async.transport.handler" level="off" additivity="false"/>
	<logger name="lz-local-cache" additivity="false"/>
	<!--关闭日志输出-->

	<logger name="info" level="info" additivity="false">
		<appender-ref ref="asyncInfoAppender"/>
	</logger>
	<logger name="lizhi" level="info" additivity="false">
		<appender-ref ref="asyncInfoAppender"/>
	</logger>
	<logger name="access_log" level="info" additivity="false">
		<appender-ref ref="asyncAccessAppender"/>
	</logger>

	<root level="info">
		<appender-ref ref="asyncServerAppender"/>
		<appender-ref ref="asyncErrorAppender"/>
		<appender-ref ref="catAppender" />
	</root>
</configuration>
