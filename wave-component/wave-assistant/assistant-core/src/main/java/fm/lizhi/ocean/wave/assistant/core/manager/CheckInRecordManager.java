package fm.lizhi.ocean.wave.assistant.core.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.assistant.core.datastore.entity.WaveCheckInRecord;
import fm.lizhi.ocean.wave.assistant.core.datastore.mapper.WaveCheckInRecordMapper;
import fm.lizhi.ocean.wave.assistant.core.datastore.mapper.ext.WaveCheckInRecordExtMapper;
import fm.lizhi.ocean.wave.assistant.core.datastore.redis.CheckInRedisDao;
import fm.lizhi.ocean.wave.assistant.core.remote.service.CheckInServiceRemote;
import fm.lizhi.ocean.wave.common.util.RedisLock;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.common.util.TimeUtil;
import io.shardingsphere.core.routing.router.masterslave.MasterSlaveRouteOnceVisitedHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 排班打卡记录管理类
 */
@Slf4j
@Component
public class CheckInRecordManager {

    @Autowired
    private WaveCheckInRecordMapper waveCheckInRecordMapper;

    @Autowired
    private WaveCheckInRecordExtMapper waveCheckInRecordExtMapper;

    @Autowired
    private CheckInRedisDao checkInRedisDao;

    @Autowired
    private CheckInServiceRemote checkInServiceRemote;


    /**
     * 查询用户最近24小时的打卡记录，不包含当前小时
     *
     * @param scheduleId 用户ID
     * @return 打卡记录列表
     */
    public List<WaveCheckInRecord> queryRecordsByScheduleId(long scheduleId) {
        WaveCheckInRecord checkInRecord = new WaveCheckInRecord();
        checkInRecord.setScheduleId(scheduleId);
        return waveCheckInRecordMapper.selectMany(checkInRecord);
    }

    /**
     * 初始化打卡记录
     *
     * @param appId      应用ID
     * @param scheduleId 档期ID
     * @param userId     用户ID
     */
    public boolean initCheckInRecord(int appId, long scheduleId, long userId, long roomId) {
        String date = DateUtil.formatDateToString(new Date(), "yyyyMMddHH");
        if (checkInRedisDao.hasInitRecordStatMark(appId, scheduleId, userId, date)) {
            //初始化过了，返回成功
            return true;
        }

        //查询一遍数据，避免主从延迟，从主库查询
        WaveCheckInRecord checkInRecord = getUserCheckInRecordByScheduleId(scheduleId, userId, true);
        if (checkInRecord == null) {
            //初始化记录
            boolean res = initRecordAndTask(scheduleId, userId, roomId, appId);
            if (!res) {
                return false;
            }
        }

        //标记已经打卡初始化成功
        checkInRedisDao.setInitRecordStatMark(appId, scheduleId, userId, date);
        return true;
    }


    /**
     * 查询用户档期所在的自然天打卡记录，包含当天小时
     *
     * @param roomId            用户ID
     * @param scheduleStartTime 档期开始时间
     * @param scheduleId        档期ID
     * @return 打卡记录列表
     */
    public List<WaveCheckInRecord> queryRecordsTodayWithCache(long roomId, Date scheduleStartTime, Long scheduleId) {
        //获取档期所在的那天的起始时间，获取自然天的数据
        Date startTime = DateUtil.getDayStart(scheduleStartTime);
        Date endTime = DateUtil.getDayAfter(startTime, 1);
        //如果请求时没有过scheduleStartTime所在的小时范围，则直接从数据库查询，因为此时数据一直在变
        if (scheduleStartTime.getTime() >= TimeUtil.getHourStartTime(new Date()).getTime()) {
            return waveCheckInRecordExtMapper.getRecordListByTime(roomId, startTime, endTime);
        }

        //先从缓存中获取
        List<WaveCheckInRecord> recordCacheList = checkInRedisDao.getRecordCache(roomId, scheduleStartTime);
        if (CollectionUtils.isNotEmpty(recordCacheList)) {
            return recordCacheList;
        }

        //加分布式锁，设置缓存，避免都冲到了数据库
        try (RedisLock lock = checkInRedisDao.getRecordLock(roomId, scheduleStartTime.getTime())) {
            //加锁，拿不到锁等待5s
            if (!lock.lock()) {
                log.warn("queryRecordsTodayWithCache lock fail, roomId:{}", roomId);
                return Collections.emptyList();
            }

            //再从缓存中获取一把
            List<WaveCheckInRecord> recordList = checkInRedisDao.getRecordCache(roomId, scheduleStartTime);
            if (CollectionUtils.isNotEmpty(recordList)) {
                return recordList;
            }

            recordList = queryRecordsByScheduleId(scheduleId);
            checkInRedisDao.setRecordCache(roomId, scheduleStartTime, recordList);
            return recordList;
        } catch (Exception e) {
            log.error("queryRecordsTodayWithCache happen error, roomId:{}", roomId, e);
        }
        return Collections.emptyList();
    }


    /**
     * 获取用户打卡记录
     *
     * @param scheduleId 档期ID
     * @param userId     用户ID
     * @param fromMaster 是否从主库查询
     * @return 打卡记录
     */
    public WaveCheckInRecord getUserCheckInRecordByScheduleId(long scheduleId, long userId, boolean fromMaster) {
        if (fromMaster) {
            //查询主库
            MasterSlaveRouteOnceVisitedHolder.routeMaster();
        }
        WaveCheckInRecord checkInRecord = new WaveCheckInRecord();
        checkInRecord.setScheduleId(scheduleId);
        checkInRecord.setUserId(userId);
        return waveCheckInRecordMapper.selectOne(checkInRecord);
    }


    /**
     * 查询记录列表
     *
     * @param ids ID列表
     * @return 结果
     */
    public List<WaveCheckInRecord> queryRecordListByIds(List<Long> ids) {
        return waveCheckInRecordExtMapper.queryRecordByIds(ids);
    }



    private boolean initRecordAndTask(Long scheduleId, Long userId, Long roomId, int appId) {
        Result<Long> result = checkInServiceRemote.initRecordAndTask(scheduleId, userId, roomId, appId);
        if (RpcResult.isFail(result)){
            log.warn("initRecordAndTask fail, scheduleId:{}, userId:{}, roomId:{}, appId:{}, rCode: {}", scheduleId, userId, roomId, appId, result.rCode());
            return false;
        }
        return result.target() != null && result.target() > 0;
    }
}
