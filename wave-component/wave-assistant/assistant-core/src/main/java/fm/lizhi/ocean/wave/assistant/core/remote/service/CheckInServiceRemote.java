package fm.lizhi.ocean.wave.assistant.core.remote.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.assistant.core.model.bean.CheckInAllMicGiftRecordBean;
import fm.lizhi.ocean.wave.assistant.core.model.bean.CheckInHostConfigBean;
import fm.lizhi.ocean.wave.assistant.core.model.bean.CheckInLightGiftRecordBean;
import fm.lizhi.ocean.wave.assistant.core.model.bean.CheckInMyTaskBean;
import fm.lizhi.ocean.wave.assistant.core.model.param.GetAllMicGiftNotAllocationParam;
import fm.lizhi.ocean.wave.assistant.core.model.param.GetCheckInHostConfigParam;
import fm.lizhi.ocean.wave.assistant.core.model.result.GetCheckInConfigResult;
import fm.lizhi.ocean.wave.assistant.core.model.result.GetCheckInDetailsResult;
import fm.lizhi.ocean.wave.common.model.bean.PageBean;
import fm.lizhi.ocean.wave.platform.api.platform.bean.WaveCheckInHostConfigBean;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestCheckInOperateCore;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestIncrIncomeAndTask;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CheckInServiceRemote {


    /**
     * 获取基础配置
     */
    Result<GetCheckInConfigResult> getConfig(Long roomId, int appId, Long familyId);

    /**
     * 初始化用户记录以及任务
     * @return recordId
     */
    Result<Long> initRecordAndTask(long scheduleId, long userId, long roomId, int appId);

    /**
     * 处理用户收入和任务
     */
    Result<Void> increaseIncomeAndTask(RequestIncrIncomeAndTask request);


    /**
     * 麦序福利数据处理
     */
    Result<Void> checkInOperateCore(RequestCheckInOperateCore request);


    /**
     * 批量获取用户任务和用户档期收光记录
     */
    Result<List<GetCheckInDetailsResult>> getCheckInDetails(List<Long> recordIds, Long scheduleId);

    /**
     * 获取麦序福利版本号
     */
    Result<Long> getCheckInVersion(int appId, long scheduleId);


    /**
     * 获取收光记录列表
     */
    Result<PageBean<CheckInLightGiftRecordBean>> getLightGiftList(Long roomId, Long scheduleId, Integer pageNo, Integer pageSize, int appId);

    /**
     * 我的任务
     */
    Result<List<CheckInMyTaskBean>> getMyTask(long userId, Long roomId, int appId, String date);

    /**
     * 获取当前时间配置的主持人
     */
    Result<CheckInHostConfigBean> getCheckInHostConfigByTimeSlot(GetCheckInHostConfigParam param);

    /**
     * 获取全麦奖励待分配记录
     */
    Result<List<CheckInAllMicGiftRecordBean>> getAllMicNotAllocation(GetAllMicGiftNotAllocationParam param);

    /**
     * 获取全麦奖励记录列表
     */
    Result<PageBean<CheckInAllMicGiftRecordBean>> getAllMicGiftRecordList(Long roomId, Long scheduleId, Integer pageNo, Integer pageSize, int appId);

    /**
     * 分配全麦奖励
     */
    Result<Void> allMicGiftAllocation(Long allMicId, Long allocationUserId, Long operatorUserId);

    /**
     * 更新主持人
     */
    Result<Boolean> updateScheduleHost(long scheduleId, Long hostId);


    // ------ allMicGiftAllocation ----

    /**
     * 全麦奖励分配失败，没有权限
     */
    int ALL_MIC_GIFT_ALLOCATION_NOT_PERMISSION = 2;


}
