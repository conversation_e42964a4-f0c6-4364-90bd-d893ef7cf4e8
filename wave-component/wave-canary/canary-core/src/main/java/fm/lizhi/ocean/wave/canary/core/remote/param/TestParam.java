package fm.lizhi.ocean.wave.canary.core.remote.param;

import lombok.Data;

import java.util.List;

@Data
public class TestParam {

    public Double doubleVal = 0.0;
    public List<Double> doubleValList;

    public Long longVal = 0L;
    public List<Long> longValList;

    public String strVal = "";
    public List<String> strValList;

    public Integer intVal = 1;
    public List<Integer> intValList;

    public Boolean boolVal = false;
    public List<Boolean> boolValList;

    public Float floatVal = 0.0f;
    public List<Float> floatValList;


    public String byteStrVal = "";
    public List<String> byteStringValList;


    public TestParamObj objectVal2InObjectVal1;
    public List<TestParamObj> listObjectVal2InObjectVal1;

    @Data
    public static class TestParamObj {
        public Double doubleVal;
        public List<Double> doubleValList;

        public Long longVal;
        public List<Long> longValList;

        public String strVal;
        public List<String> strValList;

        public Integer intVal;
        public List<Integer> intValList;

        public Boolean boolVal;
        public List<Boolean> boolValList;

        public Float floatVal;
        public List<Float> floatValList;


        public String enumValues;
        public List<String> enumValuesList;


        public String byteStrVal;
        public List<String> byteStrValList;

        public TestParamObj2 objectVal2InObjectVal1;
        public List<TestParamObj2> listObjectVal2InObjectVal1;
    }

    @Data
    public static class TestParamObj2 {
        public Double doubleVal;
        public List<Double> doubleValList;

        public Long longVal;
        public List<Long> longValList;

        public String strVal;
        public List<String> strValList;

        public Integer intVal;
        public List<Integer> intValList;

        public Boolean boolVal;
        public List<Boolean> boolValList;

        public Float floatVal;
        public List<Float> floatValList;


        public String enumValues;
        public List<String> enumValuesList;


        public String byteStrVal;
        public List<String> byteStrValList;
    }




}
