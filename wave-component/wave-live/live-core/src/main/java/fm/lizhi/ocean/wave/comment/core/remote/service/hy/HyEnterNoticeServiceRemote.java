package fm.lizhi.ocean.wave.comment.core.remote.service.hy;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.remote.adapter.hy.HyEnterNoticesByRangeAdapter;
import fm.lizhi.ocean.wave.comment.core.remote.bean.EnterNoticeEntry;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetEnterNoticeStatusRequest;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetEnterNoticesByRangeParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetNewFanNoticesByRangeParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetVehicleEnterNoticesByRangeParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.*;
import fm.lizhi.ocean.wave.comment.core.remote.service.IEnterNoticeServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import hy.fm.lizhi.live.enternotice.api.EnterNoticeService;
import hy.fm.lizhi.live.enternotice.api.VehicleEnterService;
import hy.fm.lizhi.live.enternotice.protocol.EnternoticeProto;
import hy.fm.lizhi.live.enternotice.protocol.VehicleEnterProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 评论服务远程调用实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HyEnterNoticeServiceRemote extends RemoteServiceInvokeFacade implements IEnterNoticeServiceRemote {

    @Autowired
    private VehicleEnterService vehicleEnterService;
    @Autowired
    private EnterNoticeService enterNoticeService;

    @Autowired
    private HyEnterNoticesByRangeAdapter adapter;

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.HEI_YE.equals(evnEnum);
    }

    /**
     * 获取座驾进房公告
     *
     * @param param 获取座驾进房公告参数
     * @return 座驾进房公告结果
     */
    @Override
    public Result<GetVehicleEnterNoticesByRangeResult> getVehicleEnterNoticesByRange(
            GetVehicleEnterNoticesByRangeParam param) {
        // 1. 请求获取座驾进房公告
        Result<VehicleEnterProto.ResponseVehicleEnterNoticesByRange> result =
                vehicleEnterService.vehicleEnterNoticesByRange(
                        param.getLiveId(), param.getStartTimeStamp(), param.getEndTimeStamp());

        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("vehicleEnterNoticesByRange fail rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }

        // 2. 获取进房公告信息列表
        List<EnternoticeProto.EnterNoticeEntry> enterNoticesList = result.target().getEnterNoticesList();
        // 3. 创建进房公告信息列表
        List<EnterNoticeEntry> enterNoticeEntryBeanList = adapter.convertResult(enterNoticesList);


        // 4. 创建进房公告结果
        GetVehicleEnterNoticesByRangeResult getVehicleEnterNoticesByRangeResult = new GetVehicleEnterNoticesByRangeResult();
        getVehicleEnterNoticesByRangeResult.setLiveId(result.target().getLiveId());
        getVehicleEnterNoticesByRangeResult.setStartTimeStamp(result.target().getStartTimeStamp());
        getVehicleEnterNoticesByRangeResult.setEndTimeStamp(result.target().getEndTimeStamp());
        getVehicleEnterNoticesByRangeResult.setEnterNotices(enterNoticeEntryBeanList);

        return new Result<>(result.rCode(), getVehicleEnterNoticesByRangeResult);
    }

    @Override
    public Result<GetNewFanNoticesByRangeResult> getNewFanNoticesByRange(GetNewFanNoticesByRangeParam param) {
        // 1. 请求获取新粉丝进房公告
        Result<EnternoticeProto.ResponseNewFanNoticesByRange> result =
                enterNoticeService.getNewFanNoticesByRange(param.getLiveId(),
                        param.getStartTimeStamp(), param.getEndTimeStamp());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getNewFanNoticesByRange fail rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }

        // 2. 获取进房公告信息列表
        List<EnternoticeProto.EnterNoticeEntry> enterNoticesList = result.target().getEnterNoticesList();
        // 3. 创建进房公告信息列表
        List<EnterNoticeEntry> enterNoticeEntryBeanList = adapter.convertResult(enterNoticesList);


        // 4. 创建进房公告结果
        GetNewFanNoticesByRangeResult getNewFanNoticesByRangeResult = new GetNewFanNoticesByRangeResult();
        getNewFanNoticesByRangeResult.setLiveId(result.target().getLiveId());
        getNewFanNoticesByRangeResult.setStartTimeStamp(result.target().getStartTimeStamp());
        getNewFanNoticesByRangeResult.setEndTimeStamp(result.target().getEndTimeStamp());
        getNewFanNoticesByRangeResult.setEnterNotices(enterNoticeEntryBeanList);

        return new Result<>(result.rCode(), getNewFanNoticesByRangeResult);
    }

    @Override
    public Result<GetEnterNoticesByRangeResult> getEnterNoticesByRange(GetEnterNoticesByRangeParam param) {
        // 1. 请求获取进房公告
        Result<EnternoticeProto.ResponseEnterNoticesByRange> result =
                enterNoticeService.getEnterNoticesByRange(param.getLiveId(),
                        param.getStartTimeStamp(), param.getEndTimeStamp());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getEnterNoticesByRange fail rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }
        // 2. 获取进房公告列表
        List<EnternoticeProto.EnterNoticeEntry> enterNoticesList = result.target().getEnterNoticesList();
        // 3. 进房公告列表
        List<EnterNoticeEntry> enterNoticeEntryBeanList = adapter.convertResult(enterNoticesList);

        // 4. 创建进房公告结果
        GetEnterNoticesByRangeResult getEnterNoticesByRangeResult = new GetEnterNoticesByRangeResult();
        getEnterNoticesByRangeResult.setLiveId(result.target().getLiveId());
        getEnterNoticesByRangeResult.setStartTimeStamp(result.target().getStartTimeStamp());
        getEnterNoticesByRangeResult.setEndTimeStamp(result.target().getEndTimeStamp());
        getEnterNoticesByRangeResult.setEnterNotices(enterNoticeEntryBeanList);

        return new Result<>(result.rCode(), getEnterNoticesByRangeResult);
    }

    @Override
    public Result<GetEnterNoticeStatusResponse> getEnterNoticeStatus(GetEnterNoticeStatusRequest request) {
        Result<EnternoticeProto.ResponseEnterNoticeStatus> responseEnterNoticeStatusResult = getSpringInterfaceProxyBean(EnterNoticeService.class).getEnterNoticeStatus(request.getUserId(), request.getNjId());
        if (responseEnterNoticeStatusResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("hy getEnterNoticeStatus fail rCode={}`userId={}`njId={}", responseEnterNoticeStatusResult.rCode(), request.getUserId(), request.getNjId());
            return new Result<>(responseEnterNoticeStatusResult.rCode(), null);
        }

        EnternoticeProto.ResponseEnterNoticeStatus responseEnterNoticeStatus = responseEnterNoticeStatusResult.target();
        GetEnterNoticeStatusResponse getEnterNoticeStatusResponse = GetEnterNoticeStatusResponse.builder()
                .enterTime(responseEnterNoticeStatus.getLastEnterTime())
                .runningDays(responseEnterNoticeStatus.getRunningDays())
                .userId(responseEnterNoticeStatus.getUserId())
                .anonymous(responseEnterNoticeStatus.getAnonymous()).build();

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, getEnterNoticeStatusResponse);
    }

    @Override
    public Result<GetEnterNoticeTemplateResponse> getEnterNoticeTemplate() {
        Result<EnternoticeProto.ResponseEnterNoticeTemplate> responseEnterNoticeTemplateResult = getSpringInterfaceProxyBean(EnterNoticeService.class).getEnterNoticeTemplate();
        if (responseEnterNoticeTemplateResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("pp getEnterNoticeTemplate fail rCode={}", responseEnterNoticeTemplateResult.rCode());
            return new Result<>(responseEnterNoticeTemplateResult.rCode(), null);
        }
        EnternoticeProto.ResponseEnterNoticeTemplate responseEnterNoticeTemplate = responseEnterNoticeTemplateResult.target();
        GetEnterNoticeTemplateResponse getEnterNoticeTemplateResponse = GetEnterNoticeTemplateResponse.builder()
                .anonymousNoticeTemplate(responseEnterNoticeTemplate.getAnonymousNoticeTemplate())
                .commonNoticeTemplate(responseEnterNoticeTemplate.getCommonNoticeTemplate())
                .runningDaysNoticeTemplate(responseEnterNoticeTemplate.getRunningDaysNoticeTemplate())
                .build();

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, getEnterNoticeTemplateResponse);
    }

    @Override
    public Result<Void> sendVehicleEnterRoomMsg(List<String> notices) {
        try {
            Result<Void> resp = vehicleEnterService.vehicleEnterLive(notices);
            if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("sendVehicleEnterNotices pp - notices:{} rCode:{}", JsonUtil.dumps(notices), resp.rCode());
                return new Result<>(IEnterNoticeServiceRemote.SEND_VEHICLE_ENTER_ROOM_ERROR, null);
            }
            log.info("sendVehicleEnterNotices pp - notices:{}", JsonUtil.dumps(notices));
            return resp;

        } catch (Exception e) {
            log.error("pp vehicle enter live notices:" + JsonUtil.dumps(notices), e);
            return new Result<>(IEnterNoticeServiceRemote.SEND_VEHICLE_ENTER_ROOM_ERROR, null);
        }
    }

    @Override
    public Result<Void> sendEnterRoomMsg(List<String> notices) {
        try {
            Result<Void> resp = enterNoticeService.sendEnterNotices(notices);
            if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.warn("sendEnterNotices hy - notices:{} rCode:{}", JsonUtil.dumps(notices), resp.rCode());
                return new Result<>(IEnterNoticeServiceRemote.SEND_VEHICLE_ENTER_ROOM_ERROR, null);
            }
            log.info("sendEnterNotices hy - notices:{}", JsonUtil.dumps(notices));
            return resp;

        } catch (Exception e) {
            log.error("sendEnterNotices hy error:" + JsonUtil.dumps(notices), e);
            return new Result<>(IEnterNoticeServiceRemote.SEND_VEHICLE_ENTER_ROOM_ERROR, null);
        }
    }
}
