package fm.lizhi.ocean.wave.comment.core.extension.comment.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomRoleService;
import fm.lizhi.ocean.wave.api.live.api.LiveService;
import fm.lizhi.ocean.wave.api.live.param.GetLiveParam;
import fm.lizhi.ocean.wave.api.live.result.GetLiveResult;
import fm.lizhi.ocean.wave.comment.core.constants.CommentMsgCodes;
import fm.lizhi.ocean.wave.comment.core.extension.comment.ISendImageCommentProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.comment.bean.SendImageCommentPostBean;
import fm.lizhi.ocean.wave.comment.core.extension.comment.bean.SendImageCommentPreBean;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.datacenter.comment.pp.api.TransientCommentService;

@Component
public class XmSendImageCommentProcessor implements ISendImageCommentProcessor {

    @Autowired
    private LiveRoomRoleService liveRoomRoleService;

    @Autowired
    private LiveService liveService;


    @Override
    public ResultVO<Void> preprocessor(SendImageCommentPreBean data) {
        //只是判断是否是房主，使用缓存没有问题
        Result<GetLiveResult> liveResult = liveService.getLiveByLocalCache(GetLiveParam.builder().liveId(data.getLiveId()).build());
        if (liveResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            if (liveResult.rCode() == LiveService.GET_LIVE_LOCAL_CACHE_NOT_FOUND) {
                return ResultVO.failure(CommentMsgCodes.LIVE_NOT_EXIST.getCode(), CommentMsgCodes.LIVE_NOT_EXIST.getMsg());
            }
            return ResultVO.failure(CommentMsgCodes.SEND_COMMENT_ERROR.getCode(), CommentMsgCodes.SEND_COMMENT_ERROR.getMsg());
        }
        Long njUserId = liveResult.target().getLive().getUserId();
        long userId = data.getUserId();
        //是否为房主
        if (njUserId.equals(userId)) {
            return ResultVO.success();
        }
        Result<Boolean> roomHost = liveRoomRoleService.isRoomHost(njUserId, userId);
        if (roomHost.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure(CommentMsgCodes.SEND_COMMENT_ERROR.getCode(), CommentMsgCodes.SEND_COMMENT_ERROR.getMsg());
        } else if (!roomHost.target()) {
            return ResultVO.failure(CommentMsgCodes.NOT_AUTH_SEND_COMMENT.getCode(), CommentMsgCodes.NOT_AUTH_SEND_COMMENT.getMsg());
        }
        return ResultVO.success();
    }

    @Override
    public ResultVO<Long> postprocessor(SendImageCommentPostBean data) {
        if (data.getRCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.success(data.getCommentId());
        }

        CommentMsgCodes commentMsgCodes = convertErrorCode(data.getRCode());
        return ResultVO.failure(commentMsgCodes.getCode(),commentMsgCodes.getMsg());
    }


    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.XM;
    }

    /**
     * 转换错误码
     *
     * @param rCode 错误码
     * @return 错误码
     */
    private CommentMsgCodes convertErrorCode(int rCode) {
        switch (rCode) {
            case TransientCommentService.ADD_COMMENT_LIVE_NOT_FOUND:
                return CommentMsgCodes.LIVE_NOT_EXIST;
            case TransientCommentService.ADD_COMMENT_BANNED:
                return CommentMsgCodes.SEND_COMMENT_USER_BAN;
            case TransientCommentService.ADD_COMMENT_LIVE_BAN_OPEN_MODE:
                return CommentMsgCodes.SEND_IMAGE_COMMENT_LIVE_BAN_MODEL;
            case TransientCommentService.ADD_COMMENT_IS_BAN:
                return CommentMsgCodes.SEND_COMMENT_REQ_TOO_FAST;
            case TransientCommentService.ADD_COMMENT_IS_BAN_CONTENT:
                return CommentMsgCodes.SEND_COMMENT_REVIEW_NO_PASS;
            default:
                return CommentMsgCodes.SEND_COMMENT_ERROR;
        }
    }
}
