package fm.lizhi.ocean.wave.live.core.model.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class QueryRoleManagerListParam {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long liveId;

    private Integer pageNum = 1;

    private Integer pageSize = 99;
}
