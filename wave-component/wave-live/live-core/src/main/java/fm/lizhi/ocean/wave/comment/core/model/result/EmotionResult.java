package fm.lizhi.ocean.wave.comment.core.model.result;

import fm.lizhi.ocean.wave.comment.core.model.vo.EmotionVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.GroupEmotionVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class EmotionResult {
    /**
     * 表情包信息列表
     */
    private List<EmotionVO> emotionList;

    /**
     * 表情包分组形式
     * 黑叶和PP的表情包有分组
     */
    private List<GroupEmotionVO> groupEmotionList;

    /**
     * 表情包版本号
     */
    private Integer version;
    /**
     * 是否要更新表情包
     */
    private Boolean isRefresh;
}
