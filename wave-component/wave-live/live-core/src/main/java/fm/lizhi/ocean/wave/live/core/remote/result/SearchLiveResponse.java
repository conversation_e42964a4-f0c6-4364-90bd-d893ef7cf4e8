package fm.lizhi.ocean.wave.live.core.remote.result;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class SearchLiveResponse {
    /**
     * 直播列表
     */
    private List<SearchLiveVo> liveList;

    /**
     * 总数
     */
    private long total;
    /**
     * 是否最后一页
     */
    private boolean isLastPage;

    /**
     * 分页标识，每个业务的结构不一样，直接透传就好了
     */
    private String performanceId;

    /**
     * 直播间
     */
    @Builder
    @Data
    public static class SearchLiveVo {
        /**
         * 直播ID
         */
        private long liveId;
        /**
         * 波段号
         */
        private String band;
        /**
         * 直播标题
         */
        private String title;
        /**
         * 直播封面
         */
        private String image;
        /**
         * 累计人气
         */
        private long totalPopularity;
        /**
         * 房间密码
         */
        private String password;
    }
}
