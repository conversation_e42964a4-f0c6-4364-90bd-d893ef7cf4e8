package fm.lizhi.ocean.wave.user.core.extension.visitor;

import fm.lizhi.ocean.wave.common.extension.BusinessEnvAwareProcessor;

/**
 * <AUTHOR>
 */
public interface QueryRecentVisitorProcess extends BusinessEnvAwareProcessor {
    /**
     * 判断用户是否有权限查看最近访客
     *
     * @param userId 用户id
     * @return true 有权限 false 无权限
     */
    public boolean checkHasPermission(long userId);

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor(){
        return QueryRecentVisitorProcess.class;
    }
}
