package fm.lizhi.ocean.wave.singer.core.config;

import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerConfigService;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerPermissionService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import fm.lizhi.common.dubbo.DubboClientBuilder;
import fm.lizhi.ocean.wave.common.auto.route.common.annotation.ScanBusinessProviderAPI;
import fm.lizhi.ocean.wavecenter.api.anchor.singer.service.SingerVerifyService;

@Configuration
@ScanBusinessProviderAPI(values = {
       
})
public class SingerApolloProvider {
    
    @Bean
    @ConditionalOnMissingBean(value = SingerVerifyService.class)
    public SingerVerifyService singerVerifyService() {
        return new DubboClientBuilder<>(SingerVerifyService.class)
                .connections(200)
                .timeoutInMillis(5000)
                .retries(0)
                .sync()
                .build();
    }

    @Bean
    @ConditionalOnMissingBean(value = SingerConfigService.class)
    public SingerConfigService singerConfigService() {
        return new DubboClientBuilder<>(SingerConfigService.class)
                .connections(200)
                .timeoutInMillis(5000)
                .retries(0)
                .sync()
                .build();
    }

    @Bean
    @ConditionalOnMissingBean(value = SingerPermissionService.class)
    public SingerPermissionService singerPermissionService() {
        return new DubboClientBuilder<>(SingerPermissionService.class)
                .connections(200)
                .timeoutInMillis(5000)
                .retries(0)
                .sync()
                .build();
    }
}
