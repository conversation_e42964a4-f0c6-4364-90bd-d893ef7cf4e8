package fm.lizhi.ocean.wave.user.core.manager;

import fm.lizhi.ocean.wave.server.common.push.manager.RomePushManager;
import fm.lizhi.ocean.wave.server.common.push.vo.PushVO;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.common.util.RomaTopicUtils;
import fm.lizhi.ocean.wave.user.core.constant.PushConstants;
import fm.lizhi.ocean.wave.user.core.model.vo.PushKickOutVo;
import fm.lizhi.ocean.wave.user.core.model.vo.PushUserOnlineStatusVo;
import fm.lizhi.ocean.wave.user.core.model.vo.UserFollowPushVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/6/6
 */
@Component
public class UserPushManager {

    @Autowired
    private RomePushManager pushManager;

    /**
     * 用户关注消息推送
     * @param doUserId
     * @param beUserId
     */
    public void pushUserFollow(Long doUserId, Long beUserId){
        PushVO<UserFollowPushVo> pushVo = new PushVO<>();
        pushVo.setBiz("im-follow-user");
        pushVo.setData(new UserFollowPushVo().setDoUserId(String.valueOf(doUserId)).setBeUserId(String.valueOf(beUserId)));
        String topic = RomaTopicUtils.getTopic(PushConstants.PUSH_USER_PREFIX, ContextUtils.getBusinessEvnEnum().getAppId(), beUserId);
        pushManager.pushWithCompLowTimeliness(topic, pushVo);
    }

    /**
     * 推送前端踢出消息
     *
     * @param token
     * @param type  1: 互踢
     * @param appId 应用ID
     */
    public void pushKickOut(String token, int type, int appId) {
        PushVO<PushKickOutVo> pushVo = new PushVO<>();
        pushVo.setBiz("account-kick-out");
        pushVo.setData(PushKickOutVo.builder().type(type).build());
        String topic = RomaTopicUtils.getTopic(PushConstants.PUSH_TOKEN, appId, token);
        pushManager.pushWithCompLowTimeliness(topic, pushVo);
    }

    /**
     * 推送前端-用户在线状态
     *
     * @param userId   用户ID
     * @param isOnline 是否在线
     * @param appId    应用ID
     */
    public void pushUserOnlineStatus(long userId, boolean isOnline, int appId) {
        PushVO<PushUserOnlineStatusVo> pushVo = new PushVO<>();
        pushVo.setBiz("user-status-sync");
        pushVo.setData(PushUserOnlineStatusVo.builder().userId(String.valueOf(userId)).isOnline(isOnline).build());
        String topic = RomaTopicUtils.getTopic(PushConstants.PUSH_USER_ONLINE_STATUS, appId);
        pushManager.pushWithCompHighTimeliness(topic, pushVo);
    }


}
