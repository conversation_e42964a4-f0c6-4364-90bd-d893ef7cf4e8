package fm.lizhi.ocean.wave.user.core.remote.result;

import fm.lizhi.ocean.wave.user.constant.OnlineStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/5/17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetUserBehaviorResult {
    private List<OnlineStatusEnum> onlineStatus;
}
