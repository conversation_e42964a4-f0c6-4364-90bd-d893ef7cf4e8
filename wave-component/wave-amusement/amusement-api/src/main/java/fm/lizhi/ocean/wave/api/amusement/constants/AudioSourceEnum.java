package fm.lizhi.ocean.wave.api.amusement.constants;

/**
 * 音频流推送端枚举
 */
public enum AudioSourceEnum {

    NONE(0),
    APP(1),
    PC(2);

    private final int value;

    AudioSourceEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static AudioSourceEnum getAudioClient(int value){
        for(AudioSourceEnum audioClient : AudioSourceEnum.values()){
            if(audioClient.getValue() == value){
                return audioClient;
            }
        }
        return NONE;
    }

}
