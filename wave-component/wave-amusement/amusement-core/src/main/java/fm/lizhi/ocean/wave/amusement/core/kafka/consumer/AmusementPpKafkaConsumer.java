package fm.lizhi.ocean.wave.amusement.core.kafka.consumer;

import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.annotation.SubscriptionOn;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.live.amusement.hy.dto.AmusementPushEvent;
import fm.lizhi.live.amusement.hy.dto.WaitingUserEvent;
import fm.lizhi.ocean.wave.amusement.core.constant.VoiceCallBizTypeEnum;
import fm.lizhi.ocean.wave.amusement.core.constant.VoiceCallMatchModelEnum;
import fm.lizhi.ocean.wave.amusement.core.dao.redis.AmusementRedisDao;
import fm.lizhi.ocean.wave.amusement.core.manager.AmusementPushManager;
import fm.lizhi.ocean.wave.amusement.core.model.vo.PushCallMatchResultEventVo;
import fm.lizhi.ocean.wave.amusement.core.model.vo.PushInviteOnSeatEventVo;
import fm.lizhi.ocean.wave.amusement.core.model.vo.VoiceCallUserEventVo;
import fm.lizhi.ocean.wave.common.disaster.annotation.KafkaBusinessBind;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.common.util.KafkaMsgUtils;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/19
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "pp-kafka250-bootstrap-server")
@SubscriptionOn(enable = "${pp.kafka.consumer.enable}")
public class AmusementPpKafkaConsumer {

    @Autowired
    private AmusementPushManager amusementPushManager;

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Autowired
    private AmusementRedisDao amusementRedisDao;

    @KafkaHandler(topic = "lz_topic_pp_amusement_push_msg",
            group = "lz_ocean_wave_amusement_push_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAmusementSync(String body) {
        String msg = null;

        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            AmusementPushEvent event = JsonUtil.loads(msg, AmusementPushEvent.class);

            // 删除直播间麦位用户映射
            try {
                amusementRedisDao.deleteMicUsersByLive(event.getLiveId());
            } catch (Exception e) {
                log.error("删除直播间麦位用户映射失败, liveId:{}, error:{}", event.getLiveId(), e.getMessage(), e);
            }

            amusementPushManager.pushAmusementSync(event.getLiveId(), BusinessEvnEnum.PP.getAppId());
        } catch (Exception e) {
            log.error("AmusementPpKafkaConsumer handleAmusementData process error, msg:{}, orgMsg:{}", msg, body, e);
        }

    }

    @KafkaHandler(topic = "lz_topic_pp_waiting_user_msg",
            group = "lz_ocean_wave_amusement_push_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAmusementApply(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            WaitingUserEvent event = JsonUtil.loads(msg, WaitingUserEvent.class);
            amusementPushManager.pushAmusementApplySync(event.getLiveId(), BusinessEvnEnum.PP.getAppId());
        } catch (Exception e) {
            log.error("AmusementPpKafkaConsumer handleAmusementApply process error,  msg:{}, orgMsg:{}", msg, body, e);
        }
    }

    @KafkaHandler(topic = "pp_topic_call_status_event",
            group = "lz_ocean_wave_call_status_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleCallStatusBeginEndEvent(String body) {
        try {
            String msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("pp.handleCallStatusBeginEndEvent msg:{}", msg);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
            amusementPushManager.pushCallStatusChangeEventSync(msg, BusinessEvnEnum.PP.getAppId());
        } catch (Exception e) {
            log.error("AmusementPpKafkaConsumer handleCallStatusBeginEndEvent process error,  msg:{}", body, e);
        }
    }

    /**
     * 语音通话匹配结果
     *
     * @param body 消息体
     */
    @KafkaHandler(topic = "pp_topic_voice_call_match_result",
            group = "lz_ocean_wave_amusement_push_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void voiceCallMatchResult(String body) {
        try {
            String msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("pp.voiceCallMatchResult msg:{}", msg);
            PushCallMatchResultEventVo event = JsonUtil.loads(msg, PushCallMatchResultEventVo.class);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
            fillData(event);
            amusementPushManager.pushVoiceCallMatchResult(event, BusinessEvnEnum.PP.getAppId());
        } catch (Exception e) {
            log.error("AmusementPpKafkaConsumer.voiceCallMatchResult json parse error,  body:{}", body, e);
        }
    }

    /**
     * 邀请上麦事件
     * @param body 消息体
     */
    @KafkaHandler(topic = "pp_topic_invite_on_seat_event",
            group = "lz_ocean_wave_amusement_push_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleInviteOnSeatEvent(String body) {
        try {
            String msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("pp.inviteOnSeatEvent msg:{}", msg);
            PushInviteOnSeatEventVo event = JsonUtil.loads(msg, PushInviteOnSeatEventVo.class);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
            amusementPushManager.pushInviteOnSeatEvent(event, BusinessEvnEnum.PP.getAppId());
        } catch (Exception e) {
            log.error("AmusementPpKafkaConsumer.voiceCallMatchResult json parse error,  body:{}", body, e);
        }
    }

    /**
     * 填充数据
     *
     * @param event 事件消息
     */
    private void fillData(PushCallMatchResultEventVo event) {
        event.setCallBizType(VoiceCallBizTypeEnum.bizValue2Wave(event.getCallBizType()).getWaveType());
        event.setMode(VoiceCallMatchModelEnum.bizValue2Wave(event.getMode()).getModel());
        VoiceCallUserEventVo targetUser = event.getTargetUser();
        if (targetUser != null) {
            String cdnHost = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.PP.getAppId()).getCdnHost();
            targetUser.setPortrait(UrlUtils.addCdnHost(cdnHost, targetUser.getPortrait()));
            event.setTargetUser(targetUser);
        }
    }

}
