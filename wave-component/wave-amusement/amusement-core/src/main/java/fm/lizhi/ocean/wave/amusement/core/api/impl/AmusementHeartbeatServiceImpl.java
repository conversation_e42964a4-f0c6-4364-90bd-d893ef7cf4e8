package fm.lizhi.ocean.wave.amusement.core.api.impl;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.amusement.core.dao.redis.AmusementRedisDao;
import fm.lizhi.ocean.wave.amusement.core.manager.AmusementInfoManager;
import fm.lizhi.ocean.wave.api.amusement.api.AmusementHeartbeatService;
import fm.lizhi.ocean.wave.api.amusement.api.AmusementService;
import fm.lizhi.ocean.wave.api.amusement.constants.UserHeartBeatStatusEnum;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class AmusementHeartbeatServiceImpl implements AmusementHeartbeatService {

    @Autowired
    private AmusementInfoManager amusementInfoManager;

    @Autowired
    private AmusementRedisDao amusementRedisDao;

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Autowired
    private AmusementService amusementService;



    @Override
    public Result<Void> asyncReportOnMicData(Long liveId, long userId, List<String> userStatus) {

        if (null == liveId || liveId <= 0) {
            log.warn("asyncReportOnMicData, liveId is null, userId:{}", userId);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        // 旧版本，不在这里上报，会维持在之前的轮询逻辑里上报
        if (isOldVersion()) {
            if (log.isDebugEnabled()){
                log.debug("asyncReportOnMicData old version, ignore. liveId:{}, userId:{}", liveId, userId);
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        if (CollUtil.isEmpty(userStatus)) {
            log.warn("asyncReportOnMicData, userStatus is null, liveId:{}, userId:{}", liveId, userId);
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
        }

        // 验证用户是否在麦位上, 前端上报状态+Redis 缓存交叉验证
        boolean isOnMicByClient = userStatus.stream().map(UserHeartBeatStatusEnum::valueOf)
                .anyMatch(status -> status == UserHeartBeatStatusEnum.onMic);
        boolean isOnMicByServer = amusementRedisDao.checkUserInMicByLive(liveId, userId);

        // 是否可上报, 默认是双端都正常
        boolean canReport = isOnMicByClient && isOnMicByServer;

        if (isOnMicByClient != isOnMicByServer) {
            // 两端对不上，查询接口检查一下， 以接口为准
            canReport = checkUserInMic(liveId, userId);
        }

        if (canReport) {
            amusementInfoManager.asyncReportOnMicData(liveId, userId);
        }

        log.info("asyncReportOnMicData, liveId:{}, userId:{}, isOnMicByClient:{}, isOnMicByServer:{}, canReport:{}", liveId, userId, isOnMicByClient, isOnMicByServer, canReport);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }


    private boolean checkUserInMic(long liveId, long userId) {
        Result<Integer> result = amusementService.userLiveSeat(liveId, userId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("checkUserInMicByServer fail, liveId:{}, userId:{}", liveId, userId);
            return false;
        }
        return result.target() != -1;
    }



    /**
     * 是否是旧版本
     */
    public boolean isOldVersion() {
        try {
            int clientVersion = Integer.parseInt(ContextUtils.getContext().getHeader().getClientVersion());
            return clientVersion < commonProviderConfig.getMinHeartbeatClientVersion();
        } catch (Exception e) {
            log.error("isOldVersion error: ", e);
            return true;
        }
    }
}
