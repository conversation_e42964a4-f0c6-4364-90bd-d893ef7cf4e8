<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<appender name="transportAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_PATH}/transport.log</file>
		<append>true</append>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/transport.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
		</rollingPolicy>
	</appender>
	<appender name="asyncTransportAppender" class="ch.qos.logback.classic.AsyncAppender">
		<queueSize>10000</queueSize>
		<appender-ref ref="transportAppender"/>
	</appender>
	<appender name="infoAppender"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_PATH}/info.log</file>
		<append>true</append>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}`%m%n</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/info.%d{yyyy-MM-dd_HH}.log
			</fileNamePattern>
		</rollingPolicy>
	</appender>
	
	<appender name="asyncInfoAppender" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="infoAppender" />
		<queueSize>10000</queueSize>
	</appender>
	
	<appender name="errorAppender"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_PATH}/error.log</file>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %X{TRACE_ID} %logger{36} - %msg%n
			</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/error.%d{yyyy-MM-dd_HH}.log
			</fileNamePattern>
		</rollingPolicy>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>WARN</level>
		</filter>
	</appender>
	
	<appender name="asyncErrorAppender"
		class="ch.qos.logback.classic.AsyncAppender">
		<queueSize>4096</queueSize>
		<appender-ref ref="errorAppender" />
	</appender>
	
	<appender name="accessAppender"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_PATH}/access.log</file>
		<append>true</append>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
			</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/access.%d{yyyy-MM-dd_HH}.log
			</fileNamePattern>
		</rollingPolicy>
	</appender>
	
	<appender name="asyncAccessAppender" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="accessAppender" />
		<queueSize>10000</queueSize>
	</appender>
	
	<appender name="serverAppender"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_PATH}/server.log</file>
		<append>true</append>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %X{TRACE_ID} %logger{36} - %msg%n
			</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_PATH}/server.%d{yyyy-MM-dd_HH}.log
			</fileNamePattern>
		</rollingPolicy>
	</appender>
	
	<appender name="asyncServerAppender" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="serverAppender" />
		<queueSize>10000</queueSize>
	</appender>
	
	<logger name="access_log" level="info" additivity="false">
		<appender-ref ref="asyncAccessAppender" />
	</logger>
	
	<logger name="info" level="info" additivity="false">
		<appender-ref ref="asyncInfoAppender" />
	</logger>
	<logger name="fm.lizhi.commons.connector.async.transport" level="error" additivity="false">
		<appender-ref ref="asyncTransportAppender"/>
	</logger>

	<logger name="fm.lizhi.commons.connector.async.transport.handler" level="error" additivity="false">
		<appender-ref ref="asyncTransportAppender"/>
	</logger>
	<appender name="consoleAppender" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
			</pattern>
		</encoder>
	</appender>

	<appender name="catAppender" class="com.dianping.cat.logback.CatLogbackAppender"></appender>
	
	<root level="info">
		<!-- <appender-ref ref="consoleAppender" /> -->
		<appender-ref ref="asyncServerAppender" />
		<appender-ref ref="asyncErrorAppender" />
		<appender-ref ref="catAppender" />
	</root>
	
</configuration>
