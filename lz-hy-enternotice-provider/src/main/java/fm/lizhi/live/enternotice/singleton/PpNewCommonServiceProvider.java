package fm.lizhi.live.enternotice.singleton;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.proxy.ProxyBuilder;
import fm.lizhi.hy.user.account.common.api.HyNewCommonService;

import javax.inject.Provider;

@AutoBindSingleton
public class PpNewCommonServiceProvider implements Provider<HyNewCommonService> {

    @Inject
    private ProxyBuilder proxyBuilder;

    @Override
    public HyNewCommonService get() {
        return proxyBuilder.buildProxy(HyNewCommonService.class);
    }
}
