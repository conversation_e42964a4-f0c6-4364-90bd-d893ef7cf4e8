package fm.lizhi.live.enternotice.manager;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Optional;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.hy.amusement.protocol.DressUpInfoProto;
import fm.lizhi.hy.user.account.common.protocol.HyCommonProto;
import fm.lizhi.hy.user.account.user.protocol.HyUserBaseProto;
import fm.lizhi.live.enternotice.config.LiveEnterNoticeConfig;
import fm.lizhi.live.enternotice.meta.contants.EnterNoticeConst;
import fm.lizhi.live.enternotice.meta.vo.*;
import fm.lizhi.pp.util.romeupload.RomeUploadImageUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by liuxiaoxiong on 2018/9/5.
 */
@AutoBindSingleton
public class EnterNoticeVoGenerator {

    private static Logger logger = LoggerFactory.getLogger(EnterNoticeVoGenerator.class);

    @Inject
    private LiveEnterNoticeConfig liveEnterNoticeConfig;
    @Inject
    private EnterNoticeRedisManager enterNoticeRedisManager;
    @Inject
    private UserManager userManager;
    @Inject
    private LiveManager liveManager;
    @Inject
    private FreshUserManager freshUserManager;
    @Inject
    private PpUserRelationManager userRelationManager;
    @Inject
    private DressUpManager dressUpManager;

    public EnterNoticeVo generateEnterNoticeByType(EnterVo enterVo, int type) {
        if(type== EnterNoticeConst.EnterNoticeType.NORMAL
                ||type==EnterNoticeConst.EnterNoticeType.AUTO){
            return this.generateEnterNotice(enterVo,type);
        }else if(type==EnterNoticeConst.EnterNoticeType.VEHICLE){
            return this.generateVehicleEnterNotice(enterVo,type);
        }else if(type==EnterNoticeConst.EnterNoticeType.NEW_FANS){
            return this.generateNewFansEnterNotice(enterVo,type);
        }
        return null;
    }


    /**
     * 生成进房公告Vo对象
     *
     * @param enterVo
     * @return
     */
    private EnterNoticeVo generateVehicleEnterNotice(EnterVo enterVo, int type) {
        EnterNoticeVo enterNoticeVo = new EnterNoticeVo();
        enterNoticeVo.setId(enterVo.getId());
        enterNoticeVo.setEnterNoticeType(type);
        String userName = "";
        enterNoticeVo.setUserId(enterVo.getUserId());

        long liveId = enterVo.getLiveId();

        HyUserBaseProto.User ppUser = userManager.getPpUser(enterVo.getUserId());
        if (null == ppUser) {
            logger.info(" user not exist ! userId:{}", enterVo.getUserId());
            return null;
        }
        userName = ppUser.getName();

        enterNoticeVo.setUserName(userName);
        Long njId = liveManager.getNjId(liveId);
        if (-1L == njId) {
            logger.info(" can't find NJ ! userId:{}`liveId:{}`", enterVo.getUserId(), liveId);
            return null;
        }
        enterNoticeVo.setEnterTime(enterVo.getEnterTime());
        enterNoticeVo.setLiveId(liveId);
        boolean markFlag = true;
        //先放置进房时间标识
        markFlag = enterNoticeRedisManager.markNoticeEnable(enterVo.getUserId(), njId);
        if (!markFlag) {
            return null;
        }
        // 新用户
        setFreshUser(enterVo.getUserId(), enterNoticeVo);
        enterNoticeVo.setWealthInfoVo(enterVo.getWealthInfoVo());
        enterNoticeVo.setContent(enterVo.getContent());
        // 座驾进房文本单独字段 20210203 TanKun
        if (enterVo.getUserMountVo() != null) {
            enterVo.getUserMountVo().setText(enterVo.getContent());
        }
        enterNoticeVo.setUserMountVo(enterVo.getUserMountVo());
        enterNoticeVo.setUserCover(enterVo.getUserCover());
        enterNoticeVo.setVipInfoVo(enterVo.getVipInfoVo());

        // 关系进房特效+文本
        fillRelationEnterRoomEffect(enterNoticeVo);
        enterNoticeVo.setCloseFriendEnterRoomEffectJson(enterVo.getCloseFriendEnterRoomEffectJson());
        enterNoticeVo.setNobleEnterRoomEffectJson(enterVo.getNobleEnterRoomEffectJson());
        enterNoticeVo.setSlideMatchEnterRoomEffectJson(enterVo.getSlideMatchEnterRoomEffectJson());
        enterNoticeVo.setNameEffectJson(enterVo.getNameEffectJson());
        return enterNoticeVo;
    }

    /**
     * 生成进房公告Vo对象
     *
     * @param enterVo
     * @return
     */
    private EnterNoticeVo generateNewFansEnterNotice(EnterVo enterVo, int type) {
        EnterNoticeVo enterNoticeVo = new EnterNoticeVo();
        enterNoticeVo.setId(enterVo.getId());
        enterNoticeVo.setEnterNoticeType(type);
        String userName = "";
        long liveId = enterVo.getLiveId();
        enterNoticeVo.setUserId(enterVo.getUserId());

        HyUserBaseProto.User ppUser = userManager.getPpUser(enterVo.getUserId());
        if (null == ppUser) {
            logger.info(" user not exist ! userId:{}", enterVo.getUserId());
            return null;
        }
        userName = ppUser.getName();
        String userCover = RomeUploadImageUtil.portraitOriginUrl(ppUser.getPortrait());
        enterNoticeVo.setUserCover(userCover);
        enterNoticeVo.setUserName(userName);

        Long njId = liveManager.getNjId(liveId);
        if (-1L == njId) {
            logger.info(" can't find NJ ! userId:{}`liveId:{}`", enterVo.getUserId(), liveId);
            return null;
        }

        enterNoticeVo.setEnterTime(System.currentTimeMillis());
        enterNoticeVo.setLiveId(liveId);
        enterNoticeVo.setContent(userName+"关注了你");
        // 新用户
        setFreshUser(enterVo.getUserId(), enterNoticeVo);
        return enterNoticeVo;
    }
    /**
     * 生成进房公告Vo对象
     *
     * @param enterVo
     * @return
     */
    private EnterNoticeVo generateEnterNotice(EnterVo enterVo, int type) {
        EnterNoticeVo enterNoticeVo = new EnterNoticeVo();
        enterNoticeVo.setId(enterVo.getId());
        enterNoticeVo.setEnterNoticeType(type);
        String userName = "";
        boolean anonymous = true;
        long liveId = enterVo.getLiveId();
        if (0 == enterVo.getAutomaton()) {
            enterNoticeVo.setUserId(enterVo.getUserId());
            HyUserBaseProto.User ppUser = userManager.getPpUser(enterVo.getUserId());
            if (null == ppUser) {
                return null;
            }
            userName = ppUser.getName();
            anonymous = false;
        } else {
            userName = "匿名侠";
        }
        enterNoticeVo.setUserName(userName);
        Long njId = liveManager.getNjId(liveId);
        if (-1L == njId) {
            logger.info(" can't find NJ ! userId:{}`liveId:{}`", enterVo.getUserId(), liveId);
            return null;
        }
        if (enterVo.isExplicit()) {
            anonymous = false;
        }
        enterNoticeVo.setAnonymous(anonymous ? 1 : 0);
        enterNoticeVo.setEnterTime(enterVo.getEnterTime());
        enterNoticeVo.setLiveId(liveId);
        boolean markFlag = true;
        int runningDays = 0;
        if (0 == enterVo.getAutomaton()) {
            // 如果有滑卡匹配的特效消息，不拦截
            if(StringUtils.isEmpty(enterVo.getSlideMatchEnterRoomEffectJson())) {
                //先放置进房时间标识
                markFlag = enterNoticeRedisManager.markNoticeEnable(enterVo.getUserId(), njId);
                if (!markFlag) {
                    return null;
                }
            }
            // 新用户
            setFreshUser(enterVo.getUserId(), enterNoticeVo);
            //然后设置连续进房天数
            enterNoticeRedisManager.setRunningDays(enterVo.getUserId(), njId);
            runningDays = enterNoticeRedisManager.getRunningDays(enterVo.getUserId(), njId);
        }
        // PP贵族进房效果
        //这里setContent的逻辑重复了，优先使用参数传递进来的
        VipInfoVo vipInfoVo = enterVo.getVipInfoVo();
        if (anonymous) {
            enterNoticeVo.setContent(liveEnterNoticeConfig.getAnonymousNoticeTemplate());
        } else if (runningDays > 1) {
            enterNoticeVo.setContent(StringUtils.isNotBlank(enterVo.getContent()) ? enterVo.getContent() :
                    String.format(liveEnterNoticeConfig.getRunningDaysNoticeTemplate(), userName, runningDays));
            enterNoticeVo.setWealthInfoVo(enterVo.getWealthInfoVo());
            enterNoticeVo.setVipInfoVo(vipInfoVo);
        } else {
            enterNoticeVo.setContent(StringUtils.isNotBlank(enterVo.getContent()) ? enterVo.getContent() :
                    String.format(liveEnterNoticeConfig.commonNoticeWithMountTemplate, userName, mountDesc(enterVo.getUserId())));
            enterNoticeVo.setWealthInfoVo(enterVo.getWealthInfoVo());
            enterNoticeVo.setVipInfoVo(vipInfoVo);
        }


        // 关系进房特效+文本
        if (!anonymous) {
            fillRelationEnterRoomEffect(enterNoticeVo);
        }
        enterNoticeVo.setCloseFriendEnterRoomEffectJson(enterVo.getCloseFriendEnterRoomEffectJson());
        enterNoticeVo.setNobleEnterRoomEffectJson(enterVo.getNobleEnterRoomEffectJson());
        enterNoticeVo.setUserCover(enterVo.getUserCover());
        enterNoticeVo.setSlideMatchEnterRoomEffectJson(enterVo.getSlideMatchEnterRoomEffectJson());
        enterNoticeVo.setNameEffectJson(enterVo.getNameEffectJson());
        logger.info("generateEnterNotice,detail:{}", JSON.toJSONString(enterNoticeVo));
        return enterNoticeVo;
    }

    private void setFreshUser(long userId, EnterNoticeVo enterNoticeVo) {
        Optional<HyCommonProto.ResponseIsFreshUser> freshUserOptional = freshUserManager.getFreshUser(userId);
        if (freshUserOptional.isPresent()) {
            HyCommonProto.ResponseIsFreshUser freshUser = freshUserOptional.get();
            FreshUserVo freshUserVo = new FreshUserVo();
            freshUserVo.setIs(freshUser.getIs());
            if (freshUser.getIs() && StringUtils.isNotBlank(freshUser.getUrl())) {
                freshUserVo.setUrl(freshUser.getUrl());
                freshUserVo.setAspect((float) freshUser.getAspect());
            }
            enterNoticeVo.setFreshUserVo(freshUserVo);
        } else {
            FreshUserVo freshUserVo = new FreshUserVo();
            freshUserVo.setIs(false);
            enterNoticeVo.setFreshUserVo(freshUserVo);
        }
    }

    /**
     * 带着座驾的文案。
     */
    private String mountDesc(long userId) {
        DressUpInfoProto.DressUpInfo mount = dressUpManager.getUserDressUsing(userId, 1);
        if (mount == null) {
            return "";
        }
        return "带着" + mount.getDressUpName();
    }

    /**
     * 填充关系进房特效
     *
     * @param enterNoticeVo
     */
    private void fillRelationEnterRoomEffect(EnterNoticeVo enterNoticeVo) {
        long userId = enterNoticeVo.getUserId();
        long liveId = enterNoticeVo.getLiveId();
        if (userId > 0 && liveId > 0) {
            Pair<String, String> relationEffectPair = userRelationManager.getUserRelationEnterRoomEffect(userId, liveId);
            if (relationEffectPair != null) {
                String enterRoomText = relationEffectPair.getLeft();
                String enterRoomEffect = relationEffectPair.getRight();
                if (StringUtils.isNotBlank(enterRoomText)) {
                    enterNoticeVo.setContent(enterRoomText);
                }
                if (StringUtils.isNotBlank(enterRoomEffect)) {
                    enterNoticeVo.setRelationEffectJson(enterRoomEffect);
                }
            }
        }
    }
}
