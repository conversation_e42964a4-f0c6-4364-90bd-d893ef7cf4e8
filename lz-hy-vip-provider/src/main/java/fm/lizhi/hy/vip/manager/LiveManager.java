package fm.lizhi.hy.vip.manager;

import com.google.common.base.Optional;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.room.hy.api.LiveNewService;
import fm.lizhi.live.room.hy.api.LiveRoomRoleService;
import fm.lizhi.live.room.hy.protocol.LiveNewProto;
import fm.lizhi.live.room.hy.protocol.LiveRoomProto;
import fm.lizhi.live.room.hy.protocol.LiveRoomRoleProto;
import io.netty.util.concurrent.DefaultThreadFactory;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
@AutoBindSingleton
public class LiveManager {

    @Inject
    private LiveNewService liveNewService;
    @Inject
    private LiveRoomRoleService liveRoomRoleService;

    private ListeningExecutorService refreshPools = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(5, new DefaultThreadFactory("LiveManager")));

    private LoadingCache<Long, Optional<LiveNewProto.Live>> liveCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .refreshAfterWrite(30, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, Optional<LiveNewProto.Live>>() {
                @Override
                public Optional<LiveNewProto.Live> load(Long key) throws Exception {
                    return doGetLive(key);
                }
            });

    private LoadingCache<Long, Optional<LiveNewProto.Live>> liveCacheByUserId = CacheBuilder.newBuilder()
            .maximumSize(50)
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, Optional<LiveNewProto.Live>>() {
                @Override
                public Optional<LiveNewProto.Live> load(Long key) throws Exception {
                    return getLiveByUserId(key);
                }
            });

    private LoadingCache<Long, List<Long>> roomAdminCache = CacheBuilder.newBuilder()
            .recordStats()
            .refreshAfterWrite(5, TimeUnit.SECONDS)
            .expireAfterWrite(2, TimeUnit.MINUTES)
            .maximumSize(3000)
            .build(new CacheLoader<Long, List<Long>>() {
                @Override
                public List<Long> load(@NonNull Long liveRoomId) throws Exception {
                    return getRoomManagerUserList(liveRoomId);
                }

                @Override
                public ListenableFuture<List<Long>> reload(final Long key, List<Long> oldValue) throws Exception {
                    return refreshPools.submit(new Callable<List<Long>>() {
                        @Override
                        public List<Long> call() throws Exception {
                            return getRoomManagerUserList(key);
                        }
                    });
                }
            });



    private Optional<LiveNewProto.Live> doGetLive(Long liveId) {
        LiveNewProto.GetLiveParams.Builder builder = LiveNewProto.GetLiveParams.newBuilder();
        builder.setLiveId(liveId);
        Result<LiveNewProto.ResponseGetLive> liveResult = liveNewService.getLive(builder.build());
        if (GeneralRCode.GENERAL_RCODE_SUCCESS != liveResult.rCode()) {
            log.error("[loadLive] - liveNewService.getLive - rCode:{}, liveId: {}", liveResult.rCode(), liveId);
            return Optional.absent();
        }
        LiveNewProto.Live live = liveResult.target().getLive();
        return Optional.of(live);

    }

    public LiveNewProto.Live getLive(long liveId) {
        Optional<LiveNewProto.Live> optional = liveCache.getUnchecked(liveId);
        return optional.isPresent() ? optional.get() : null;
    }

    /**
     * 获取房间管理员用户列表缓存
     *
     * @param liveRoomId 房间ID
     * @return
     */
    private List<Long> getRoomManagerUserList(Long liveRoomId) {
        List<Long> roomManagerUserList = Lists.newArrayList();
        Result<LiveRoomRoleProto.ResponseGetLiveRoomRoleByStatus> result = liveRoomRoleService.getLiveRoomRoleByStatus(liveRoomId, 2, 0, 0, -1);
        if (null != result && result.rCode() == 0) {
            List<LiveRoomRoleProto.LiveRoomRole> userList = result.target().getLiveRoomRoleList();
            for (LiveRoomRoleProto.LiveRoomRole role : userList) {
                roomManagerUserList.add(role.getRoleUserId());
            }
        }
        return roomManagerUserList;
    }

    public List<Long> getRoomAdminList(long liveRoomId){
        return roomAdminCache.getUnchecked(liveRoomId);
    }


    public Optional<LiveNewProto.Live> getLiveByUserId(long userId) {
        LiveNewProto.GetLivesByUserIdsParams.Builder builder = LiveNewProto.GetLivesByUserIdsParams.newBuilder();
        builder.addUserId(userId);
        Result<LiveNewProto.ResponseGetLivesByUserIds> result = liveNewService.getLivesByUserIds(builder.build());
        if (null != result && result.rCode() == 0) {
            LiveNewProto.ResponseGetLivesByUserIds target = result.target();
            if (target.getLiveCount() > 0) {
                return Optional.fromNullable(target.getLive(0));
            }
        }
        return Optional.absent();
    }

    public LiveNewProto.Live getLiveCacheByUserId(long userId) {
        Optional<LiveNewProto.Live> liveOptional = liveCacheByUserId.getUnchecked(userId);
        return liveOptional.isPresent() ? liveOptional.get() : null;
    }

}