package fm.lizhi.hy.vip.constant.amusement;

import java.util.HashMap;
import java.util.Map;

/**
 * 歌曲操作
 */
public enum LiveSingStatusEnum {

    WAITING(1, "等待中"),
    STARTING(2, "开始中"),
    END(3, "结束"),
    DELETE(4, "删除");

    private static Map<Integer, LiveSingStatusEnum> map = new HashMap<>();

    static {
        for (LiveSingStatusEnum object : LiveSingStatusEnum.values()) {
            map.put(object.getValue(), object);
        }
    }

    /**
     * 值
     */
    private int value;
    /**
     * 描述
     */
    private String msg;

    LiveSingStatusEnum(int value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    /**
     * 根据值类型找枚举
     *
     * @param value 值
     * @return
     */
    public static LiveSingStatusEnum from(int value) {
        return map.get(value);
    }

    public int getValue() {
        return value;
    }

    public String getMsg() {
        return msg;
    }

}
