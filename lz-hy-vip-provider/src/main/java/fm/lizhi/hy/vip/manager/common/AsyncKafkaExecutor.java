package fm.lizhi.hy.vip.manager.common;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.hy.vip.manager.common.info.GiftPackMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.inject.Inject;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@AutoBindSingleton
public class AsyncKafkaExecutor {
    private static Logger logger = LoggerFactory.getLogger(AsyncKafkaExecutor.class);

    private ExecutorService executorService;
    @Inject
    private UserBuyGiftInfoManager userBuyGiftInfoManager;

    @PostConstruct
    public void init() {
        executorService = Executors.newFixedThreadPool(30, new ThreadFactory() {
            private AtomicInteger ids = new AtomicInteger(0);

            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r);
                thread.setDaemon(true);
                thread.setName("pp-kafka-task-executor-" + ids.getAndIncrement());
                return thread;
            }
        });
    }

    @PreDestroy
    public void destroy() {
        if (executorService != null) {
            logger.info("start pp-kafka-task-executor");
            executorService.shutdown();
            logger.info("end pp-kafka-task-executor");
        }
    }

    /**
     * 用户购买礼包行为
     *
     * @param giftPackMsg 购买行为
     */
    public void buyGiftPackEvent(final GiftPackMsg giftPackMsg) {
        executorService.submit(RunnableWrapper.of(new Runnable() {
            @Override
            public void run() {
                try {
                    userBuyGiftInfoManager.saveBuyGift(giftPackMsg);
                } catch (Exception e) {
                    log.error("Random Game addCharmEvent is error...,giftPackMsg={},e={}", giftPackMsg, e);
                    e.printStackTrace();
                }
            }
        }));
    }
}
