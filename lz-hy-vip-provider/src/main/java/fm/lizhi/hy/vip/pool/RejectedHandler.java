package fm.lizhi.hy.vip.pool;


import fm.lizhi.hy.vip.log.LizhiLogger;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

public class RejectedHandler implements RejectedExecutionHandler{
	 @Override
	    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
	        LizhiLogger.error("discard task, runnable:{}, executor:{}", r, executor);
	    }
}
