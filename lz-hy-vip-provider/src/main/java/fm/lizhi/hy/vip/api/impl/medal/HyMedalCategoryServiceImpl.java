package fm.lizhi.hy.vip.api.impl.medal;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.hy.vip.api.HyMedalCategoryV2Service;
import fm.lizhi.hy.vip.entity.MedalCategoryV2;
import fm.lizhi.hy.vip.mapper.MedalCategoryV2Mapper;
import fm.lizhi.hy.vip.protocol.HyMedalBaseV2Proto;
import fm.lizhi.hy.vip.protocol.HyMedalCategoryV2Proto;
import fm.lizhi.hy.vip.util.ValueUtil;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@ServiceProvider
public class HyMedalCategoryServiceImpl implements HyMedalCategoryV2Service {

    @Inject
    private GuidGenerator guidGenerator;

    @Inject
    private MedalCategoryV2Mapper medalCategoryV2Mapper;

    @Override
    public Result<HyMedalCategoryV2Proto.ResponseMedalCategoryList> medalCategoryList(HyMedalBaseV2Proto.QueryParams params) {

        HyMedalCategoryV2Proto.ResponseMedalCategoryList.Builder builder = HyMedalCategoryV2Proto.ResponseMedalCategoryList.newBuilder();

        int pageNo = params.getPageNo();
        int pageSize = params.getPageSize();

        try {
            PageList<MedalCategoryV2> list = medalCategoryV2Mapper.pageList(
                    params.hasMedalCategoryId() ? params.getMedalCategoryId() : 0,
                    params.hasMedalCategoryName() ? params.getMedalCategoryName() : null
                    , pageNo, pageSize);

            if (ValueUtil.isNotEmpty(list)) {
                List<HyMedalBaseV2Proto.MedalCategoryV2> categorys = new ArrayList<>();
                for (MedalCategoryV2 cate : list) {
                    HyMedalBaseV2Proto.MedalCategoryV2.Builder cateBuild = HyMedalBaseV2Proto.MedalCategoryV2.newBuilder();
                    cateBuild.setMedalCategoryName(cate.getMedalCategoryName());
                    cateBuild.setCreateTime(cate.getCreateTime().getTime());
                    cateBuild.setId(cate.getId());
                    cateBuild.setIsShow(cate.getIsShow());
                    cateBuild.setSort(cate.getSort());
                    cateBuild.setStatus(cate.getStatus());
                    cateBuild.setModifyTime(cate.getModifyTime().getTime());
                    categorys.add(cateBuild.build());
                }
                builder.addAllMedalCategorys(categorys);
                builder.setTotalCount(list.getTotal());
            }
        } catch (Exception e) {
            log.error("HyMedalCategoryServiceImpl medalCategoryList error",e);
        }

        return new Result<>(0, builder.build());
    }

    @Override
    public Result<HyMedalCategoryV2Proto.ResponseMedalCategoryAdd> medalCategoryAdd(HyMedalBaseV2Proto.MedalCategoryV2 medalCategory) {
        HyMedalCategoryV2Proto.ResponseMedalCategoryAdd.Builder builder = HyMedalCategoryV2Proto.ResponseMedalCategoryAdd.newBuilder();
        int isShow = medalCategory.getIsShow();
        String medalCategoryName = medalCategory.getMedalCategoryName();
        int sort = medalCategory.getSort();
        int status = medalCategory.getStatus();

        try {
            MedalCategoryV2 categoryV2 = new MedalCategoryV2();
            categoryV2.setId(guidGenerator.genId());
            categoryV2.setIsShow(isShow);
            categoryV2.setSort(sort);
            categoryV2.setStatus(status);
            categoryV2.setMedalCategoryName(medalCategoryName);
            Date date = new Date();
            categoryV2.setCreateTime(date);
            categoryV2.setModifyTime(date);

            medalCategoryV2Mapper.insert(categoryV2);
            return new Result<>(0, builder.build());

        } catch (Exception e) {
            log.error("HyMedalCategoryServiceImpl medalCategoryAdd error medalCategoryName={},isShow={},sort={},status={}",
                    medalCategoryName,isShow,sort,status,e);
            return new Result<>(HyMedalCategoryV2Service.MEDAL_CATEGORY_ADD_FAIL, builder.build());
        }
    }

    @Override
    public Result<HyMedalCategoryV2Proto.ResponseMedalCategoryEdit> medalCategoryEdit(HyMedalBaseV2Proto.MedalCategoryV2 medalCategory) {
        HyMedalCategoryV2Proto.ResponseMedalCategoryEdit.Builder builder = HyMedalCategoryV2Proto.ResponseMedalCategoryEdit.newBuilder();

        long categoryId = medalCategory.getId();
        int isShow = medalCategory.getIsShow();
        String medalCategoryName = medalCategory.getMedalCategoryName();
        int sort = medalCategory.getSort();
        int status = medalCategory.getStatus();

        try {
            MedalCategoryV2 categoryV2 = new MedalCategoryV2();
            categoryV2.setId(categoryId);
            categoryV2.setIsShow(isShow);
            categoryV2.setSort(sort);
            categoryV2.setStatus(status);
            categoryV2.setMedalCategoryName(medalCategoryName);
            Date date = new Date();
            categoryV2.setModifyTime(date);

            medalCategoryV2Mapper.updateByPrimaryKey(categoryV2);
            return new Result<>(0, builder.build());

        } catch (Exception e) {
            log.error("HyMedalCategoryServiceImpl medalCategoryEdit error categoryId={},medalCategoryName={},isShow={},sort={},status={}",
                    categoryId,medalCategoryName,isShow,sort,status,e);
            return new Result<>(HyMedalCategoryV2Service.MEDAL_CATEGORY_ADD_FAIL, builder.build());
        }
    }
}
