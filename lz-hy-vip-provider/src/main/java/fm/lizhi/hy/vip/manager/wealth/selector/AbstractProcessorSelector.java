package fm.lizhi.hy.vip.manager.wealth.selector;

import com.google.common.base.Optional;
import fm.lizhi.hy.vip.config.VipConfig;
import fm.lizhi.hy.vip.manager.wealth.processor.Processor;
import fm.lizhi.hy.vip.manager.wealth.processor.ProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;

import javax.inject.Inject;
import java.util.List;
import java.util.Map;

@Slf4j
public abstract class AbstractProcessorSelector implements ProcessorSelector {

    @Inject
    protected VipConfig vipConfig;
    @Inject
    private ProcessorFactory processorFactory;

    @Override
    public Optional<Processor> trySelectMatch() {
        return processorFactory.trySelectMatch(vipConfig.getVersion());
    }

    @Override
    public Optional<Processor> trySelectMatch(int version) {
        return processorFactory.trySelectMatch(version);
    }

    @Override
    public Map<Integer, Processor> selectAll() {
        return processorFactory.selectAll();
    }
}
