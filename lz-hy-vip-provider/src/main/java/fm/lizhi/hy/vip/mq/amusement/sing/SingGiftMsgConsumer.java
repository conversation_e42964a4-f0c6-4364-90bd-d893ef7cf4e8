package fm.lizhi.hy.vip.mq.amusement.sing;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.hy.amusement.dto.SingSongEvent;
import fm.lizhi.hy.vip.config.VipConfig;
import fm.lizhi.hy.vip.entity.SingUserSong;
import fm.lizhi.hy.vip.manager.push.LiveBroadcastManager;
import fm.lizhi.hy.vip.manager.sing.SingRoomManager;
import fm.lizhi.hy.vip.manager.sing.SingerIndexManager;
import fm.lizhi.hy.vip.mq.AbstractMsgConsumer;
import fm.lizhi.hy.vip.redis.amusement.SingRoomRedisManager;
import fm.lizhi.pp.util.json.JsonUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import java.util.Objects;

@Slf4j
@AutoBindSingleton
public class SingGiftMsgConsumer extends AbstractMsgConsumer {

    /**
     * 消息中的业务ID（幂等）
     */
    private static final String MSG_ID = "SING_ROOM_GIFT:";
    private static final int MSG_EXPIRE_TIME = 864000;

    @Inject
    private VipConfig vipConfig;
    @Inject
    private SingRoomManager singRoomManager;
    @Inject
    private LiveBroadcastManager liveBroadcastManager;
    @Inject
    private SingRoomRedisManager singRoomRedisManager;
    @Inject
    private SingerIndexManager singerIndexManager;

    @Override
    public boolean isPropProduct() {
        return vipConfig.isProduct();
    }

    @Override
    public String getKafkaKey() {
        return vipConfig.getSongGiftKafkaKey();
    }

    @Override
    public String getTopic() {
        return "lz_topic_hy_sing_gift";
    }

    @Override
    public String getGroupId() {
        return vipConfig.getSongGiftGroupId();
    }

    @Override
    public boolean handleMessage(@NonNull String message) {
        try {
            SingSongEvent event = JsonUtil.fromJson(message, SingSongEvent.class);
            long id = event.getId();
            long liveId = event.getLiveId();
            long userId = event.getUserId();
            long singerId = event.getSingerId();
            long songId = event.getSongId();
            long createTime = event.getCreateTime();
            int seat = event.getSeat();
            log.info("handleMessage, id={}, userId={}, singerId={}, songId={} , createTime={}, seat={}",
                    id, userId, singerId, songId, createTime,seat);
            if (!singRoomRedisManager.setnx(MSG_ID + id, MSG_EXPIRE_TIME)) {
                log.warn("handleMessage fail, not idempotent, message={}", message);
                return true;
            }

            if (id <= 0 || userId <= 0 || singerId <= 0 || songId <= 0) {
                log.error("handleMessage fail,param is not illegal id={}, userId={}, singerId={}, songId={} , createTime={}", id, userId, singerId, songId, createTime);
                return true;
            }

            if(userId == singerId){
                log.error("handleMessage fail,can not sing song to myself, id={}, userId={}, singerId={}, songId={} , createTime={}", id, userId, singerId, songId, createTime);
                return true;
            }

            //查询歌曲信息
            SingUserSong songInfo = singRoomManager.getSongInfo(songId);
            if (Objects.isNull(songInfo)) {
                //查不到歌曲信息直接跳掉
                log.error("songId :{} can not search the data from database", songId);
                return true;
            }
            //落库 点唱记录
            singRoomManager.saveWaitingSing(0, liveId, singerId, userId,id, songInfo.getSongName());

            //点歌统计
            //认证任务，只统计花了钱的
            singerIndexManager.addSingSongCount(singerId, userId);
            return true;
        } catch (Exception e) {
            log.error("handleMessage fail", e);
            return false;
        }
    }
}