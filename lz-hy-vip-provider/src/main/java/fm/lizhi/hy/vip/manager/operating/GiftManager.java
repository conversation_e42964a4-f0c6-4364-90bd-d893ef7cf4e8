package fm.lizhi.hy.vip.manager.operating;

import fm.lizhi.live.gift.protocol.BeanProto.Gift;
import fm.lizhi.payment.protocol.PaymentProto.BasicProduct;
import fm.lizhi.trade.kylin.protocol.TradeProto;

public interface GiftManager {
	/**
	 * 获取礼物信息
	 * @param giftId	礼物ID
	 * @return
	 */
	Gift getGift(long giftId);

	/**
	 * 获取基础商品价格（旧支付中心）
	 * @param basicId 基础商品id
 	 * @return
	 */
	BasicProduct getBasicProductPrice(long basicId);

	/**
	 * 获取基础商品价格（新支付中心）
	 * @param basicId 基础商品id
	 * @return
	 */
	TradeProto.ResponseGetGoodsById getBasicProductPrice2(long basicId);
}
