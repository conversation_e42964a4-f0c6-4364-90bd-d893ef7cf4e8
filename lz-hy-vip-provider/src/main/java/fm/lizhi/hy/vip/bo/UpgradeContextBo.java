package fm.lizhi.hy.vip.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpgradeContextBo {
    private boolean isUpgrade;
    private long userId;
    private int oldLevel;
    private long oldTotalCoinAmount;
    private int newLevel;
    private boolean onlyAddRecord = true;
}
