package fm.lizhi.hy.vip.dao.amusement;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 用户靓号表记录表
 *
 * @date 2022-05-19 06:00:21
 */
@Table(name = "`user_pretty_band_record`")
public class UserPrettyBandRecordPO {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 靓号ID，波段号
     */
    @Column(name= "`pretty_band`")
    private String prettyBand;

    /**
     * 靓号上架状态，0：未上架，1：上架
     */
    @Column(name= "`pretty_band_status`")
    private Integer prettyBandStatus;

    /**
     * 靓号开始时间
     */
    @Column(name= "`valid_start_time`")
    private Date validStartTime;

    /**
     * 靓号结束时间
     */
    @Column(name= "`valid_end_time`")
    private Date validEndTime;

    /**
     * 当时靓号价格
     */
    @Column(name= "`pretty_band_price`")
    private Long prettyBandPrice;

    /**
     * 生效类型，0：永久，1：限时
     */
    @Column(name= "`valid_time_type`")
    private Integer validTimeType;

    /**
     * 持续时间，精确到秒
     */
    @Column(name= "`valid_time`")
    private Long validTime;

    /**
     * 接收时间
     */
    @Column(name= "`receive_time`")
    private Date receiveTime;

    /**
     * 操作者
     */
    @Column(name= "`operation_name`")
    private String operationName;

    /**
     * 1：购买，2：续费，3：运营发放
     */
    @Column(name= "`receive_type`")
    private Integer receiveType;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getPrettyBand() {
        return prettyBand;
    }

    public void setPrettyBand(String prettyBand) {
        this.prettyBand = prettyBand == null ? null : prettyBand.trim();
    }

    public Integer getPrettyBandStatus() {
        return prettyBandStatus;
    }

    public void setPrettyBandStatus(Integer prettyBandStatus) {
        this.prettyBandStatus = prettyBandStatus;
    }

    public Date getValidStartTime() {
        return validStartTime;
    }

    public void setValidStartTime(Date validStartTime) {
        this.validStartTime = validStartTime;
    }

    public Date getValidEndTime() {
        return validEndTime;
    }

    public void setValidEndTime(Date validEndTime) {
        this.validEndTime = validEndTime;
    }

    public Long getPrettyBandPrice() {
        return prettyBandPrice;
    }

    public void setPrettyBandPrice(Long prettyBandPrice) {
        this.prettyBandPrice = prettyBandPrice;
    }

    public Integer getValidTimeType() {
        return validTimeType;
    }

    public void setValidTimeType(Integer validTimeType) {
        this.validTimeType = validTimeType;
    }

    public Long getValidTime() {
        return validTime;
    }

    public void setValidTime(Long validTime) {
        this.validTime = validTime;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName == null ? null : operationName.trim();
    }

    public Integer getReceiveType() {
        return receiveType;
    }

    public void setReceiveType(Integer receiveType) {
        this.receiveType = receiveType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", prettyBand=").append(prettyBand);
        sb.append(", prettyBandStatus=").append(prettyBandStatus);
        sb.append(", validStartTime=").append(validStartTime);
        sb.append(", validEndTime=").append(validEndTime);
        sb.append(", prettyBandPrice=").append(prettyBandPrice);
        sb.append(", validTimeType=").append(validTimeType);
        sb.append(", validTime=").append(validTime);
        sb.append(", receiveTime=").append(receiveTime);
        sb.append(", operationName=").append(operationName);
        sb.append(", receiveType=").append(receiveType);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}