package fm.lizhi.hy.vip.redis.amusement;

import fm.lizhi.pp.util.utils.EnvUtils;

/**
 * 描述：
 * redis key
 *
 * <AUTHOR>
 * @date 2018-07-23 13:55
 */
public enum LiveAmusementRedisKey implements CacheKeyGenerator.CacheKeyType {

    /** 魔法礼物、头像框**/
    MAGIC_GIFT_LOCK(true), // 魔法礼物初始化锁
    USER_AVATAR_WIDGET_GIFT_SORTSET, // 用户礼物中头像挂件 key+=userId member:头像挂件Id score:收到头像挂件的时间(ps:1532483822)
    USER_AVATAR_WIDGET_ACTIVITY_SORTSET, // 【废弃】用户活动头像挂件 key+=userId member:头像挂件Id score:收到头像挂件的时间(ps:1532483822)
    USER_AVATAR_WIDGET_GIFT_END_TIME_SORT_SET,// 所有用户礼物挂件到期时间，用于定时器扫描。 member:userId score:头像挂件失效时间(ps:1532483822)
    USER_AVATAR_WIDGET_ACTIVITY_END_TIME_SORT_SET,// 【废弃】所有用户活动挂件到期时间，用于定时器扫描。 member:userId score:头像挂件失效时间(ps:1532483822)
    USER_AVATAR_WIDGET_ACTIVITY_HASH, //用户活动头像挂件 field: 用户Id value:json
    AVATAR_WIDGET_HASH(true), // 头像挂件HASH filed:bless(祝福) value:json filed:curse(诅咒)
    MAGIC_GIFT_ID_LIST_MASTER(true), //魔法礼物开奖池 value:头像框id
    MAGIC_GIFT_ID_LIST_SALVE(true), //魔法礼物备用开奖池 value:头像框id
    MAGIC_GIFT_ID_STR_KEY(true), //魔法礼物开奖池key
    MAGIC_GIFT_NEXT_LOTTERY_HASH(true), //魔法礼物下个开奖 field:userId value:

    /** 头像框养成计划 **/
    AVATAR_LEVEL_STR(true), // 等级头像框信息
    AVATAR_LEVEL_USER_COUNT_HASH(true), //【废弃】 等级头像框信息总数
    AVATAR_LEVEL_COUNT_STR(true), //【废弃】 等级头像框信息总数
    AVATAR_LEVEL_BASE_HASH(true), // 头像挂件的子等级信息 field:baseWidgetId value:json
    AVATAR_LEVEL_USER_HASH, // 用户等级头像框Hash , field: userId value:json
    AVATAR_LEVEL_LOTTERY_COUNT_SORT_SET(true), // 用户等级头像框key_头像框ID member:userId,score:次数

    /** 活動魔盒 **/
    MAGIC_GIFT_ACT_CONF_STR, // 活動魔盒配置

    /** 用户座驾 **/
    // 用户座驾，包含过期的
    USER_MOUNTS_HASH, //field: 用户Id value:json
    /** 礼物权限（根据座驾等级进行特权礼物发放） **/
    GIFT_PREMISSION_TIP(true), // field:giftId value:
    GIFT_PREMISSION_HASH(true), // field:座駕等級 value:礼物ID (多个礼物 0_1_2)
    USER_MOUNT_FRAGMENT_ID_HASH, // 用户座驾碎片id， field=userId_mountId value=fragmentId
    MOUNT_LIST_STR, // 座驾列表json
    MOUNT_EXPIRE_PUSH_HASH, // 座驾过期提醒过的用户记录。key+=yyMMdd，field=userId_mountId，value=推送次数。expire:1d
    MOUNT_EXPIRE_PUSH_LOCK, // 座驾过期提醒过的用户锁。key+=yyMMdd_userId_mountId
    MOUNT_ADD_USER_MOUNT_UNIQUE(true), // 发放座驾的防重复提交

    // 座驾碎片抽奖
    MOUNT_FRAGMENT_LOTTERY_AWARD_POOL_LIST(true), // 座驾碎片开奖的奖池。key+=num(0/1。1是备用奖池)。 member: "mountId,count"。
    MOUNT_FRAGMENT_LOTTERY_AWARD_POOL_INIT_LOCK(true), // 座驾碎片开奖的奖池锁。
    MOUNT_FRAGMENT_LOTTERY_USER_TIMES_HASH, // 座驾碎片用户抽奖次数，field=userId, value=times

    //锁
    LOCK,

    //装扮商城
    TYPE_ON_MALL_EXIST,

    //点唱房-歌手中心
    SINGER_INDEX_BASIC_FINISH_AWARD_LOCK,
    //用户是否签约点唱房
    USER_IS_SIGN_SINGER_ROOM,
    //歌手歌曲列表
    SINGER_SONG_LIST,
    //歌手歌曲数
    SINGER_SONG_LIST_COUNT,
    //歌手被点歌数
    SING_SONG_COUNT,
    //歌手被点歌用户数
    SING_SONG_ORDER_USER_COUNT,
    //用户是否是歌手
    USER_IS_SINGER,
    //用户是否是歌手
    UPLOAD_AUDIO_MAX_DURATION,
    //直播间当前点唱的记录
    LIVE_SING_NOW_RECORD_ID,
    //标记不存在旧的失效歌手数据
    NO_INEFFECTIVE_SINGER,
    //歌曲信息
    SONG_INFO,
    //歌手的代表作歌曲-统计结果
    SINGER_SIGNATURE_SONG,
    //歌手的最近被点歌曲-队列
    SINGER_SING_SONG_QUEUE,
    /**
     * 直播间待唱记录
     * 结构 :zset
     * score: 送礼的时间戳+麦序
     */
    LIVE_SING_WAITING_RECORD,

    // 牵手各等级魅力值门槛
    BLIND_HAND_CHARM_THRESHOLD,

    /**
     * 用户弹幕数量
     * 结构: string
     * USER_NOBEL_SCREEN_AMOUNT_{userId}
     * value:弹幕数量
     * 有效期：用户贵族身份的保护期
     */
    USER_NOBEL_SCREEN_AMOUNT,


    /**
     * 用户贵族过期集合
     */
    USER_NOBLE_RECORD_EXPIRE,


    /**
     * 用户设置的权限
     */
    USER_NOBLE_PRIVILEGE,

    /**
     * 用户贵族等级id
     */
    USER_NOBLE_LEVEL_ID,

    /**
     * 操作贵族分布式锁
     */
    NOBLE_OPERATE_LOCK,

    /**
     * 融云白名单同步分布式锁
     */
    RONG_WHITE_LIST_SYNC_LOCK,

    /**
     * 开启融云白名单的用户列表
     */
    OPEN_RONG_WHITE_LIST_USER_LIST,

    /**
     * 用户充值金额（自然周）
     */
    USER_RECHARGE_COIN,

    /**
     * vip用户充值金额（两周结算一次）
     */
    VIP_USER_RECHARGE_COIN,

    /**
     * 用户名称特效
     */
    VIP_USER_NAME_EFFECT,

    ;

    private static String envType = ""; // 环境类型 预发:PRE
    private boolean afx; // 是否区分线上和预发

    LiveAmusementRedisKey() {
    }

    LiveAmusementRedisKey(boolean afx) {
        this.afx = afx;
    }


    public static void setEnvType(String envType) {
        LiveAmusementRedisKey.envType = envType;
    }

    @Override
    public String getKey(Object... args) {
        StringBuilder sb = new StringBuilder(this.getPrefix());

        switch (this) {
            default:
                sb.append("_");
                sb.append(this.name().toUpperCase());
                break;
        }

        for (Object o : args) {
            sb.append("_").append(o);
        }
        return sb.toString();
    }

    @Override
    public String getPrefix() {
        if (afx && EnvUtils.isPre()) {
            return "LZ_LIVE_COMMON_AMUSEMENT_PRE";
        }
        return "LZ_LIVE_COMMON_AMUSEMENT";
    }
}
