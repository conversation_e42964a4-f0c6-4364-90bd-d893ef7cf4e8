package fm.lizhi.hy.vip.manager.operating.impl;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.datastore.query.Compare;
import fm.lizhi.datastore.query.Condition;
import fm.lizhi.datastore.query.Query;
import fm.lizhi.hy.vip.bean.operating.AwardRecievedRecord;
import fm.lizhi.hy.vip.dao.operating.AwardRecievedRecordDao;
import fm.lizhi.hy.vip.manager.operating.AwardRecievedManager;

import java.util.Date;
import java.util.List;

/**
 * Created in 2018-07-02 11:29.
 *
 * <AUTHOR>
 */
@AutoBindSingleton(AwardRecievedManager.class)
public class AwardRecievedManagerImpl implements AwardRecievedManager {


    @Inject
    AwardRecievedRecordDao awardRecievedRecordDao;

    @Override
    public List<AwardRecievedRecord> getStatCount(long taskId, Date beginTime, Date endTime) {


        return null;
    }

    @Override
    public int count(long taskId, long userId, Date beginTime, Date endTime) {
        AwardRecievedRecord awardRecievedRecord = new AwardRecievedRecord();
        Condition condition = new Condition("create_time", Compare.GreaterEqual, beginTime)
                .and("create_time", Compare.LessEqual, endTime);

        if (taskId > 0L) {
            condition.and("task_id", Compare.Is, taskId);
        }
        if (userId > 0L) {
            condition.and("user_id", Compare.Is, userId);
        }
        Long count = awardRecievedRecord.where(condition).count();
        return count.intValue();
    }

    @Override
    public List<AwardRecievedRecord> list(long taskId, long userId, Date beginTime, Date endTime,int offset, int count) {
        AwardRecievedRecord awardRecievedRecord = new AwardRecievedRecord();
        Condition condition = new Condition("create_time", Compare.GreaterEqual, beginTime)
                .and("create_time", Compare.LessEqual, endTime);

        if (taskId > 0L) {
            condition.and("task_id", Compare.Is, taskId);
        }
        if (userId > 0L) {
            condition.and("user_id", Compare.Is, userId);
        }

        return awardRecievedRecord.where(condition).limit(offset,count).fetch();
    }

    @Override
    public List<AwardRecievedRecord> getUserRecord(long userId, long taskId){
        Query query = new Query(new AwardRecievedRecord());
        Condition condition = new Condition("task_id", Compare.Is, taskId).and("user_id", Compare.Is, userId);
        query.setCondition(condition);
        return query.fetch();
    }
}
