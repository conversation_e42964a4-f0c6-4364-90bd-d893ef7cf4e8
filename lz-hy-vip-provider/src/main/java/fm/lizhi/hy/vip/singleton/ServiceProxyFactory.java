package fm.lizhi.hy.vip.singleton;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.hy.family.api.FamilyService;
import fm.hy.family.api.PlayerSignService;
import fm.hy.family.api.RoomWhitelistService;
import fm.lizhi.biz.right.hy.api.RightService;
import fm.lizhi.biz.right.hy.api.UserRightService;
import fm.lizhi.biz.trade.hy.api.GoodsService;
import fm.lizhi.biz.trade.hy.api.OrderSearchService;
import fm.lizhi.biz.trade.hy.api.TradeService;
import fm.lizhi.chat.api.ChatManagerService;
import fm.lizhi.common.verify.api.UserVerifyBizService;
import fm.lizhi.commons.config.event.ConfigFuture;
import fm.lizhi.commons.config.service.ConfigService;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.commservice.api.IpService;
import fm.lizhi.datacenter.comment.pp.api.TransientCommentService;
import fm.lizhi.datacenter.user.api.model.UserService;
import fm.lizhi.finance.accounting.engines.api.QueryService;
import fm.lizhi.growth.adfilter.api.ChannelAdvertiseFilterManagerService;
import fm.lizhi.hy.account.device.api.PpUserDeviceService;
import fm.lizhi.hy.account.innermap.api.PpUserInnerMapService;
import fm.lizhi.hy.chat.api.HyChatService;
import fm.lizhi.hy.chat.api.PpChatService;
import fm.lizhi.hy.content.api.LiveArtTabTopPositionService;
import fm.lizhi.hy.rank.api.GlobalLiveRoomRankingService;
import fm.lizhi.hy.security.standard.api.HyStandardTextReviewService;
import fm.lizhi.hy.social.api.RelationShipService;
import fm.lizhi.hy.user.account.user.api.HyNewUserService;
import fm.lizhi.hy.usergroup.api.UserGroupService;
import fm.lizhi.hy.vip.config.OssConfig;
import fm.lizhi.hy.vip.config.VipConfig;
import fm.lizhi.hy.vip.util.ConfigInfoUtil;
import fm.lizhi.live.amusement.hy.api.HyAmusementService;
import fm.lizhi.live.amusement.hy.api.HyNewLiveAmusementService;
import fm.lizhi.live.amusement.hy.api.NewLiveAmusementService;
import fm.lizhi.live.amusement.task.api.IntegralService;
import fm.lizhi.live.data.api.LiveDataRoomService;
import fm.lizhi.live.data.api.UserBehaviorService;
import fm.lizhi.live.data.api.UserDataService;
import fm.lizhi.live.gift.api.GiftBoxRelationService;
import fm.lizhi.live.gift.api.GiftGroupService;
import fm.lizhi.live.gift.api.GiftListService;
import fm.lizhi.live.operation.hy.api.OperationMessageService;
import fm.lizhi.live.popularity.guard.api.PopularityGuardService;
import fm.lizhi.live.pp.core.api.LivePpUserService;
import fm.lizhi.live.pp.core.api.PpCoreUploadService;
import fm.lizhi.live.pp.player.api.PpPlayerQuestService;
import fm.lizhi.live.push.api.LiveBroadcastService;
import fm.lizhi.live.push.api.PushService;
import fm.lizhi.live.push.api.StudioLiveService;
import fm.lizhi.live.room.hy.api.*;
import fm.lizhi.payment.api.PaymentAccountQueryService;
import fm.lizhi.payment.api.PaymentAccountService;
import fm.lizhi.pp.util.factory.proxy.BaseServiceFactory;
import fm.lizhi.trade.first.trade.api.UserFirstTradeService;
import fm.lizhi.voicefriend.api.RoomService;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import java.util.Map;

@Slf4j
@AutoBindSingleton
public class ServiceProxyFactory extends BaseServiceFactory {

    @Override
    protected void configure() {
        super.configure();
        // 自定义IOC注入
//        super.bind(GuidGenerator.class).toInstance(new GuidGenerator(vipConfig.getServerId()));
        super.bind(OssConfig.class).toInstance(getOssConfig());

//        super.configure();
        // 声明注入RPC Service
        guiceUtils.bind(LiveBroadcastService.class);
//        guiceUtils.bind(MountService.class);
        guiceUtils.bind(HyNewUserService.class);
        guiceUtils.bind(StudioLiveService.class);
        guiceUtils.bind(TransientCommentService.class);
        guiceUtils.bind(UserBehaviorService.class);
        guiceUtils.bind(PpUserInnerMapService.class);
        guiceUtils.bind(LiveService.class);
        guiceUtils.bind(PopularityGuardService.class);
        guiceUtils.bind(ChatManagerService.class);
        guiceUtils.bind(LiveArtTabTopPositionService.class);
        guiceUtils.bind(UserFirstTradeService.class);
        guiceUtils.bind(LiveRoomService.class);
//        guiceUtils.bind(DressUpInfoService.class);
        guiceUtils.bind(FamilyService.class);
        guiceUtils.bind(RoomWhitelistService.class);
        guiceUtils.bind(PlayerSignService.class);
        guiceUtils.bind(GlobalLiveRoomRankingService.class);
        guiceUtils.bind(PpChatService.class);
        //互娱商业化通用商品模块
        guiceUtils.bind(GoodsService.class);
        //互娱商业化通用特权模块
        guiceUtils.bind(RightService.class);
        guiceUtils.bind(UserRightService.class);
        //互娱商业化通用订单模块
        guiceUtils.bind(TradeService.class);
        guiceUtils.bind(OrderSearchService.class);
        guiceUtils.bind(PaymentAccountService.class);
//        guiceUtils.bind(PropRewardService.class);
        guiceUtils.bind(LiveBroadcastService.class);
        guiceUtils.bind(GiftGroupService.class);
        guiceUtils.bind(GiftBoxRelationService.class);
//        guiceUtils.bind(UserFirstRecordService.class);
        guiceUtils.bind(GlobalLiveRoomRankingService.class);
        guiceUtils.bind(PpUserDeviceService.class);
        // 荔枝派房间
        guiceUtils.bind(RoomService.class);
        guiceUtils.bind(PopularityService.class);
        guiceUtils.bind(UserService.class);
        guiceUtils.bind(GiftListService.class);
        guiceUtils.bind(ChannelAdvertiseFilterManagerService.class,2000L);
        guiceUtils.bind(IpService.class);
        guiceUtils.bind(UserDataService.class);
//        guiceUtils.bind(MessagePushService.class);
        guiceUtils.bind(fm.lizhi.trade.kylin.api.TradeService.class);
        guiceUtils.bind(UserGroupService.class);
        guiceUtils.bind(IntegralService.class);
        guiceUtils.bind(PaymentAccountQueryService.class);
        guiceUtils.bind(PpCoreUploadService.class);
        guiceUtils.bind(HyStandardTextReviewService.class);
        guiceUtils.bind(OperationMessageService.class);
        guiceUtils.bind(HyAmusementService.class);
        guiceUtils.bind(HyNewLiveAmusementService.class);
        guiceUtils.bind(NewLiveAmusementService.class);
        guiceUtils.bind(PpPlayerQuestService.class);
        guiceUtils.bind(LiveDataRoomService.class);
        guiceUtils.bind(LiveRoomRoleService.class);
        guiceUtils.bind(LiveNewService.class);
        guiceUtils.bind(LivePpUserService.class);
        guiceUtils.bind(fm.lizhi.finance.accounting.engines.api.TradeService.class);
        guiceUtils.bind(PushService.class);
        guiceUtils.bind(QueryService.class);
        guiceUtils.bind(HyChatService.class);
        guiceUtils.bind(UserVerifyBizService.class);
        guiceUtils.bind(RelationShipService.class);
    }

    private OssConfig getOssConfig() {
        OssConfig ossConfig = ConfigService.loadConfig(OssConfig.class, new ConfigFuture() {
            @Override
            public void configChange(String file, Map<String, String> configs) {
                if (configs.containsKey("emotionConfig")) {
                    ConfigInfoUtil.reloadEmotionInfo(configs.get("emotionConfig"));
                }

                if (configs.containsKey("followOfficialAccountConfig")) {
                    ConfigInfoUtil.reloadFollowOfficialAccountConfig(configs.get("followOfficialAccountConfig"));
                }
            }
        }, "oss_config");

        // 初始化值
        ConfigInfoUtil.reloadEmotionInfo(ossConfig.getEmotionConfig());
        ConfigInfoUtil.reloadFollowOfficialAccountConfig(ossConfig.getFollowOfficialAccountConfig());

        return ossConfig;
    }
}
