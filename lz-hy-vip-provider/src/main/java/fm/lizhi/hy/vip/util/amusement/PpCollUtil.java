package fm.lizhi.hy.vip.util.amusement;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * PP集合操作工具类。
 *
 * <AUTHOR>
 * @since 2020-05
 */
@Slf4j
public class PpCollUtil {

    private static final Random random = new Random();
    private static final Comparator COMP_SHUFFLE = new Comparator() {
        @Override
        public int compare(Object o1, Object o2) {
            return Double.compare(Math.random(), 0.5);
        }
    };

    public static <T, V> List<V> extract(Iterable<T> iterable, Getter<T, V> get) {
        List<V> list = new ArrayList<>();
        for (T t : iterable) {
            list.add(get.get(t));
        }
        return list;
    }

    public static <T> List<T> retainNewList(Iterable<T> iterable, IApply<T> func) {
        List<T> list = new ArrayList<>();
        for (T t : iterable) if (func.apply(t)) list.add(t);
        return list;
    }

    public static <T, V> Set<V> extractSet(Iterable<T> iterable, Getter<T, V> get) {
        Set<V> vSet = new HashSet<>();
        for (T t : iterable) {
            vSet.add(get.get(t));
        }
        return vSet;
    }

    public static <T, K> Map<K, List<T>> group2Map(Iterable<T> iterable, Getter<T, K> keyGetter) {
        Map<K, List<T>> map = new HashMap<>();
        for (T t : iterable) {
            K k = keyGetter.get(t);
            List<T> list = map.get(k);
            if (list == null) {
                list = new ArrayList<>();
                map.put(k, list);
            }
            list.add(t);
        }
        return map;
    }

    public static <T, K> Map<K, Integer> group2MapCount(Iterable<T> iterable, Getter<T, K> keyGetter) {
        Map<K, List<T>> kListMap = group2Map(iterable, keyGetter);
        Map<K, Integer> map = new HashMap<>();
        for (Map.Entry<K, List<T>> e : kListMap.entrySet()) {
            map.put(e.getKey(), e.getValue().size());
        }
        return map;
    }

    public static <T, K, V> Map<K, V> buildMap(Iterable<T> iterable, Getter<T, K> keyGetter, Getter<T, V> valGetter) {
        Map<K, V> map = new HashMap<>();
        for (T t : iterable) {
            K k = keyGetter.get(t);
            V v = valGetter.get(t);
            if (k == null || v == null) {
                continue;
            }
            map.put(k, v);
        }
        return map;
    }

    public static <T, K> Map<K, T> buildMap(Iterable<T> iterable, Getter<T, K> keyGetter) {
        return buildMap(iterable, keyGetter, new Getter<T, T>() {
            @Override
            public T get(T t) {
                return t;
            }
        });
    }

    public static <T, V> List<V> transform(Iterable<T> ts, IFunc<T, V> trans) {
        List<V> list = new ArrayList<>();
        for (T t : ts) {
            list.add(trans.apply(t));
        }
        return list;
    }

    @SafeVarargs
    public static <T> Set<T> newHashSet(Set<T>... sets) {
        Set<T> totalSet = new HashSet<>();
        for (Set<T> set : sets)
            if (!CollectionUtils.isEmpty(set)) totalSet.addAll(set);
        return totalSet;
    }

    @SafeVarargs
    public static <T> List<T> newArrayList(Collection<T>... cols) {
        List<T> total = new ArrayList<>();
        for (Collection<T> c : cols)
            if (!CollectionUtils.isEmpty(c)) total.addAll(c);
        return total;
    }

    /**
     * 从中随机挑选n个。
     */
    public static <T> List<T> pickRandom(List<T> list, int count) {
        return pickRandom(list, count, System.currentTimeMillis());
    }

    /**
     * 从中随机挑选n个。
     */
    public static <T> List<T> pickRandom(List<T> list, int count, long seed) {
        List<T> resList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) return resList;
        //快速取1项
        if (count == 1) {
            resList.add(pickRandomOne(list, seed));
            return resList;
        }

        //打乱取前n项
        Collections.shuffle(list, new Random(seed));
        if (count >= list.size()) return list;
        return list.subList(0, count);
    }

    /**
     * 排序指定部分。越界将不执行。
     */
    public static <T> void sort(List<T> list, int startInclude, int endInclude, Comparator<? super T> comp) {
        if (startInclude < 0 || endInclude > list.size() - 1) return;
        if (endInclude - startInclude < 2) return;
        //排序新数组
        List<T> subList = list.subList(startInclude, endInclude + 1);
        Collections.sort(subList, comp);
        //应用排序到原数组
        for (int i = 0; i < subList.size(); i++) {
            list.set(i+startInclude, subList.get(i));
        }
    }

    public static <T> T pickRandomOne(List<T> list) {
        return pickRandomOne(list, System.currentTimeMillis());
    }

    public static <T> T pickRandomOne(List<T> list, long seed) {
        if (CollectionUtils.isEmpty(list)) return null;
        if (list.size() == 1) return list.get(0);

        //随机
        Random pRandom = new Random(seed);
        return list.get(pRandom.nextInt(list.size()));
    }

    /**
     * 打乱指定部分。越界将不执行。
     */
    public static <T> void shuffle(List<T> list, int startInclude, int endInclude) {
        sort(list, startInclude, endInclude, COMP_SHUFFLE);
    }

    public static <T> List<T> randomSub(Collection<T> cols, int subSize, long seed) {
        List<T> subList = pickRandom(new ArrayList<T>(cols), subSize, seed);
        return new ArrayList<>(subList);
    }

    public static <T> Map<T, Integer> buildIndexMap(List<T> list) {
        Map<T, Integer> map = new HashMap<>();
        for (int i = 0; i < list.size(); i++) {
            map.put(list.get(i), i);
        }
        return map;
    }

    public static <T> T filterFirst(List<T> list, IApply<T> func) {
        for (T t : list) {
            if(func.apply(t)){
                return t;
            }
        }
        return null;
    }

    public static <T> T safeGetFirst(List<T> list) {
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    public static <T> List<T> filterToList(Collection<T> cols, IApply<T> func) {
        List<T> tList = new ArrayList<>();
        for (T t : cols) {
            if(func.apply(t)){
                tList.add(t);
            }
        }
        return tList;
    }

    public static <T> Set<T> filterToSet(Collection<T> cols, IApply<T> func) {
        Set<T> tSet = new HashSet<>();
        for (T t : cols) {
            if(func.apply(t)){
                tSet.add(t);
            }
        }
        return tSet;
    }

    public static <T> long sum(List<T> list, Getter<T, Long> getter) {
        long sum = 0;
        for (T t : list) {
            if (getter.get(t) != null) {
                sum += getter.get(t);
            }
        }
        return sum;
    }

    public static void addAllLong(Collection<Long> longSet, Collection<String> longStrSet) {
        for (String str : longStrSet) {
            try {
                longSet.add(Long.parseLong(str));
            } catch (Exception e) {
                log.info("addAllLongError str={},e={}", str, e);
            }
        }
    }

    public static boolean containsAny(String str, Collection<String> keywords) {
        for (String keyword : keywords) {
            if (str.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    public static <V, T> int countValidDistinct(Collection<List<V>> vBatchList, final Integer validCountInclude, final Getter<V, T> getter) {
        return filterToList(vBatchList, new IApply<List<V>>() {
            @Override
            public boolean apply(List<V> vList) {
                return extractSet(vList, getter).size() >= validCountInclude;
            }
        }).size();
    }

    /**
     * 把to中的from元素移到尾部
     * @param from
     * @param to
     * @param <T>
     */
    public static <T> void move2Tail(List<T> from, List<T> to){
        if(from.isEmpty()){
            return;
        }
        to.removeAll(from);
        to.addAll(from);
    }

    /**
     * 取子数组
     * @param offset 起始下标。数组下标从0开始
     * @param limit 共取多少个。
     */
    public static <T> List<T> safeSubList(List<T> list, int offset, int limit) {
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list.stream().skip(offset).limit(limit).collect(Collectors.toList());
    }
}
