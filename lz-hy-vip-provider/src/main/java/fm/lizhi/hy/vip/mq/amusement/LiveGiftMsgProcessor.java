package fm.lizhi.hy.vip.mq.amusement;

import com.dianping.cat.Cat;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.queue.callback.MsgFuture;
import fm.lizhi.hy.vip.constant.amusement.CatConst;
import fm.lizhi.hy.vip.manager.common.amusement.LiveGiftBroadcastManager;
import fm.lizhi.hy.vip.manager.common.amusement.LiveGiftManager;
import fm.lizhi.hy.vip.manager.common.info.SendMagicGiftEvent;
import fm.lizhi.hy.vip.util.amusement.EnvUtils;
import fm.lizhi.live.gift.bean.GiftMsg;
import fm.lizhi.live.gift.constants.GiftType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 直播送礼处理
 *
 * <AUTHOR>
 * @date 2018/7/23
 */
@Slf4j
@AutoBindSingleton
public class LiveGiftMsgProcessor implements MsgFuture {
    /**
     * 魔法礼物的biz
     */
    private static final int MAGIC_BIZ = 1;

    @Inject
    private LiveGiftManager liveGiftManager;

//    @Inject
//    private MagicGiftManager magicGiftManager;

    @Inject
    private LiveGiftBroadcastManager liveGiftBroadcastManager;

    @Override
    public boolean msgReceived(String key, String msg, long timestamp, int partition, long offset) {
        if (StringUtils.isBlank(msg)) {
            return true;
        }
        if (EnvUtils.isPre()) log.info("LiveGiftMsgProcessor`json={}", msg);
        GiftMsg giftMsg = null;
        try {
            giftMsg = JsonUtil.loads(msg, GiftMsg.class);
            if (giftMsg == null) {
                return true;
            }
        } catch (Exception e) {
            log.error("LiveSendGiftProcessor parseObject error {}", e);
            return true;
        }

        SendMagicGiftEvent event = new SendMagicGiftEvent(giftMsg);
        // 飘屏逻辑
        try {
            liveGiftBroadcastManager.afterSendGift(event);
        } catch (Exception e) {
            log.error("liveGiftManager.afterSendBoxGift  exception:", e);
        }

        //判断是否为宝箱礼物
        boolean isBoxGift = giftMsg.getGiftType() == GiftType.BOX_GIFT.getValue();
        if (isBoxGift) {
            try {
                liveGiftManager.afterSendBoxGift(giftMsg);
            } catch (Exception e) {
                log.error("liveGiftManager.afterSendBoxGift  exception:", e);
                return true;
            }
        }

//        // 魔法礼物获得头像挂件
//        if (giftMsg.getBiz() == MAGIC_BIZ) {
//            try {
//                magicGiftManager.afterSendMagicGift(event, 0);
//            } catch (Exception e) {
//                Cat.logMetricForCount(CatConst.MagicGift.LIVE_KAFKA);
//                return true;
//            }
//        }
//        log.error("magicGiftManager.afterSendMagicGift live exception:  move common_amusement to vop");

        return true;
    }


}
