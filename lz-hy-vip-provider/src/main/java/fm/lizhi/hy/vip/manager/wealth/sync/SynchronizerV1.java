package fm.lizhi.hy.vip.manager.wealth.sync;

import com.google.common.base.Optional;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.hy.vip.bean.wealth.common.WealthLevelBean;
import fm.lizhi.hy.vip.bean.wealth.v1.WealthLevelBeanV1;
import fm.lizhi.hy.vip.manager.wealth.WealthLevelBeanV1Manager;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;

@Slf4j
@AutoBindSingleton
public class SynchronizerV1 extends AbstractSynchronizer {

    private static final int VERSION = 1;

    @Inject
    private WealthLevelBeanV1Manager wealthLevelBeanV1Manager;

    @Override
    public int getVersion() {
        return VERSION;
    }

    @Override
    protected void doSync(long userId) {
        log.info("doSync, nothing to do, userId={}", userId);
    }

    @Override
    protected WealthLevelBean doGet(long userId) {
        Optional<WealthLevelBeanV1> wealthLevelBeanOpt = wealthLevelBeanV1Manager.getByUserId(userId);
        return wealthLevelBeanOpt.isPresent() ? wealthLevelBeanOpt.get() : null;
    }

    @Override
    public void syncForCorrectData(long userId) {
        log.info("syncForCorrectData, nothing to do, userId={}", userId);
    }
}
