package fm.lizhi.hy.vip.bean.operating;

import fm.lizhi.datastore.Db;
import fm.lizhi.datastore.DbField;
import fm.lizhi.datastore.StoreBean;
import fm.lizhi.hy.vip.constant.DBConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Id;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@Db(database = DBConstants.DB_TASKS, table = DBConstants.TABLE_PRIZE_PACKAGE)
public class PrizePackage extends StoreBean{

    /**
     * 主键id
     */
    @Id
    private Long id;

    /**
     * 基础商品ID
     */
    @DbField(name = "base_id")
    private Long baseId;

    /**
     * 奖励礼品ID
     */
    @DbField(name = "prize_id")
    private Long prizeId;

    /**
     * 苹果内购ID
     */
    @DbField(name = "iap_id")
    private String iapId;

    /**
     * 奖励礼品ID(金币第三方支付基础商品ID)
     */
    @DbField(name = "price_prize_id")
    private Long pricePrizeId;

    /**
     * 奖励类型：1礼物，2守护，3勋章，4背景
     * PS: 注意下，这里的type跟awaraType类的type不一样
     */
    @DbField(name = "award_type")
    private Integer awardType;

    /**
     * 标题
     */
    @DbField(name = "title")
    private String title;

    /**
     * 奖励数量
     */
    @DbField(name = "award_num")
    private Integer awardNum;

     /**
     * 状态 0:有效 1:失效
     */
    @DbField(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @DbField(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @DbField(name = "modify_time")
    private Date modifyTime;

    /**
     * 任务奖励表id
     */
    @DbField(name = "task_award_id")
    private Long taskAwardId;

}

