package fm.lizhi.hy.vip.api.impl;

import com.google.common.collect.Lists;
import com.google.inject.Inject;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.hy.amusement.api.DressUpInfoService;
import fm.lizhi.hy.amusement.protocol.DressUpInfoProto;
import fm.lizhi.hy.vip.adapter.CommentStyleAdapter;
import fm.lizhi.hy.vip.adapter.CommentUserStyleAdapter;
import fm.lizhi.hy.vip.api.CommentStyleManagerService;
import fm.lizhi.hy.vip.bean.oss.CommentStyle;
import fm.lizhi.hy.vip.bean.oss.CommentUserStyle;
import fm.lizhi.hy.vip.manager.PpNewUserManager;
import fm.lizhi.hy.vip.manager.oss.CommentStyleDataManager;
import fm.lizhi.hy.vip.protocol.CommentStyleProto;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.locks.LockSupport;

/**
 * Created in 2018-05-18 11:55.
 *
 * <AUTHOR>
 */
@ServiceProvider
public class CommentStyleManagerServiceImpl implements CommentStyleManagerService {
    private static Logger logger = LoggerFactory.getLogger(CommentStyleManagerServiceImpl.class);

    private static final int MAX_PER_SIZE = 500;

    @Inject
    private CommentStyleDataManager commentStyleDataManager;
    @Inject
    private CommentStyleAdapter commentStyleAdapter;
    @Inject
    private CommentUserStyleAdapter userCommentStyleAdapter;
    @Inject
    private PpNewUserManager ppNewUserManager;
    @Inject
    private DressUpInfoService dressUpInfoService;


    /**
     * 查询评论样式
     *
     * @param status      状态 1 生效 0时效
     * @param currentPage 当前页码 从1开始
     * @param pageSize    页容量 (最大100)
     * @return
     */
    @Override
    public Result<CommentStyleProto.ResponseCommentStyleListByFilter> commentStyleListByFilter(int status, int currentPage, int pageSize) {
        CommentStyleProto.ResponseCommentStyleListByFilter.Builder builder = CommentStyleProto.ResponseCommentStyleListByFilter.newBuilder();
        builder.setStatus(status);
        builder.setCurrentPage(currentPage);
        builder.setPageSize(pageSize);
        int skip = (currentPage - 1) * pageSize;
        long total = commentStyleDataManager.getCommentStyleTotal(status);
        builder.setTotal((int) total);
        List<CommentStyle> commeentStyles = new ArrayList<>();
        if (skip < total) {
            commeentStyles = commentStyleDataManager.findCommentStyleByFilter(status, skip, pageSize);
        }
        if (!commeentStyles.isEmpty()) {
            List<CommentStyleProto.CommentStyle> commentStyles = commentStyleAdapter.adapter(commeentStyles);
            builder.addAllCommentStyles(commentStyles);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
    }



    /**
     * 查询用户评论样式
     *
     * @param status      状态 0 未生效 1 已生效 2 已失效
     * @param band        波段号
     * @param userId      用户ID
     * @param currentPage 当前页码 从1开始
     * @param pageSize    $param.comment
     * @return
     */
    @Override
    public Result<CommentStyleProto.ResponseUserCommentStyleListByFilter> userCommentStyleListByFilter(int status, String band, long userId, int currentPage, int pageSize) {
        CommentStyleProto.ResponseUserCommentStyleListByFilter.Builder builder = CommentStyleProto.ResponseUserCommentStyleListByFilter.newBuilder();
        builder.setStatus(status);
        builder.setBand(band);
        builder.setUserId(userId);
        builder.setCurrentPage(currentPage);
        builder.setPageSize(pageSize);
        Date operationDate = new Date();
        long total = commentStyleDataManager.getUserCommentStyleCount(status, band, userId, operationDate);
        builder.setTotal((int) total);
        int skip = (currentPage - 1) * pageSize;
        List<CommentUserStyle> userCommentStyles = new ArrayList<>();
        if (skip < total) {
            userCommentStyles = commentStyleDataManager.findUserCommentStyleByFilter(status, band, userId, operationDate, skip, pageSize);
        }
        if (!userCommentStyles.isEmpty()) {
            for (CommentUserStyle userCommentStyle : userCommentStyles) {
                builder.addUserCommentStyles(userCommentStyleAdapter.coverToProto(userCommentStyle));
            }
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
    }


    @Override
    public Result<Void> addCommentStyle(String iosColor, String andColor, String iosImage, String andImage, int status) {
        return new Result<>(0, null);
    }


    @Override
    public Result<Void> delCommentStyle(long id) {
        return new Result<>(0, null);
    }

    @Override
    public Result<Void> updateCommentStyle(long id, String iosColor, String andColor, String iosImage, String andImage) {
        return new Result<>(0, null);
    }

    @Override
    public Result<Void> addUserCommentStyle(long userId, String band, long styleId, long startTime, long endTIme) {
        return new Result<>(0, null);

    }

    @Override
    public Result<CommentStyleProto.ResponseBatchAddUserCommentStyle> batchAddUserCommentStyle(List<String> bands, long styleId, long startTime, long endTime) {
        return new Result<>(0, null);
    }

    @Override
    public Result<Void> delUserCommentStyle(long id) {
        return null;
    }


    @Override
    public Result<Void> updateUserCommentStyle(long id, long styleId, long startTime, long endTIme) {
        return new Result<>(0, null);
    }

    @Override
    public Result<CommentStyleProto.ResponseCommentStyle> commentStyle(long id) {
        return new Result<>(0, null);
    }

    @Override
    public Result<CommentStyleProto.ResponseUserCommentStyle> userCommentStyle(long id) {
        return new Result<>(0, null);
    }


    @Override
    public Result<CommentStyleProto.ResponseUserCommentStyleByUserId> userCommentStyleByUserId(long userId) {
        return new Result<>(0, null);
    }

    @Override
    public Result<CommentStyleProto.ResponseBatchGetUserCommentStyle> batchGetUserCommentStyle(List<Long> userIds) {
        return new Result<>(0, null);
    }

    @Override
    public Result<CommentStyleProto.ResponseBatchGetUserCommentStyleV2> batchGetUserCommentStyleV2(List<Long> userIds) {
        return new Result<>(0, null);
    }
}
