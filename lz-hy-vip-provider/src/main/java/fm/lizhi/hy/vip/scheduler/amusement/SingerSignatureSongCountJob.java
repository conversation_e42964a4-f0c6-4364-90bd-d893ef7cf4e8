package fm.lizhi.hy.vip.scheduler.amusement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import fm.lizhi.hy.vip.manager.sing.SingerIndexManager;
import lombok.extern.slf4j.Slf4j;

/**
 * 歌手-代表作歌曲统计
 */
@Slf4j
@JobHandler("SingerSignatureSongCountJob")
@AutoBindSingleton(baseClass = IJobHandler.class, multiple = true)
public class SingerSignatureSongCountJob extends IJobHandler {

    @Inject
    private SingerIndexManager singerIndexManager;

    @Override
    public ReturnT<String> execute(String param) {
        String resStr = process();
        return new ReturnT<>(resStr);
    }

    private String process() {
        try {
            JSONObject logMap = singerIndexManager.batchBuildSignatureSong();
            return JSON.toJSONString(logMap, true);
        } catch (Exception e) {
            log.error("SingerSignatureSongCountJobProcessError", e);
        }
        return "";
    }

}