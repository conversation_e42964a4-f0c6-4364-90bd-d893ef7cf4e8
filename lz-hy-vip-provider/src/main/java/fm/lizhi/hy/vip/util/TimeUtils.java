package fm.lizhi.hy.vip.util;

import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.Calendar;
import java.util.Date;

@Slf4j
public class TimeUtils {

	public static final long ONE_SECOND_IN_MILLS = 1000L;
	public static final long ONE_MINUTE_IN_MILLS = ONE_SECOND_IN_MILLS * 60;
	public static final long ONE_HOUR_IN_MILLS = ONE_MINUTE_IN_MILLS * 60;
	public static final long ONE_DAY_IN_MILLS = ONE_HOUR_IN_MILLS * 24;

	public static Date parseTime(String timeStr) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			return sdf.parse(timeStr);
		} catch (ParseException e) {
			log.error("parse time error", e);
			return null;
		}
	}

	public static Date parseTime(String timeStr, String pattern) {
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		try {
			return sdf.parse(timeStr);
		} catch (ParseException e) {
			log.error("parse time error", e);
			return null;
		}
	}

	public static String formatTime(Date date, String pattern){
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		return sdf.format(date);
	}

	public static boolean nowIsBetween(Date now, Date startTime, Date endTime) {
		return startTime.before(now) && endTime.after(now);
	}

    //判断选择的日期是否是今天
    public static boolean isToday(long time) {
        return isThisTime(time, "yyyy-MM-dd");
    }

    private static boolean isThisTime(long time,String pattern) {
		Date date = new Date(time);
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		String param = sdf.format(date);//参数时间
		String now = sdf.format(new Date());//当前时间
		if (param.equals(now)) {
			return true;
		}
		return false;
	}

	public static long getNextDay(long time) {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date(time));
        c.add(Calendar.DAY_OF_MONTH, 1);
        return c.getTime().getTime();
    }

	/**
	 * 增加天数
	 * @param date
	 * @param days 天数，负数为减
	 * @return
	 */
	public static Date plusDays(Date date, int days) {
		return new DateTime(date.getTime()).plusDays(days).toDate();
	}

	public static long getTheEndOfTheDay(long time){
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date(time));
		cal.set(Calendar.DAY_OF_MONTH, cal.get(Calendar.DAY_OF_MONTH) + 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTimeInMillis();
	}

	public static boolean isBetween(long starTime, long endTime, long nowTime){
		return nowTime >= starTime && nowTime <= endTime;
	}

	public static long getStartTimeOfNextDay(long time){
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date(time));
		cal.add(Calendar.DAY_OF_MONTH,1);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTime().getTime();
	}

	public static long getFirstDayOfThisWeek(){
		LocalDate date = LocalDate.now();
		LocalDate.Property property = date.dayOfWeek();
		return property.withMinimumValue().toDate().getTime();
	}

	public static long getLastDayOfThisWeek(){
		LocalDate date = LocalDate.now();
		LocalDate.Property property = date.dayOfWeek();
		return property.withMaximumValue().toDate().getTime();
	}

	// 获取指定日期是星期几，返回int，1代表星期一、7代表星期日
	public static int getDayOfWeek(String dateStr) {
		LocalDate date = LocalDate.parse(dateStr);
		return date.getDayOfWeek();
	}

	// 将时间换算成小数，如：12:00 -> 0.5
	public static double getNumberByTime(String timeStr) {
		LocalTime time = LocalTime.parse(timeStr);

		int hour = time.getHour();
		int minute = time.getMinute();
		int second = time.getSecond();

		return (hour * 3600 + minute * 60 + second) / (24.0 * 3600);
	}

	public static void main(String[] args) {
		System.out.println(TimeUtils.formatTime(new Date(), "yyyy_MM_dd_HH_mm"));
	}
}
