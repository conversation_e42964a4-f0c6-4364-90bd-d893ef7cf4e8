package fm.lizhi.hy.vip.dao.amusement;

import java.util.Date;
import javax.persistence.*;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 用户靓号兑换值
 *
 * @date 2023-03-23 02:36:54
 */
@Table(name = "`user_pretty_band_exchange`")
public class UserPrettyBandExchangePO {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 用户拥有的兑换值
     */
    @Column(name= "`total_exchange`")
    private Integer totalExchange;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getTotalExchange() {
        return totalExchange;
    }

    public void setTotalExchange(Integer totalExchange) {
        this.totalExchange = totalExchange;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", totalExchange=").append(totalExchange);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}