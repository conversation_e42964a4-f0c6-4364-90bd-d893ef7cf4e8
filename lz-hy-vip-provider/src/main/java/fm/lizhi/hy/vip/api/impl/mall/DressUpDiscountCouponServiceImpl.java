package fm.lizhi.hy.vip.api.impl.mall;

import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.amusement.api.DressUpDiscountCouponService;
import fm.lizhi.hy.amusement.protocol.DressUpDiscountCouponProto;
import fm.lizhi.hy.vip.entity.DressUpDisCountCoupon;
import fm.lizhi.hy.vip.manager.common.amusement.mall.DressUpDiscountCouponManager;

import java.util.List;

/**
 * @program: lz-hy-vip
 * @description:
 * @author: chenzj
 * @date: 2023-02-22 15:03
 **/
@ServiceProvider
public class DressUpDiscountCouponServiceImpl implements DressUpDiscountCouponService {

    @Inject
    private DressUpDiscountCouponManager dressUpDiscountCouponManager;
    /**
     * 获取用户优惠券
     *
     * @param uid
     * @return
     */
    @Override
    public Result<DressUpDiscountCouponProto.ResponseGetUserDiscountCoupon> getUserDiscountCoupon(long uid) {
        LogContext.addReqLog("getUserDiscountCoupon`userId={}",uid);
        LogContext.addResLog("getUserDiscountCoupon`userId={}",uid);
        if (uid <= 0) {
            return new Result<>(GET_USER_DISCOUNT_COUPON_ILLEGAL_PARAMS, null);
        }
        DressUpDiscountCouponProto.ResponseGetUserDiscountCoupon.Builder builder = DressUpDiscountCouponProto.ResponseGetUserDiscountCoupon.newBuilder();
        List<DressUpDisCountCoupon> userDiscountCoupon = dressUpDiscountCouponManager.getUserDiscountCoupon(uid);
        builder.setDiscountCouponJsonStr(JSONObject.toJSONString(userDiscountCoupon));
        return new Result<>(GET_USER_DISCOUNT_COUPON_SUCCESS, builder.build());
    }

    @Override
    public Result<DressUpDiscountCouponProto.ResponseAwardUserDiscountCoupon> awardUserDiscountCoupon(long userId, String couponName, int couponType, int discountNum, long dressUpInfoId, int minutes, int count, String logDesc) {
        LogContext.addReqLog("awardUserDiscountCoupon`userId={}`couponName={}`couponType={}`discountNum={}`dressUpInfoId={}`minutes={}`count={}", userId, couponName, couponType, discountNum, dressUpInfoId, minutes, count);
        LogContext.addResLog("awardUserDiscountCoupon`userId={}`couponName={}`couponType={}`discountNum={}`dressUpInfoId={}`minutes={}`count={}", userId, couponName, couponType, discountNum, dressUpInfoId, minutes, count);
        dressUpDiscountCouponManager.awardUserDiscountCoupon(userId, couponName, couponType, discountNum, dressUpInfoId, minutes, count, logDesc);
        return new Result<>(0, DressUpDiscountCouponProto.ResponseAwardUserDiscountCoupon.newBuilder().build());
    }
}
