package fm.lizhi.hy.vip.manager;

import com.google.common.base.Optional;
import com.google.protobuf.InvalidProtocolBufferException;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.hy.vip.bo.UserStateBo;
import fm.lizhi.hy.vip.config.VipConfig;
import fm.lizhi.live.data.api.UserBehaviorService;
import fm.lizhi.live.data.protocol.LiveBehaviorProto;
import fm.lizhi.hy.user.account.user.protocol.HyUserBaseProto;
import fm.lizhi.hy.vip.protocol.PpWealthBaseProto;
import fm.lizhi.pp.util.romeupload.RomeUploadImageUtil;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import java.util.List;

@Slf4j
@AutoBindSingleton
public class UserBehaviorManager {

    @Inject
    private VipConfig vipConfig;
    @Inject
    private UserBehaviorService userBehaviorService;
    @Inject
    private PpNewUserManager ppNewUserManager;

    /**
     * 获取用户的状态
     *
     * @param userId 用户ID
     */
    public UserStateBo getUserState(final long userId) {
        // 获取用户行为列表
        LiveBehaviorProto.UserBehaviorsParams.Builder builder = LiveBehaviorProto.UserBehaviorsParams.newBuilder();
        builder.setUserId(userId);
        Result<LiveBehaviorProto.ResponseGetUserBehaviorsNoCache> userBehaviorsNoCacheResult = userBehaviorService.getUserBehaviorsNoCache(builder.build());
        if (null == userBehaviorsNoCacheResult || userBehaviorsNoCacheResult.rCode() != 0) {
            log.error("getUserState fail, getUserBehaviorsNoCache fail, rCode={}, userId={}",
                    null == userBehaviorsNoCacheResult ? "null" : userBehaviorsNoCacheResult.rCode(), userId);
            return null;
        }

        List<LiveBehaviorProto.UserBehavior> behaviorsList = userBehaviorsNoCacheResult.target().getBehaviorsList();
        boolean isOpen = false;
        boolean isListen = false;
        long openLiveId = 0;
        long listenLiveId = 0;
        // 处理单个用户的行为列表
        for (LiveBehaviorProto.UserBehavior userBehavior : behaviorsList) {
            LiveBehaviorProto.BehaviorType type = userBehavior.getType();
            if (type == LiveBehaviorProto.BehaviorType.LIVE_OPEN) {
                LiveBehaviorProto.LiveListenBehaviorData behaviorData = null;
                try {
                    behaviorData = LiveBehaviorProto.LiveListenBehaviorData.parseFrom(userBehavior.getData().toByteArray());
                } catch (InvalidProtocolBufferException e) {
                    e.printStackTrace();
                }
                isOpen = true;
                if (behaviorData != null) {
                    openLiveId = behaviorData.getLiveId();
                }
            } else if (type == LiveBehaviorProto.BehaviorType.LIVE_LISTEN) {
                LiveBehaviorProto.LiveListenBehaviorData behaviorData = null;
                try {
                    behaviorData = LiveBehaviorProto.LiveListenBehaviorData.parseFrom(userBehavior.getData().toByteArray());
                } catch (InvalidProtocolBufferException e) {
                    e.printStackTrace();
                }
                isListen = true;
                if (behaviorData != null) {
                    listenLiveId = behaviorData.getLiveId();
                }
            }
        }

        UserStateBo userStateBo = new UserStateBo();
        if (isOpen) {
            userStateBo.setLiveId(openLiveId);
            userStateBo.setUserStateEnum(PpWealthBaseProto.UserStateEnum.OPEN);
        } else if (isListen) {
            userStateBo.setLiveId(listenLiveId);
            userStateBo.setUserStateEnum(PpWealthBaseProto.UserStateEnum.LISTEN);
        }

        // 填充用户信息
        Optional<HyUserBaseProto.User> optional = ppNewUserManager.getUser(userId);
        if (!optional.isPresent()) {
            log.error("getUserState fail, getUser fail, followUserId={}", userId);
            return null;
        }
        HyUserBaseProto.User user = optional.get();
        userStateBo.setUserId(userId);
        userStateBo.setName(user.getName());
        userStateBo.setPortrait(RomeUploadImageUtil.portraitOriginUrl(user.getPortrait()));

        return userStateBo;
    }


    public Long getLiveIdIfInRoom(Long uid) {
        try {
            LiveBehaviorProto.ResponseGetUserBehaviors behaviors = getUserBehaviors(uid);
            if (behaviors == null) {
                return null;
            }
            for (LiveBehaviorProto.UserBehavior userBehavior : behaviors.getBehaviorsList()) {
                int typeNum = userBehavior.getType().getNumber();
                if (typeNum == LiveBehaviorProto.BehaviorType.LIVE_LISTEN_VALUE) {
                    LiveBehaviorProto.LiveListenBehaviorData liveListenBehaviorData = LiveBehaviorProto.LiveListenBehaviorData.parseFrom(userBehavior.getData());
                    return liveListenBehaviorData.getLiveId();
                } else if (typeNum == LiveBehaviorProto.BehaviorType.LIVE_OPEN_VALUE) {
                    LiveBehaviorProto.LiveOpenBehaviorData liveOpenBehaviorData = LiveBehaviorProto.LiveOpenBehaviorData.parseFrom(userBehavior.getData());
                    return liveOpenBehaviorData.getLiveId();
                }
            }
            return null;
        } catch (Exception e) {
            log.warn("getLiveIdIfInRoom Error={},uid={}", e, uid);
        }
        return null;
    }

    /**
     * 查询用户行为状态。
     */
    private LiveBehaviorProto.ResponseGetUserBehaviors getUserBehaviors(Long uid) {
        Result<LiveBehaviorProto.ResponseGetUserBehaviors> result = userBehaviorService.getUserBehaviors(LiveBehaviorProto.UserBehaviorsParams.newBuilder().setUserId(uid).build());
        int rCode = result.rCode();
        if (rCode == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return result.target();
        }
        log.warn("getBatchUserBehaviorsRCode={}, uid={}", rCode, uid);
        return null;
    }

}