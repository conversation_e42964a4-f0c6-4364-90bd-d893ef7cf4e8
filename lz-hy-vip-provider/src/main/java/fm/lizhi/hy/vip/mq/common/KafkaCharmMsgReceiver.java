package fm.lizhi.hy.vip.mq.common;

import com.alibaba.fastjson.JSON;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.queue.callback.MsgFuture;
import fm.lizhi.commons.queue.service.Consumer;
import fm.lizhi.hy.vip.config.LivePpCommonConfig;
import fm.lizhi.hy.vip.manager.PpNewUserManager;
import fm.lizhi.hy.vip.manager.common.AsyncKafkaExecutor;
import fm.lizhi.hy.vip.manager.common.info.GiftPackMsg;
import fm.lizhi.pp.util.utils.ConfigUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.inject.Inject;

/**
 * 充值消费kafka
 *
 * <AUTHOR>
 * @date 2019-11-22
 */
@Slf4j
@AutoBindSingleton
public class KafkaCharmMsgReceiver implements BaseMessageReceiver {
    @Inject
    private LivePpCommonConfig ppCommonConfig;

    @Inject
    private AsyncKafkaExecutor asyncKafkaExecutor;

    @Inject
    private PpNewUserManager userManager;

    @Override
    public boolean isProduct() {
        return ppCommonConfig.isProduct();
    }

    @Override
    public String getKafkaKey() {
        return ppCommonConfig.getKafkaKey();
    }

    @Override
    public String getTopic() {
        return ppCommonConfig.getKafkaTopics();
    }

    @Override
    public String getGroupId() {
        return ppCommonConfig.getConsumeGroupId();
    }

    @Override
    @PostConstruct
    public void init() {
        boolean isPropProduct = isProduct();
        String kafkaKey = getKafkaKey();
        String groupId = getGroupId();
        String topic = getTopic();
        log.info("isProduct={}, kafkaKey={}, groupId={}, topic={}", isPropProduct, kafkaKey, groupId, topic);

        try {

            new Consumer(isPropProduct, kafkaKey, groupId, topic, DEFAULT_THREAD_COUNT, DEFAULT_THREAD_COUNT,
                    new MsgFuture() {
                        @Override
                        public boolean msgReceived(String key, String msg, long timestamp, int partition, long offset) {
                            try {
                                return handleMessage(msg);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            return true;
                        }
                    }).start();

        } catch (Exception e) {
            log.error("init error", e);
        }
    }

    @Override
    public boolean handleMessage(@NonNull String msg) {
        GiftPackMsg giftPackMsg = JSON.parseObject(msg, GiftPackMsg.class);
        if (null == giftPackMsg) {
            log.warn("gift msg format error, gift msg={}", msg);
            return true;
        }
        // 非PP
        if (giftPackMsg.getAppId() != ppCommonConfig.getPpywAppId()) {
            return true;
        }

        if (ConfigUtil.isOpenPpSepTest()
                && !ConfigUtil.isTestWhiteUser(giftPackMsg.getUserId())) {
            //开启白名单测试功能，不在白名单内的过滤
            log.info("PayCompleteProcessor not pp test users msg={}", msg);
            return true;
        }

        //异步处理消费情况
        asyncKafkaExecutor.buyGiftPackEvent(giftPackMsg);
        // 潜力标识
        if(ppCommonConfig.buyGiftBugPotentialMark) {
            userManager.markUserPotential(giftPackMsg.getUserId());
        }

        return true;
    }
}
