package fm.lizhi.hy.vip.api.impl;

import com.google.inject.Inject;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.vip.adapter.HatAdapter;
import fm.lizhi.hy.vip.api.HatService;
import fm.lizhi.hy.vip.entity.AvatarFrame;
import fm.lizhi.hy.vip.entity.HatInfo;
import fm.lizhi.hy.vip.entity.HatSeries;
import fm.lizhi.hy.vip.manager.blindDate.HatInfoManager;
import fm.lizhi.hy.vip.manager.blindDate.HatSeriesManager;
import fm.lizhi.hy.vip.manager.blindDate.HatUserInfoManager;
import fm.lizhi.hy.vip.manager.common.amusement.mall.DressUpInfoManager;
import fm.lizhi.hy.vip.protocol.HatBaseProto;
import fm.lizhi.hy.vip.protocol.HatProto;
import fm.lizhi.hy.vip.protocol.HatRequestProto;

import java.util.ArrayList;
import java.util.List;

/**
 * HatServiceImpl
 *
 * <AUTHOR>
 * @date 2023/3/18
 */
@ServiceProvider
public class HatServiceImpl implements HatService {

    @Inject
    private HatAdapter hatAdapter;
    @Inject
    private HatSeriesManager hatSeriesManager;
    @Inject
    private HatInfoManager hatInfoManager;
    @Inject
    private HatUserInfoManager hatUserInfoManager;
    @Inject
    private DressUpInfoManager dressUpInfoManager;

//    private final LoadingCache<Long,List<HatInfo>> hatInfoListCache = CacheBuilder.newBuilder()
//            .maximumSize(1000)
//            .expireAfterWrite(10, TimeUnit.MINUTES)
//            .refreshAfterWrite(5, TimeUnit.MINUTES)
//            .build(new CacheLoader<Long, List<HatInfo>>() {
//                @Override
//                public List<HatInfo> load(Long hatSeriesId) {
//                    return hatInfoManager.getHatInfoList(hatSeriesId);
//                }
//            });



    @Override
    public Result<HatProto.ResponseCreateHatSeries> createHatSeries(HatRequestProto.CreateHatSeriesRequest createHatSeriesRequest) {
        HatProto.ResponseCreateHatSeries.Builder builder = HatProto.ResponseCreateHatSeries.newBuilder();

        HatBaseProto.HatSeries hatSeriesProto = createHatSeriesRequest.getHatSeries();

        // 判断系列名字是否重复
        List<String> nameList = hatSeriesManager.getHatSeriesNameList();
        if (nameList.contains(hatSeriesProto.getHatSeriesName())) {
            return new Result<>(CREATE_HAT_SERIES_FAIL, builder.setRes(Boolean.FALSE).setMessage("该系列名字已存在").build());
        }
        HatSeries hatSeries = hatAdapter.convertHatSeriesProto2Bean(hatSeriesProto);
        int result = hatSeriesManager.createHatSeries(hatSeries);
        if (result > 0) {
            return new Result<>(CREATE_HAT_SERIES_SUCCESS, builder.setRes(Boolean.TRUE).build());
        }else {
            return new Result<>(CREATE_HAT_SERIES_FAIL, builder.setRes(Boolean.FALSE).build());
        }
    }

    @Override
    public Result<HatProto.ResponseUpdateHatSeries> updateHatSeries(HatRequestProto.UpdateHatSeriesRequest updateHatSeriesRequest) {
        HatProto.ResponseUpdateHatSeries.Builder builder = HatProto.ResponseUpdateHatSeries.newBuilder();

        HatBaseProto.HatSeries hatSeriesProto = updateHatSeriesRequest.getHatSeries();

        // 判断系列id是否存在
        List<Long> hatSeriesIdList = hatSeriesManager.getHatSeriesIdList();
        if(!hatSeriesIdList.contains(hatSeriesProto.getId())) {
            return new Result<>(UPDATE_HAT_SERIES_FAIL, builder.setRes(Boolean.FALSE).setMessage("该系列id不存在").build());
        }

        // 判断系列名字是否重复
        List<String> nameList = hatSeriesManager.getHatSeriesNameList();
        String currentName = hatSeriesManager.getHatSeriesNameById(hatSeriesProto.getId());
        if (!currentName.equals(hatSeriesProto.getHatSeriesName()) && nameList.contains(hatSeriesProto.getHatSeriesName())) {
            return new Result<>(CREATE_HAT_SERIES_FAIL, builder.setRes(Boolean.FALSE).setMessage("该系列名字已存在").build());
        }
        HatSeries hatSeries = hatAdapter.convertHatSeriesProto2Bean(hatSeriesProto);
        int result = hatSeriesManager.updateHatSeries(hatSeries);
        if (result > 0) {
            return new Result<>(UPDATE_HAT_SERIES_SUCCESS, builder.setRes(Boolean.TRUE).build());
        }else {
            return new Result<>(UPDATE_HAT_SERIES_FAIL, builder.setRes(Boolean.FALSE).build());
        }
    }

    @Override
    public Result<HatProto.ResponseGetHatSeries> getHatSeries(HatRequestProto.GetHatSeriesRequest getHatSeriesRequest) {
        HatProto.ResponseGetHatSeries.Builder builder = HatProto.ResponseGetHatSeries.newBuilder();
        Integer pageNo = getHatSeriesRequest.getPageNo();
        Integer pageSize = getHatSeriesRequest.getPageSize();
        Long id = getHatSeriesRequest.getId();
        String hatSeriesName = getHatSeriesRequest.getHatSeriesName();
        List<HatSeries> hatSeriesList = hatSeriesManager.getHatSeries(pageNo, pageSize, id, hatSeriesName);
        Integer total = hatSeriesManager.getHatSeriesTotal();
        builder.setTotal(total);
        if (hatSeriesList != null && !hatSeriesList.isEmpty()) {
            for (HatSeries hatSeries : hatSeriesList) {
                HatBaseProto.HatSeries hatSeriesProto = hatAdapter.convertHatSeriesBean2Proto(hatSeries);
                builder.addHatSeries(hatSeriesProto);
            }
            return new Result<>(GET_HAT_SERIES_SUCCESS, builder.build());
        }else {
            return new Result<>(GET_HAT_SERIES_FAIL, builder.build());
        }
    }

    @Override
    public Result<HatProto.ResponseDeleteHatSeries> deleteHatSeries(HatRequestProto.DeleteHatSeriesRequest deleteHatSeriesRequest) {
        HatProto.ResponseDeleteHatSeries.Builder builder = HatProto.ResponseDeleteHatSeries.newBuilder();
        long id = deleteHatSeriesRequest.getId();
        // 判断系列id是否存在
        List<Long> hatSeriesIdList = hatSeriesManager.getHatSeriesIdList();
        if(!hatSeriesIdList.contains(id)) {
            return new Result<>(DELETE_HAT_SERIES_FAIL, builder.setRes(Boolean.FALSE).setMessage("该系列id不存在").build());
        }
        int result = hatSeriesManager.deleteHatSeries(id);
        if (result > 0) {
            return new Result<>(DELETE_HAT_SERIES_SUCCESS, builder.setRes(Boolean.TRUE).build());
        }else {
            return new Result<>(DELETE_HAT_SERIES_FAIL, builder.setRes(Boolean.FALSE).build());
        }
    }

    @Override
    public Result<HatProto.ResponseGetHatInfo> getHatInfo(HatRequestProto.GetHatInfoRequest getHatInfoRequest) {
        HatProto.ResponseGetHatInfo.Builder builder = HatProto.ResponseGetHatInfo.newBuilder();
        Long hatSeriesId = getHatInfoRequest.getHatSeriesId();
        // 判断系列id是否存在
        List<Long> hatSeriesIdList = hatSeriesManager.getHatSeriesIdList();
        if(!hatSeriesIdList.contains(hatSeriesId)) {
            return new Result<>(GET_HAT_INFO_ILLEGAL_PARAMS, builder.build());
        }
        List<HatInfo> hatInfoList = hatInfoManager.getHatInfoList(hatSeriesId);
        if (hatInfoList != null && !hatInfoList.isEmpty()) {
            for (HatInfo hatInfo : hatInfoList) {
                HatBaseProto.HatInfo hatInfoProto = hatAdapter.convertHatInfoBean2Proto(hatInfo);
                builder.addHatInfo(hatInfoProto);
            }
        }
        return new Result<>(GET_HAT_INFO_SUCCESS, builder.build());
    }

    @Override
    public Result<HatProto.ResponseSaveHatInfo> saveHatInfo(HatRequestProto.SaveHatInfoRequest saveHatInfoRequest) {
        List<HatBaseProto.HatInfo> hatInfoProtoList = saveHatInfoRequest.getHatInfoList();
        Long hatSeriesId = saveHatInfoRequest.getHatSeriesId();
        List<HatInfo> hatInfoList = new ArrayList<>();
        for (HatBaseProto.HatInfo hatInfoProto : hatInfoProtoList) {
            HatInfo hatInfo = hatAdapter.convertHatInfoProto2Bean(hatInfoProto);
            hatInfoList.add(hatInfo);
        }
        HatProto.ResponseSaveHatInfo result = hatInfoManager.saveHatInfo(hatInfoList,hatSeriesId);
        HatProto.ResponseSaveHatInfo.Builder builder = HatProto.ResponseSaveHatInfo.newBuilder();
        builder.setRes(true);
        builder.setMessage(result.getMessage());
        if (result.getRes()) {
//            hatInfoListCache.refresh(hatSeriesId);
            return new Result<>(SAVE_HAT_INFO_SUCCESS, builder.build());
        }else {
            return new Result<>(SAVE_HAT_INFO_FAIL, builder.build());
        }
    }

    @Override
    public Result<HatProto.ResponseGetAvatarFrame> getAvatarFrame(HatRequestProto.GetAvatarFrameRequest getAvatarFrameRequest) {
        HatProto.ResponseGetAvatarFrame.Builder builder = HatProto.ResponseGetAvatarFrame.newBuilder();
        Long itemId = getAvatarFrameRequest.getItemId();
        String itemName = getAvatarFrameRequest.getItemName();
        List<AvatarFrame> avatarFrameList = dressUpInfoManager.getAvatarFrameList(itemId, itemName);
        if (avatarFrameList != null && !avatarFrameList.isEmpty()) {
            for (AvatarFrame avatarFrame : avatarFrameList) {
                HatBaseProto.AvatarFrame avatarFrameProto = hatAdapter.convertAvatarFrameBean2Proto(avatarFrame);
                builder.addAvatarFrame(avatarFrameProto);
            }
            return new Result<>(GET_AVATAR_FRAME_SUCCESS, builder.build());
        }else {
            return new Result<>(GET_AVATAR_FRAME_FAIL, builder.build());
        }
    }

    /**
     * 保存用户帽子信息
     * @param request
     * @return
     */
    @Override
    public Result<HatProto.ResponseSaveHatUserInfo> saveHatUserInfo(HatRequestProto.SaveHatUserInfoRequest request) {
        return hatUserInfoManager.saveHatUserInfo(request);
    }

    /**
     * 获取用户性别对应魅力值门口的帽子信息
     * @param request
     * @return
     */
    @Override
    public Result<HatProto.ResponseGetHatUserInfo> getHatUserInfo(HatRequestProto.GetHatUserInfoRequest request) {
        return hatUserInfoManager.getHatUserInfo(request);
    }
}
