package fm.lizhi.hy.vip.manager.trade;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.hy.vip.bean.trade.RechargeCompleteEvent;
import fm.lizhi.hy.vip.mq.trade.producer.UserBehaviorEventProducer;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户行为收集。
 *
 * <AUTHOR>
 * @since 2020-07-17
 */
@Slf4j
@AutoBindSingleton
public class UserBehaviorEventManager {

    @Inject
    private UserBehaviorEventProducer behaviorEventProducer;

    public void sendRechargeComplete(final RechargeCompleteEvent rechargeEvent) {
        Long uid = rechargeEvent.getUserId();
        Long amount = rechargeEvent.getOrderAmount();
        String currencyType = rechargeEvent.getRechargeCurrencyType();
        Map<String, Object> map = new HashMap<>();
        map.put("amount", amount);
        map.put("currencyType", currencyType);
        behaviorEventProducer.send(uid, "USER_RECHARGE", map);
    }
}
