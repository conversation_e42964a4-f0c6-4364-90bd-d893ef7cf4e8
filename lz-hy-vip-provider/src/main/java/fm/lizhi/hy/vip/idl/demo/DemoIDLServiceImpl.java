package fm.lizhi.hy.vip.idl.demo;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.vip.demo.api.DemoIDLService;
import fm.lizhi.hy.vip.demo.dto.request.DemoReq;
import fm.lizhi.hy.vip.demo.dto.response.DemoResp;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ServiceProvider
public class DemoIDLServiceImpl implements DemoIDLService {

    @Override
    public Result<DemoResp> getDemo(DemoReq req) {
        log.info("getDemo req={}", req.getUserId());
        DemoResp demoResp = new DemoResp();
        demoResp.setUserId(1L);
        return new Result<>(0, demoResp);
    }
}