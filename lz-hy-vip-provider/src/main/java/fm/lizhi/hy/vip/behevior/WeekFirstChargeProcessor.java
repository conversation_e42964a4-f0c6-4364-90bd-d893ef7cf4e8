package fm.lizhi.hy.vip.behevior;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.hy.vip.bo.operating.TaskConditionBean;
import fm.lizhi.hy.vip.log.LizhiLogger;
import fm.lizhi.hy.vip.manager.operating.UserChargeManager;
import fm.lizhi.hy.vip.util.TimeUtils;

import java.util.Map;

/**
 * 充值-周首充
 */
@AutoBindSingleton
public class WeekFirstChargeProcessor implements ConditionProcessor<Object> {

    @Inject
    private UserChargeManager userChargeManager;

    @Override
    public boolean processed(long userId, TaskConditionBean conditionBean, Object object) {
        long firstDayOfWeekTime = TimeUtils.getFirstDayOfThisWeek();
        long lastDayOfWeekTime = TimeUtils.getLastDayOfThisWeek();
        long time = userChargeManager.getFirstChargeTime(userId, firstDayOfWeekTime, lastDayOfWeekTime, 4);
        LizhiLogger.info("WeekFirstChargeProcessor processed time={}`userId={}`conditionBean={}", time, userId, conditionBean.toString());
        if(time == 0){
            // 历史首充包含周首充
            return true;
        }
        return false;
    }

    @Override
    public Map<String, Object> processedMap(long userId, TaskConditionBean conditionBean, Object o) {
        return null;
    }

}
