package fm.lizhi.hy.vip.mq.amusement;

import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.queue.callback.MsgFuture;
import fm.lizhi.hy.vip.dao.amusement.DressUpInfoPO;
import fm.lizhi.hy.vip.dao.amusement.DressUpMallPO;
import fm.lizhi.hy.vip.manager.common.amusement.mall.DressUpGoodsManager;
import fm.lizhi.hy.vip.manager.common.amusement.mall.DressUpInfoManager;
import fm.lizhi.hy.vip.manager.common.amusement.mall.DressUpTreasureManager;
import fm.lizhi.hy.vip.manager.common.info.DressUpGoodsKafkaMsg;
import fm.lizhi.hy.vip.redis.amusement.DressUpRedisKey;
import fm.lizhi.hy.vip.redis.amusement.LiveAmusementRedis;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 消费MQ 处理珍宝积分
 */
@Slf4j
@AutoBindSingleton
public class DressUpGoodsRankProcessor implements MsgFuture {

    @Inject
    private DressUpTreasureManager dressUpTreasureManager;
    @Inject
    private DressUpInfoManager dressUpInfoManager;
    @Inject
    private DressUpGoodsManager dressUpGoodsManager;
    @Inject
    private LiveAmusementRedis redis;

    @Override
    public boolean msgReceived(String key, String msg, long timestamp, int partition, long offset) {
        if (StringUtils.isBlank(msg)) {
            return true;
        }
        log.info("DressUpGoodsRankProcessor={}", msg);
        try {
            DressUpGoodsKafkaMsg dressUpGoodsKafkaMsg = JSONObject.parseObject(msg, DressUpGoodsKafkaMsg.class);
            long orderId = dressUpGoodsKafkaMsg.getOrderId();
            long userId = dressUpGoodsKafkaMsg.getUserId();
            long dressUpId = dressUpGoodsKafkaMsg.getDressUpId();
            long orderTime = dressUpGoodsKafkaMsg.getOrderTime();
            int payActQuantity = dressUpGoodsKafkaMsg.getPayActQuantity();
            if (orderId < 1 || userId < 1 || dressUpId < 1 || orderTime < 1|| payActQuantity < 0) {
                log.info("DressUpGoodsRankProcessor data error msg={}", msg);
                return true;
            }
            String lockKey = DressUpRedisKey.USER_TREASURE_RANK_MQ_LOCK.getKey(orderId);
            //一分钟内幂等
            long oneMintue = 60 * 1000;
            if (!redis.lock(lockKey, oneMintue)) {
                log.info("DressUpGoodsRankProcessor getLockFail msg={}", msg);
                return true;
            }
            DressUpInfoPO dressUpInfoById = dressUpInfoManager.getDressUpInfoById(dressUpId);
            if(Objects.isNull(dressUpInfoById)){
                log.info("DressUpGoodsRankProcessor get dressUpInfo is null  msg={}", msg);
                return true;
            }
            DressUpMallPO dressUpMallPO = dressUpGoodsManager.getMallDressByDressUpId(dressUpId);
            if(Objects.isNull(dressUpMallPO)){
                log.info("DressUpGoodsRankProcessor get DressUpMallPO is null  msg={}", msg);
                return true;
            }
            Integer treasureShow = dressUpMallPO.getTreasureShow();
            if(treasureShow != 1){
                log.info("DressUpGoodsRankProcessor treasureShow not  1 is null  msg={}", msg);
                return true;
            }
            dressUpTreasureManager.addUserTreasureScore(userId, dressUpId, orderTime, payActQuantity);
            log.info("DressUpGoodsRankProcessor add  userScore userId={},score:{},dressUpId:{}", userId, payActQuantity, dressUpId);
        } catch (Exception e) {
            log.warn("dressUpGoodsOrderFinError", e);
        }
        return true;
    }


}
