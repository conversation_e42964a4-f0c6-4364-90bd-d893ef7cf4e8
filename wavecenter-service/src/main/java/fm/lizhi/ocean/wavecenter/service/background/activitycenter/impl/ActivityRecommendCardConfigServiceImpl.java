package fm.lizhi.ocean.wavecenter.service.background.activitycenter.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityRecommendCardConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestSaveActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestUpdateActivityRecommendCard;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityRecommendCardConfigService;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRecommendCardManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 推荐卡
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class ActivityRecommendCardConfigServiceImpl implements ActivityRecommendCardConfigService {

    @Autowired
    private ActivityRecommendCardManager activityRecommendCardManager;


    @Override
    public Result<Void> saveRecommendCard(RequestSaveActivityRecommendCard param) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(param));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(param));

        return ResultHandler.handle(param.getAppId(), () -> activityRecommendCardManager.saveRecommendCard(param));
    }

    @Override
    public Result<Void> updateRecommendCard(RequestUpdateActivityRecommendCard param) {
        LogContext.addReqLog("paramBean={}", JsonUtil.dumps(param));
        LogContext.addResLog("paramBean={}", JsonUtil.dumps(param));
        return ResultHandler.handle(param.getAppId(), () -> activityRecommendCardManager.updateRecommendCard(param));
    }

    @Override
    public Result<Void> deleteRecommendCard(Long id, Integer appId, String operator) {
        LogContext.addReqLog("appId={}, id={}, operator={}", appId, id, operator);
        LogContext.addResLog("appId={}, id={}, operator={}", appId, id, operator);

        return ResultHandler.handle(appId, () -> activityRecommendCardManager.deleteRecommendCard(id, operator));
    }

    @Override
    public Result<List<ActivityRecommendCardConfigBean>> listByAppId(Integer appId) {
        LogContext.addReqLog("appId={}", appId);
        LogContext.addResLog("appId={}", appId);
        return ResultHandler.handle(appId, () -> activityRecommendCardManager.listByAppId(appId));
    }
}
