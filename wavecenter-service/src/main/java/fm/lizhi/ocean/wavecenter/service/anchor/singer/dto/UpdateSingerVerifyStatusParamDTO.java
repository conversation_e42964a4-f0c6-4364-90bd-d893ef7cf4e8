package fm.lizhi.ocean.wavecenter.service.anchor.singer.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UpdateSingerVerifyStatusParamDTO {

    private Long id;

    private Integer currentAuditStatus;

    private Integer targetAuditStatus;

    private Integer targetSingerStatus;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 审核不通过原因
     */
    private String rejectReason;

    /**
     * 预审核不过原因
     */
    private String preAuditRejectReason;

    /**
     * 是否需要更新歌手信息
     */
    private boolean needUpdateSingerInfo;

    /**
     * 通过的歌手类型
     */
    private Integer passSingerType;

}
