package fm.lizhi.ocean.wavecenter.service.live.manager;

import fm.lizhi.ocean.wavecenter.api.live.request.*;
import fm.lizhi.ocean.wavecenter.api.live.response.*;

import java.util.Date;
import java.util.List;

/**
 * 麦序福利数据管理
 */
public interface WaveCheckInDataManager {

    /**
     * 获取麦序福利厅汇总
     *
     * @param req 请求
     * @return 麦序福利厅汇总
     */
    ResponseGetCheckInRoomSum getCheckInRoomSum(RequestGetCheckInRoomSum req);

    /**
     * 获取麦序福利厅明细统计
     *
     * @param req 请求
     * @return 麦序福利厅明细统计
     */
    ResponseGetCheckInRoomStatistic getCheckInRoomStatistic(RequestGetCheckInRoomStatistic req);

    /**
     * 获取麦序福利主播汇总数据
     *
     * @param req 请求
     * @return 麦序福利主播汇总数据
     */
    ResponseGetCheckInPlayerSum getCheckInPlayerSum(RequestGetCheckInPlayerSum req);

    /**
     * 获取麦序福利主播明细统计
     *
     * @param req 请求
     * @return 麦序福利主播明细统计
     */
    ResponseGetCheckInPlayerStatistic getCheckInPlayerStatistic(RequestGetCheckInPlayerStatistic req);


    /**
     * 获取指定时间的主持人信息
     */
    List<Long> getHostUserIds(Integer appId, Long njId, Date startTime, Date endTime);

    /**
     * 获取厅打卡管理员
     */
    List<Long> getManagerIds(Integer appId, Long njId);

    /**
     * 获取打卡报告
     * @param req
     * @return
     */
    ResponseGetCheckInRoomStatisticReport getCheckInRoomStatisticReport(RequestGetCheckInRoomStatisticReport req);
}
