package fm.lizhi.ocean.wavecenter.service.message.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.message.request.*;
import fm.lizhi.ocean.wavecenter.api.message.response.ResponseGetMessageList;
import fm.lizhi.ocean.wavecenter.api.message.service.WaveCenterMessageService;
import fm.lizhi.ocean.wavecenter.service.message.manager.WaveCenterMessageManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class WaveCenterMessageServiceImpl implements WaveCenterMessageService {

    @Autowired
    private WaveCenterMessageManager waveCenterMessageManager;



    @Override
    public Result<Long> sendMessage(RequestSendMessage param) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, waveCenterMessageManager.sendMessage(param));
    }

    @Override
    public Result<List<Long>> sendMessageBatch(RequestSendMessageBatch param) {
        return waveCenterMessageManager.sendMessageBatch(param);
    }

    @Override
    public Result<List<Long>> sendMessage2Role(RequestSendMessageToRole param) {
        return waveCenterMessageManager.sendMessage2Role(param);
    }

    @Override
    public Result<ResponseGetMessageList> getMessageList(RequestGetMessageList param) {
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, waveCenterMessageManager.getMessageList(param));
    }

    @Override
    public Result<Void> batchRead(RequestBatchReadMessage param) {
        waveCenterMessageManager.batchRead(param);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }
}
