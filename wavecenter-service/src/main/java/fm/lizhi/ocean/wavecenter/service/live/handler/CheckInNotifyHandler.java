package fm.lizhi.ocean.wavecenter.service.live.handler;

import cn.hutool.crypto.SignUtil;
import com.google.common.collect.Sets;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.live.constants.CheckInDateTypeEnum;
import fm.lizhi.ocean.wavecenter.api.permissions.constants.RoleEnum;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.GetRoomInfoByNjIdDTO;
import fm.lizhi.ocean.wavecenter.service.common.manager.ChatManager;
import fm.lizhi.ocean.wavecenter.service.datacenter.dto.GetHasIncomeRoomsParam;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.RoomDataManager;
import fm.lizhi.ocean.wavecenter.service.live.config.LiveConfig;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveManager;
import fm.lizhi.ocean.wavecenter.service.live.manager.WaveCheckInDataManager;
import fm.lizhi.ocean.wavecenter.service.permissions.manager.RoleManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class CheckInNotifyHandler {

    @Autowired
    private RoomDataManager roomDataManager;
    @Autowired
    private ChatManager chatManager;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private WaveCheckInDataManager waveCheckInDataManager;
    @Autowired
    private LiveConfig liveConfig;
    @Autowired
    private List<CheckInRoomPeriodStatHandler> checkInRoomPeriodStatHandlers;
    @Autowired
    private LiveManager liveManager;


    private static final List<CheckInDateTypeEnum> SUPPORT_DATE_TYPES =
            Arrays.asList(CheckInDateTypeEnum.HOUR, CheckInDateTypeEnum.DAY, CheckInDateTypeEnum.WEEK);

    /**
     * 执行通知
     * @param appId
     */
    public void executeNotify(Integer appId){
        log.info("start executeNotify;appId={}", appId);
        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.from(appId));
        // 获取指定厅
        Integer days = liveConfig.getCheckInRoomRecentRevenueDays();
        Date now = new Date();
        GetHasIncomeRoomsParam incomeRoomsParam = new GetHasIncomeRoomsParam()
                        .setAppId(appId)
                        .setStartDay(MyDateUtil.getOffsetDayStartBefore(now, days))
                        .setEndDay(MyDateUtil.getBeginOfDay(now));
        List<Long> hasIncomeRoomIdList = roomDataManager.getHasIncomeRoomIdList(incomeRoomsParam);
        log.info("executeNotify;appId={};hasIncomeRoomIdList.size={}", appId, hasIncomeRoomIdList.size());
        log.debug("executeNotify;appId={};hasIncomeRoomIdList={}", appId, hasIncomeRoomIdList);
        long sender = liveConfig.getBizConfig().getCheckInReportSender();
        if (sender <= 0) {
            log.error("sendReportDataMessageByAppId fail;sender is null");
            return;
        }
        int totalReceiverId = 0;
        //每个厅发送报告私信
        for (Long njId : hasIncomeRoomIdList) {
            totalReceiverId += sendReportDataMessageByRoom(appId, sender, njId, now);
        }
        log.info("sendReportDataMessageByAppId finish;appId={};totalRoom={};totalReceiverId={}",
                appId, hasIncomeRoomIdList.size(), totalReceiverId);
    }

    /**
     * 每个厅发送发送小时、日、周打卡报告私信、结束语
     */
    private int sendReportDataMessageByRoom(Integer appId, Long senderId, Long njId, Date now) {
        Result<GetRoomInfoByNjIdDTO> roomInfoByNjId = liveManager.getRoomInfoByNjId(njId);
        if(roomInfoByNjId.rCode() != 0) {
            log.error("sendReportDataMessageByRoom;roomId no found;appId={};njId={}", appId, njId);
            return 0;
        }
        Long roomId = roomInfoByNjId.target().getId();
        //获取所有报告都需要通知的基本人群
        Set<Long> baseReceiverIds = getBaseReceiverIds(appId, njId);
        //收集被通知的所有人
        Set<Long> collectAllReceiver = new HashSet<>(baseReceiverIds);
        //增加额外指定人群，并进行个性化的报告
        for (CheckInDateTypeEnum dateTypeEnum : SUPPORT_DATE_TYPES) {
            CheckInRoomPeriodStatHandler handler = getStatHandler(dateTypeEnum);
            if(handler == null) {
                continue;
            }
            if(!handler.isTimeToReport(now)) {
                log.debug("is not time to report;time={};dateType={}", now, dateTypeEnum);
                continue;
            }
            //构建通知内容
            String content = handler.buildContent(appId, njId, roomId, now);
            //通知基础人群
            chatManager.batchSendChatAsync(senderId, baseReceiverIds, content);

            //获取额外指定人群
            List<Long> otherReceivers = handler.populateExtraReceiver(appId, njId, now);
            //额外通知的指定人群
            chatManager.batchSendChatAsync(senderId, otherReceivers, content);
            //收集额外指定人群
            collectAllReceiver.addAll(otherReceivers);
        }
        //所有报告都通知了，整句结束语
        chatManager.batchSendChatAsync(senderId, collectAllReceiver, String.format(liveConfig.getCheckInReportEndMsgModel(), liveConfig.getBizConfig().getCheckInDetailUrl()));
        log.info("sendReportDataMessageByRoom;njId={};triggerTime={};sendSize={}", njId, now, collectAllReceiver.size());
        return collectAllReceiver.size();
    }


    /**
     * 获取支持的处理器
     * @param dateTypeEnum
     * @return
     */
    private CheckInRoomPeriodStatHandler getStatHandler(CheckInDateTypeEnum dateTypeEnum) {
        for (CheckInRoomPeriodStatHandler handler : checkInRoomPeriodStatHandlers) {
            if(handler.support(dateTypeEnum)) {
                return handler;
            }
        }
        log.error("type is not support;dateType={}", dateTypeEnum);
        return null;
    }

    /**
     * 获取指定厅的私信接受主播列表
     */
    private Set<Long> getBaseReceiverIds(Integer appId, Long njId) {
        //厅主也要通知
        Set<Long> result = Sets.newHashSet(njId);
        //打卡管理员
        List<Long> managerIds = waveCheckInDataManager.getManagerIds(appId, njId);
        result.addAll(managerIds);
        //厅管理员
        List<Long> roomRoleAuthUserIds = roleManager.getRoomRoleAuthUserIds(appId, njId, RoleEnum.ROOM);
        result.addAll(roomRoleAuthUserIds);
        log.info("getRoomCheckInReceiverIds;appId={};njId={};managerIds.size={};total={}",
                appId, njId, managerIds.size(), result.size());
        log.debug("getRoomCheckInReceiverIds;appId={};njId={};managerIds={};total={}",
                appId, njId, managerIds, result);
        return result;
    }


    public static void main(String[] args) {
        Map<String, String> params = new HashMap<>();
        params.put("dateType", "HOUR");
        params.put("roomId", "5248023412317627958");
        params.put("startDate", "1749718800000");
        params.put("endDate", "1749722399999");
        params.put("appId", "9637128");
        String signCode = SignUtil.signParamsMd5(params, "432346Ebuf#*(BF#$^GFB");
        System.out.println(signCode);
    }

}
