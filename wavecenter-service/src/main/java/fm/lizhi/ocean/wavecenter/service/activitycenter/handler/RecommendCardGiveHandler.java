package fm.lizhi.ocean.wavecenter.service.activitycenter.handler;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityRecommendCardConfigBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.activitycenter.constatns.ResourceGiveErrorTipConstant;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.*;
import fm.lizhi.ocean.wavecenter.service.activitycenter.manager.ActivityMaterielManager;
import fm.lizhi.ocean.wavecenter.service.activitycenter.processor.IRecommendCardGiveProcess;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityRecommendCardManager;
import fm.lizhi.ocean.wavecenter.service.common.processor.ProcessorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 推荐卡发放处理器
 */
@Slf4j
@Component
public class RecommendCardGiveHandler implements FlowResourceGiveHandler {

    @Autowired
    private ActivityMaterielManager activityMaterielManager;

    @Autowired
    private ActivityRecommendCardManager activityRecommendCardManager;

    @Autowired
    private ProcessorFactory processorFactory;

    @Override
    public Result<GiveFlowResourceResDTO> giveFlowResource(FlowResourceContext context) {
        try {
            ActivityFlowResourceGiveDTO flowResourceGiveDTO = context.getFlowResourceGiveDTO();
            ActivityResourceGiveDTO resourceGiveDTO = context.getResourceGiveDTO();
            //根据活动ID查询等级ID
            ActivityRecommendCardConfigBean cardConfig = activityRecommendCardManager
                    .getRecommendCardByAppIdAndLevelId(ContextUtils.getBusinessEvnEnum().appId(), context.getLevelId());
            if (cardConfig == null) {
                return RpcResult.fail(GIVE_FLOW_RESOURCE_NO_CONFIG, ResourceGiveErrorTipConstant.REC_CARD_GIVE_CONFIG_NOT_FOUND);
            }
            IRecommendCardGiveProcess processor = processorFactory.getProcessor(IRecommendCardGiveProcess.class);
            SendRecommendCardParamDTO paramDTO = new SendRecommendCardParamDTO();
            paramDTO.setUserId(flowResourceGiveDTO.getUserId());
            paramDTO.setCount(cardConfig.getCount());
            paramDTO.setReason(processor.getGiveReason(resourceGiveDTO.getActivityName()));
            paramDTO.setOperator("creator");
            paramDTO.setValid(cardConfig.getValidDay());
            Result<Void> result = activityMaterielManager.sendRecommendCard(paramDTO);
            if (RpcResult.isFail(result)) {
                String message = StringUtils.defaultIfBlank(result.getMessage(), ResourceGiveErrorTipConstant.REC_CARD_GIVE_FAIL);
                return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, message);
            }
            return RpcResult.success(new GiveFlowResourceResDTO().setBizRecordId(0L));
        } catch (Exception e) {
            log.error("RecommendCardGiveHandler.giveFlowResource happen error: context={}", JSONObject.toJSONString(context), e);
            return RpcResult.fail(GIVE_FLOW_RESOURCE_FAIL, ResourceGiveErrorTipConstant.REC_CARD_GIVE_EXCEPTION);
        }
    }

    @Override
    public Result<Void> cancelGiveFlowResource(DeleteOfficialSeatParamDTO param) {
        //推荐卡不是实时发放的，不需要取消业务侧的发放，直接修改平台的数据即可
        return RpcResult.success();
    }

    @Override
    public String getResourceCode() {
        return AutoConfigResourceEnum.REC_CARD.getResourceCode();
    }

}
