package fm.lizhi.ocean.wavecenter.service.live.manager;

import fm.lizhi.ocean.wavecenter.api.live.bean.*;

public interface LiveRoomCheckInManager {


    /**
     * 厅的日历汇总
     * @param req
     * @return
     */
    RoomDayCalendarRes roomCalendar(RoomDayCalendarReq req);

    /**
     * 厅的主播的打卡 详情信息
     * @param req
     * @return
     */
    RoomHourCheckDetailRes hourDetail(RoomHourCheckDetailReq req);


    /**
     * 厅的日统计
     *
     * @return
     */
    LRCSRoomDayStatsRes roomDayStats(RoomDayCheckStatsReq req);


    /**
     * 厅的日汇总
     */
    RoomDayStatsSummaryRes roomDayStatsSummary(RoomDayCheckStatsReq req);


    /**
     * 厅的小时统计
     *
     * @return
     */
    LRCSRoomHourStatsRes roomHourStats(RoomHourCheckStatsReq req);


    /**
     * 厅的小时汇总
     */
    RoomHourStatsSummaryRes roomHourStatsSummary(RoomHourCheckStatsReq req);

}
