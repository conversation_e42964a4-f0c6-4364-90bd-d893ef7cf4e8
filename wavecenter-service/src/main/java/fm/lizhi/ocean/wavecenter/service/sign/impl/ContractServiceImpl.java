package fm.lizhi.ocean.wavecenter.service.sign.impl;

import cn.hutool.core.collection.CollUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyAndNjContractBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.FamilyNjSignRecordBean;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestQueryContract;
import fm.lizhi.ocean.wavecenter.api.sign.request.RequestQueryFamilyNjSignRecordById;
import fm.lizhi.ocean.wavecenter.api.sign.service.ContractService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.sign.convert.SignConvert;
import fm.lizhi.ocean.wavecenter.service.sign.dto.FamilyNjSignRecordDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.QueryFamilyNjSignRecordDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.ContractManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@Slf4j
@ServiceProvider
public class ContractServiceImpl implements ContractService {


    @Autowired
    private ContractManager contractManager;

    /**
     * 查询合同
     * @return
     */
    @Override
    public Result<FamilyAndNjContractBean> queryContract(RequestQueryContract request){

        PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                .contractId(request.getContractId())
                .signId(request.getSignId())
                .build()
        );

        if (CollUtil.isEmpty(pageBean.getList())) {
            log.warn("contract not exist. request={}", request);
            return RpcResult.fail(ContractService.QUERY_CONTRACT_NOT_EXIST);
        }

        return RpcResult.success(pageBean.getList().get(0));
    }

    @Override
    public Result<FamilyNjSignRecordBean> querySignRecordById(RequestQueryFamilyNjSignRecordById param) {
        PageBean<FamilyNjSignRecordDTO> pageBean = contractManager.querySignRecord(QueryFamilyNjSignRecordDTO.builder()
                .id(param.getId())
                .build()
        );
        if (CollUtil.isEmpty(pageBean.getList())) {
            log.warn("sign record not exist. id={}", param.getId());
            return RpcResult.fail(ContractService.QUERY_SIGN_RECORD_NOT_EXIST);
        }

        FamilyNjSignRecordDTO dto = pageBean.getList().get(0);
        return RpcResult.success(SignConvert.I.toFamilyNjSignRecordBean(dto));
    }


}
