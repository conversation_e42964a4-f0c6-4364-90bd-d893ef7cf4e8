package fm.lizhi.ocean.wavecenter.service.background.activitycenter.convert;

import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.RequestCancelActivity;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.ActivityUserCancelParamDTO;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActivityAdminOperateConvert {

    ActivityAdminOperateConvert I = Mappers.getMapper(ActivityAdminOperateConvert.class);

    @Mappings({
            @Mapping(target = "targetAuditStatus", expression = "java(fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityAuditStatusEnum.OFFICIAL_CANCEL.getStatus())"),
            @Mapping(target = "activityInfo", ignore = true),
            @Mapping(target = "operateUserId", ignore = true),

    })
    ActivityUserCancelParamDTO buildActivityUserCancelParam(RequestCancelActivity request);

}
