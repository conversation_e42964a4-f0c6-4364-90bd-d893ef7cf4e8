package fm.lizhi.ocean.wavecenter.service.activitycenter.dto;

import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.ActivityBigClassTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 活动提报详细信息，用于APP端查看
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ActivityApplyDetailForAppDTO {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 业务定义不超过10个字，稍微冗余
     */
    private String name;
    /**
     * 提报厅厅主ID
     */
    private Long njId;
    /**
     * 活动分类ID
     */
    private Long classId;
    /**
     * 活动分类名称
     */
    private String className;
    /**
     * 活动开始时间
     */
    private Date startTime;
    /**
     * 活动结束时间
     */
    private Date endTime;
    /**
     * 陪档主播ID列表
     */
    private List<Long> accompanyNjIds;
    /**
     * 活动海报图片地址
     */
    private String posterUrl;
    /**
     * 玩法工具，多个逗号分隔
     */
    private String activityTool;
    /**
     * 活动介绍，不超过100字
     */
    private String introduction;
    /**
     * 资源图片url
     */
    private String flowResourceImageUrl;
    /**
     * 活动大分类类型 {@link ActivityBigClassTypeEnum}
     */
    private int activityBigClassType;
}
