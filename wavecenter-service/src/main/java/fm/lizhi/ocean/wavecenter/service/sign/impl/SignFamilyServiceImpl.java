package fm.lizhi.ocean.wavecenter.service.sign.impl;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.sign.bean.*;
import fm.lizhi.ocean.wavecenter.api.sign.constant.ContractTypeEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.SignRelationEnum;
import fm.lizhi.ocean.wavecenter.api.sign.constant.UserSignStatusEnum;
import fm.lizhi.ocean.wavecenter.api.sign.request.*;
import fm.lizhi.ocean.wavecenter.api.sign.response.*;
import fm.lizhi.ocean.wavecenter.api.sign.service.SignFamilyService;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInfoBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.ResultHandler;
import fm.lizhi.ocean.wavecenter.service.common.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.service.sign.convert.SignConvert;
import fm.lizhi.ocean.wavecenter.service.sign.dto.FamilyNjCancelRecordDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.RequestFamilyAndNjContractDTO;
import fm.lizhi.ocean.wavecenter.service.sign.dto.SignSettleDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.*;
import fm.lizhi.ocean.wavecenter.service.sign.process.FamilySignProcessor;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@ServiceProvider
public class SignFamilyServiceImpl implements SignFamilyService {

    @Autowired
    private NonContractManager nonContractManager;
    @Autowired
    private ProcessorFactory processorFactory;
    @Autowired
    private ContractManager contractManager;
    @Autowired
    private UserManager userManager;
    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private SignFlowManager signFlowManager;
    @Autowired
    private SettleManager settleManager;

    @Override
    public Result<ResponseFamilyReviewCancel> reviewCancel(RequestFamilyReviewCancel request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{

            FamilySignProcessor processor = processorFactory.getProcessor(FamilySignProcessor.class);
            ResponseFamilyReviewCancel checkRes = processor.reviewCancelCheck(request);
            if (checkRes.getCode() != 0) {
                LogContext.addResLog("checkResCode={}", checkRes.getCode());
                return RpcResult.success(checkRes);
            }

            Pair<Integer, String> res = nonContractManager.reviewCancel(request.getPlayerSignId(), request.getCurUserId(), request.getOperateType());
            LogContext.addResLog("resCode={}", res.getKey());

            return RpcResult.success(new ResponseFamilyReviewCancel().setCode(res.getKey()).setMsg(res.getValue()));
        });
    }

    @Override
    public Result<List<TodoSignBean>> todoRoomList(RequestFamilyTodoRoomList request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{

            //查询待办列表
            List<TodoSignBean> todoSignBeans = contractManager.todoRoomList(request);
            List<Long> njIds = todoSignBeans.stream().map(v -> v.getRoomInfo().getId()).collect(Collectors.toList());

            //查询下级人数
            Map<Long, Integer> signCountMap = nonContractManager.countNjSign(njIds);

            //查询厅主信息
            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(njIds);

            for (TodoSignBean todoSignBean : todoSignBeans) {
                UserInfoBean roomInfo = todoSignBean.getRoomInfo();
                Long njId = roomInfo.getId();
                Integer count = signCountMap.get(njId);
                if (count != null) {
                    todoSignBean.setSignCount(count);
                }
                SimpleUserDto user = userMap.get(njId);
                if (user != null) {
                    roomInfo.setName(user.getName());
                    roomInfo.setBand(user.getBand());
                    roomInfo.setPhoneNum(user.getPhoneNum());
                    roomInfo.setPhoto(user.getAvatar());
                }
                //签约状态转换
                detailStatus(todoSignBean);
            }

            LogContext.addResLog("todoList={}", JsonUtil.dumps(todoSignBeans));
            return RpcResult.success(todoSignBeans);
        });
    }

    @Override
    public Result<List<TodoSignPlayerBean>> todoPlayerList(RequestFamilyTodoPlayerList request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {
            List<TodoSignPlayerBean> list = nonContractManager.todoPlayerList(request);

            List<Long> userIds = new ArrayList<>();
            for (TodoSignPlayerBean todoSignPlayerBean : list) {
                userIds.add(todoSignPlayerBean.getPlayerInfo().getId());
                userIds.add(todoSignPlayerBean.getRoomInfo().getId());
            }

            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);
            for (TodoSignPlayerBean todoSignPlayerBean : list) {
                UserBean playerInfo = todoSignPlayerBean.getPlayerInfo();
                SimpleUserDto player = userMap.get(playerInfo.getId());
                if (player != null) {
                    playerInfo.setName(player.getName());
                    playerInfo.setBand(player.getBand());
                    playerInfo.setPhoto(player.getAvatar());
                }

                UserBean roomInfo = todoSignPlayerBean.getRoomInfo();
                SimpleUserDto room = userMap.get(roomInfo.getId());
                if (room != null) {
                    roomInfo.setName(room.getName());
                    roomInfo.setBand(room.getBand());
                    roomInfo.setPhoto(room.getAvatar());
                }
            }
            LogContext.addResLog("todoList={}", JsonUtil.dumps(list));

            return RpcResult.success(list);
        });
    }

    /**
     * 区分详细状态
     * @param todoSignBean
     */
    private void detailStatus(ISignRecord todoSignBean){
        //待签署 -> 区分厅主是否已签署
        if ((SignRelationEnum.WAIT_SIGN.getCode().equals(todoSignBean.findStatus())
                || SignRelationEnum.SIGNING.getCode().equals(todoSignBean.findStatus()))) {
            if (StringUtils.isBlank(todoSignBean.findSignId())) {
                todoSignBean.changeStatus(SignRelationEnum.WAIT_PARTNER_SIGN.getCode());
                return;
            }

            if (UserSignStatusEnum.WAIT_SIGN.getCode().equals(todoSignBean.findFamilySignStatus())) {
                todoSignBean.changeStatus(SignRelationEnum.WAIT_SIGN.getCode());
            } else {
                todoSignBean.changeStatus(SignRelationEnum.WAIT_PARTNER_SIGN.getCode());
            }
        }

        //拒绝 -> 区分是否厅主拒绝
        if (SignRelationEnum.REJECT.getCode().equals(todoSignBean.findStatus())
                && UserSignStatusEnum.REJECT.getCode().equals(todoSignBean.findNjSignStatus())) {
            todoSignBean.changeStatus(SignRelationEnum.PARTNER_REJECT.getCode());
        }
    }

    @Override
    public Result<PageBean<RoomSignRecordBean>> queryRoomSignList(RequestQueryRoomSignList request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {
            //查询记录
            PageBean<RoomSignRecordBean> pageList = contractManager.queryRoomSignList(request);
            if (CollectionUtils.isEmpty(pageList.getList())) {
                return RpcResult.success(pageList);
            }

            List<Long> njIds = pageList.getList().stream().map(v -> v.getRoomInfo().getId()).collect(Collectors.toList());

            //查询下级人数
            Map<Long, Integer> signCountMap = nonContractManager.countNjSign(njIds);

            //查询厅主信息
            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(njIds);

            //结算信息
            Map<Long, SignSettleDTO> settleMap = new HashMap<>();

            //解约查询原合同
            //key=解约合同id，value=原合同id
            Map<Long, Long> oldContractIdMap = new HashMap<>();
            //key=原合同id, value=原合同信息
            Map<Long, FamilyAndNjContractBean> oldContractMap = new HashMap<>();
            if (ContractTypeEnum.CANCEL == request.getType()) {
                List<Long> contractIds = pageList.getList().stream().map(RoomSignRecordBean::getContractId).collect(Collectors.toList());
                List<FamilyNjCancelRecordDTO> cancelRecords = contractManager.queryCancelRecordByContractId(contractIds);
                if (CollectionUtils.isNotEmpty(cancelRecords)) {
                    for (FamilyNjCancelRecordDTO cancelRecord : cancelRecords) {
                        oldContractIdMap.put(cancelRecord.getCancelContractId(), cancelRecord.getOldContractId());
                    }
                    PageBean<FamilyAndNjContractBean> oldList = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                            .contractLists(oldContractIdMap.values()).pageSize(100)
                            .build());
                    oldContractMap = oldList.getList().stream().collect(Collectors.toMap(FamilyAndNjContractBean::getContractId, v->v));

                    //解约的查询原合同的结算信息
                    settleMap = settleManager.querySettle(new ArrayList<>(oldContractMap.values()));
                }
            } else {
                settleMap = settleManager.querySettle(SignConvert.I.roomSignRecordBeans2familyNjSigns(pageList.getList()));
            }

            for (RoomSignRecordBean rb : pageList.getList()) {
                UserInfoBean roomInfo = rb.getRoomInfo();
                Long njId = roomInfo.getId();

                //下级人数
                Integer count = signCountMap.get(njId);
                if (count != null) {
                    rb.setSignCount(count);
                }

                //厅主信息
                SimpleUserDto user = userMap.get(njId);
                if (user != null) {
                    roomInfo.setName(user.getName());
                    roomInfo.setBand(user.getBand());
                    roomInfo.setPhoneNum(user.getPhoneNum());
                    roomInfo.setPhoto(user.getAvatar());
                }

                //原合同信息
                if (ContractTypeEnum.CANCEL == request.getType()) {
                    Long oldId = oldContractIdMap.get(rb.getContractId());
                    if (oldId != null) {
                        FamilyAndNjContractBean oldContract = oldContractMap.get(oldId);
                        if (oldContract != null) {
                            rb.setOldContractId(oldContract.getContractId());
                            rb.setOldBeginTime(oldContract.getBeginTime());
                            rb.setOldExpireTime(oldContract.getExpireTime());
                        }

                        SignSettleDTO settleDTO = settleMap.get(oldId);
                        if (settleDTO != null) {
                            rb.setSettleType(settleDTO.getSettleType());
                            rb.setSettlePercentage(settleDTO.getSettlePercentage());
                        }
                    }
                } else {
                    SignSettleDTO settleDTO = settleMap.get(rb.getContractId());
                    if (settleDTO != null) {
                        rb.setSettleType(settleDTO.getSettleType());
                        rb.setSettlePercentage(settleDTO.getSettlePercentage());
                    }
                }

                //签约状态转换
                detailStatus(rb);
            }

            return RpcResult.success(pageList);
        });
    }

    @Override
    public Result<PageBean<CancelPlayerRecordBean>> queryCancelPlayerList(RequestQueryRoomSignList request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {
            //查询记录
            PageBean<CancelPlayerRecordBean> pageList = nonContractManager.queryFamilyCancelPlayerList(request);
            if (CollectionUtils.isEmpty(pageList.getList())) {
                LogContext.addResLog("pageList is empty");
                return RpcResult.success(pageList);
            }

            List<CancelPlayerRecordBean> list = pageList.getList();
            List<Long> userIds = new ArrayList<>();
            for (CancelPlayerRecordBean cb : list) {
                userIds.add(cb.getPlayerInfo().getId());
                userIds.add(cb.getRoomInfo().getId());
            }

            Map<Long, SimpleUserDto> userMap = userManager.getSimpleUserMapByIds(userIds);
            for (CancelPlayerRecordBean cb : list) {
                UserBean playerInfo = cb.getPlayerInfo();
                SimpleUserDto player = userMap.get(playerInfo.getId());
                if (player != null) {
                    playerInfo.setName(player.getName());
                    playerInfo.setBand(player.getBand());
                    playerInfo.setPhoto(player.getAvatar());
                }

                UserBean roomInfo = cb.getRoomInfo();
                SimpleUserDto room = userMap.get(roomInfo.getId());
                if (room != null) {
                    roomInfo.setName(room.getName());
                    roomInfo.setBand(room.getBand());
                    roomInfo.setPhoto(room.getAvatar());
                }
            }

            return RpcResult.success(pageList);
        });
    }

    @Override
    public Result<Integer> countCanOpenRoomNum(int appId, long familyId) {
        LogContext.addReqLog("appId={},familyId={}", appId, familyId);
        LogContext.addResLog("appId={},familyId={}", appId, familyId);
        return ResultHandler.handle(appId, ()->{
            Integer num = familyManager.countCanOpenRoomNum(familyId);
            LogContext.addResLog("num={}", num);
            return RpcResult.success(num);
        });
    }

    @Override
    public Result<Integer> countSignRoomNum(int appId, long familyId) {
        LogContext.addReqLog("appId={},familyId={}", appId, familyId);
        LogContext.addResLog("appId={},familyId={}", appId, familyId);
        return ResultHandler.handle(appId, ()->{
            Integer num = contractManager.countSignRoomNum(familyId);
            LogContext.addResLog("num={}", num);
            return RpcResult.success(num);
        });
    }

    @Override
    public Result<ResponseFamilyDoSign> doSign(RequestFamilyDoSign request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{
            //前置检查
            FamilySignProcessor processor = processorFactory.getProcessor(FamilySignProcessor.class);

            ResponseFamilyDoSign checkRes = processor.doSignCheck(request);
            if (checkRes.getCode() != 0) {
                LogContext.addResLog("checkResCode={}", checkRes.getCode());
                return RpcResult.success(checkRes);
            }

            //生成合同URL
            Optional<String> urlOp = contractManager.genContractSignUrl(request.getCurUserId(), request.getSignId());

            if (urlOp.isPresent()) {
                processor.doSignSuccessProcessor(request);
            }

            return urlOp.map(s -> RpcResult.success(new ResponseFamilyDoSign().setContractUrl(s)))
                    .orElseGet(() -> RpcResult.fail(DO_SIGN_URL_GEN_FAIL));

        });
    }

    @Override
    public Result<ResponseFamilyInviteAdmin> inviteAdmin(RequestFamilyInviteAdmin request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), () -> {
            if (request.getCurUserId().equals(request.getTargetUserId())) {
                return RpcResult.success(
                        new ResponseFamilyInviteAdmin().setCode(-1).setMsg("家族长不可以邀请自己")
                );
            }

            FamilySignProcessor processor = processorFactory.getProcessor(FamilySignProcessor.class);
            ResponseFamilyInviteAdmin checkRes = processor.inviteAdminCheck(request);
            if (checkRes.getCode() != 0) {
                LogContext.addResLog("checkResCode={}", checkRes.getCode());
                return RpcResult.success(checkRes);
            }

            //检查是否有待签署合同
            Optional<FamilyAndNjContractBean> existContractOp = processor.existWaitSignContract(request);
            if (existContractOp.isPresent() && StringUtils.isNotBlank(existContractOp.get().getSignId())) {
                ResponseFamilyInviteAdmin res = new ResponseFamilyInviteAdmin().setCode(0);
                if (SignRelationEnum.SIGN_SUCCESS.getCode().equals(existContractOp.get().getStatus())) {
                    return RpcResult.success(res
                            .setCode(-1)
                            .setMsg("已签署")
                    );
                }

                //请求用户已签署也算完成
                Map<Long, UserSignStatusEnum> contractUserSignStatus = contractManager.getContractUserSignStatus(existContractOp.get().getSignId());
                if (contractUserSignStatus.get(request.getCurUserId()) == UserSignStatusEnum.SIGN_SUCCESS) {
                    return RpcResult.success(res
                            .setCode(-1)
                            .setMsg("已签署")
                    );
                }

                return RpcResult.success(res.setSignId(existContractOp.get().getSignId()));
            }

            ResponseFamilyInviteAdmin res = contractManager.familyInviteAdmin(request);
            LogContext.addResLog("resCode={}", res.getCode());

            if (res.getCode() != ResponseSignResult.SUCCESS_CODE) {
                return RpcResult.success(res);
            }

            if (StringUtils.isNotBlank(res.getSignId())) {
                Optional<String> urlOp = contractManager.genContractSignUrl(request.getCurUserId(), res.getSignId());
                if (!urlOp.isPresent()) {
                    LogContext.addResLog("url is blank");
                    return RpcResult.success(res.setCode(-1));
                }
                res.setContractUrl(urlOp.get());

                if (res.getContractId() != null) {
                    processor.inviteAdminSuccessProcessor(request, res.getContractId());
                }
            }

            return RpcResult.success(res);
        });
    }

    @Override
    public Result<ResponseFamilyApplyCancelAdmin> applyCancelAdmin(RequestFamilyApplyCancelAdmin request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{
            FamilySignProcessor processor = processorFactory.getProcessor(FamilySignProcessor.class);

            ResponseFamilyApplyCancelAdmin checkRes = processor.applyCancelAdmin(request);
            if (checkRes.getCode() != ResponseSignResult.SUCCESS_CODE) {
                LogContext.addResLog("checkResCode={}", checkRes.getCode());
                return RpcResult.success(checkRes);
            }

            Optional<String> signIdOp = contractManager.familyApplyCancelAdmin(request);
            if (!signIdOp.isPresent()) {
                LogContext.addResLog("signId is not present");
                return RpcResult.fail(APPLY_CANCEL_ADMIN_FAIL);
            }

            Optional<String> contractUrlOp = contractManager.genContractSignUrl(request.getCurUserId(), signIdOp.get());
            if (!contractUrlOp.isPresent()) {
                LogContext.addResLog("contactUrl is not present");
                return RpcResult.fail(APPLY_CANCEL_ADMIN_FAIL);
            }

            ResponseFamilyApplyCancelAdmin response = new ResponseFamilyApplyCancelAdmin();
            response.setContractUrl(contractUrlOp.get());
            PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                    .signId(signIdOp.get()).pageSize(1)
                    .build());
            if (CollectionUtils.isNotEmpty(pageBean.getList())) {
                FamilyAndNjContractBean sign = pageBean.getList().get(0);
                response.setContractId(sign.getContractId());
                //标记待同步签署状态
                processor.applyCancelAdminSuccessProcessor(request, sign.getContractId());
            }

            return RpcResult.success(response);
        });
    }

    @Override
    public Result<ResponseFamilyDoCancelAdmin> doCancelAdmin(RequestFamilyDoCancelAdmin request) {
        LogContext.addReqLog("request={}", JsonUtil.dumps(request));
        LogContext.addResLog("request={}", JsonUtil.dumps(request));
        return ResultHandler.handle(request.getAppId(), ()->{
            ResponseFamilyDoCancelAdmin res = new ResponseFamilyDoCancelAdmin();

            PageBean<FamilyAndNjContractBean> pageBean = contractManager.queryContract(RequestFamilyAndNjContractDTO.builder()
                    .contractId(request.getContractId())
                    .pageSize(1)
                    .build());
            if (CollectionUtils.isEmpty(pageBean.getList())) {
                LogContext.addResLog("pageList is empty");
                return RpcResult.success(res.setCode(-1).setMsg("合同不存在"));
            }

            FamilyAndNjContractBean contract = pageBean.getList().get(0);
            if (!contract.getFamilyId().equals(request.getFamilyId())) {
                LogContext.addResLog("curUserId not eq njUserId");
                return RpcResult.success(res.setCode(-1).setMsg("您不是合同所有人"));
            }

            Optional<String> urlOp = contractManager.genContractSignUrl(request.getCurrUserId(), request.getSignId());
            if (!urlOp.isPresent()) {
                LogContext.addResLog("genContractSignUrl fail");
                return RpcResult.success(res.setCode(-1).setMsg("合同签署链接生成失败"));
            }

            FamilySignProcessor processor = processorFactory.getProcessor(FamilySignProcessor.class);
            processor.doCancelAdminSuccessProcessor(request);

            return RpcResult.success(res.setContractUrl(urlOp.get()));

        });
    }
}
