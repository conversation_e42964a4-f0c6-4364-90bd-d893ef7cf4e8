package fm.lizhi.ocean.wavecenter.service.live.manager;

import fm.lizhi.ocean.wavecenter.api.live.bean.*;

public interface LiveGuildCheckInManager {


    LGCSRoomDayStatsRes roomDayStats(GuildRoomDayCheckStatsReq req);

    GuildRoomDayStatsSummaryRes roomDayStatsSummary(GuildRoomDayCheckStatsReq req);

    LGCSRoomHourStatsRes roomHourStats(GuildRoomHourCheckStatsReq req);

    GuildRoomHourStatsSummaryRes roomHourStatsSummary(GuildRoomHourCheckStatsReq req);
}
