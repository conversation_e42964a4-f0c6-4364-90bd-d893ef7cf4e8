<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fm.lizhi.ocean.wavecenter</groupId>
        <artifactId>lz-ocean-wavecenter</artifactId>
        <version>1.5.7</version>
    </parent>

    <artifactId>wavecenter-service</artifactId>

    <properties>
        <!-- 跳过安装 -->
<!--        <maven.install.skip>true</maven.install.skip>-->
        <!-- 跳过部署 -->
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!-- ====================        内部依赖        ==================== -->

        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-domain</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- ====================        基础架构的依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-serviceclient</artifactId>
        </dependency>


        <!-- ====================        第三方框架依赖        ==================== -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
    </dependencies>

</project>