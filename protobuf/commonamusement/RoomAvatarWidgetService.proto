option java_package = "fm.lizhi.xm.vip.protocol.commonamusement";
option java_outer_classname = "RoomAvatarWidgetProto";

// 房间外显头像框
message RoomAvatarWidget {
    optional int64 id = 1; // 主键ID
    optional string name = 2; // 头像框名称
    optional string imageUrl = 3; // 图标地址
    optional string createTime = 4; //创建时间
    optional string svgaUrl = 5; // 图标地址
    optional int32 usefulLife = 6; //有效期，0表示无限期
    optional string usefulLifeUnit = 7; //有效期单位，s:秒, m:分钟, h:小时, d:天, M:月, y:年

}
//头像框发放记录
message GrantedAvatarWidget {
    optional int64 id = 1; //主键id
    optional string name = 2; // 头像框名称
    optional string imageUrl = 3; // 图标地址
    optional int32 type = 4; //图像类型，0表示没有图像，1表示图片，2表示svga
    optional string startTime = 5; //生效时间
    optional string endTime = 6; //失效时间
    optional int64 avatarWidgetId = 7; //头像框id
    optional bool valid = 8; //头像框是否有效，true表示有效
    optional string band = 9; //用户波段号
    optional string userName = 10; // 用户名称
    optional string svgaUrl = 11; // 图标地址
    optional int32 validStatus = 12; //头像框状态，1表示筛选有效状态，2表示筛选无效状态,3表示待生效
}

// RoomAvatarWidgetService.java
// 获取配置的房间外显头像框列表
// domain = 20013, op = 311
message RequestGetAvatarWidgetList {
    optional string jsonParam = 1;
}

// rCode == 0 (SUCCESS) == 成功
// rCode == 1 (FAIL) == 失败
// rCode == 2 (ILLEGAL_PARAMS) == 非法参数
message ResponseGetAvatarWidgetList {
    repeated RoomAvatarWidget avatarWidget = 1; // 房间外显头像框
    optional int32 total = 2; //总条数
}

// RoomAvatarWidgetService.java
// 添加配置房间外显头像框
// domain = 20013, op = 312
message RequestAddAvatarWidget {
    optional string jsonParam = 1;
}

// rCode == 0 (SUCCESS) == 成功
// rCode == 1 (FAIL) == 失败
// rCode == 2 (ILLEGAL_PARAMS) == 非法参数
message ResponseAddAvatarWidget {
}

// RoomAvatarWidgetService.java
// 发放房间外显头像框
// domain = 20013, op = 313
message RequestGrantUsersAvatarWidget {
    optional string jsonParam = 1;
}

// rCode == 0 (SUCCESS) == 成功
// rCode == 1 (FAIL) == 失败
// rCode == 2 (ILLEGAL_PARAMS) == 非法参数
message ResponseGrantUsersAvatarWidget {
    optional string message = 1; //提示信息
}

// RoomAvatarWidgetService.java
// 获取已发放的房间外显头像框记录列表
// domain = 20013, op = 314
message RequestGetGrantedAvatarWidgetList {
    optional string jsonParam = 1;
}

// rCode == 0 (SUCCESS) == 成功
// rCode == 1 (FAIL) == 失败
// rCode == 2 (ILLEGAL_PARAMS) == 非法参数
message ResponseGetGrantedAvatarWidgetList {
    repeated GrantedAvatarWidget avatarWidget = 1; //头像框发放记录
    optional int32 total = 2; //总条数
}

// RoomAvatarWidgetService.java
// 取消用户的有效头像框
// domain = 20013, op = 315
message RequestDeleteUserAvatarWidget {
    optional string jsonParam = 1;
}

// rCode == 0 (SUCCESS) == 成功
// rCode == 1 (FAIL) == 失败
// rCode == 2 (ILLEGAL_PARAMS) == 非法参数
message ResponseDeleteUserAvatarWidget {
}

// RoomAvatarWidgetService.java
// 查询用户的最新有效头衔框
// domain = 20013, op = 316
message RequestGetUserLastValidAvatarWidget {
    optional string jsonParam = 1;
}

// rCode == 0 (SUCCESS) == 成功
// rCode == 1 (FAIL) == 失败
// rCode == 2 (ILLEGAL_PARAMS) == 非法参数
message ResponseGetUserLastValidAvatarWidget {
    optional GrantedAvatarWidget avatarWidget = 1; //勋章发放记录
}


// RoomAvatarWidgetService.java
// 更新配置房间外显头像框
// domain = 20013, op = 317
message RequestUpdateAvatarWidget {
    optional string jsonParam = 1;
}

message ResponseUpdateAvatarWidget {
}

// RoomAvatarWidgetService.java
// 发放房间外显头像框,用于任务系统
// domain = 20013, op = 318
message RequestGrantUsersAvatarWidgetFromTask {
    optional string jsonParam = 1;
}

// rCode == 0 (SUCCESS) == 成功
// rCode == 1 (FAIL) == 失败
// rCode == 2 (ILLEGAL_PARAMS) == 非法参数
message ResponseGrantUsersAvatarWidgetFromTask {
    optional string message = 1; //提示信息
}