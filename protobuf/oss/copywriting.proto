option java_package = "fm.lizhi.pp.oss.protocol";
option java_outer_classname = "CopywritingProto";

/**文案实体*/
message Copywriting {
  optional int32 id = 1;  //主键ID
  optional int32 type = 2;  //文案类型 0 分享文案 1 开播文案
  optional int32 delAble = 3;  //是否可刪除 0 否 1是
  optional int32 status = 4;  //状态 0刪除 1有效
  optional string tags = 5;  //文案对应的标签
  optional string trailerTitle = 6;  //预告文案标题
  optional string trailerContent = 7;  //预告文案内容
  optional string liveTitle = 8;  //直播文案标题/开播文案标题
  optional string liveContent = 9;  //直播文案内容/开播文案内容
  optional int64 createTime = 10;  // 创建时间
  optional int64 modifyTime = 11;  // 修改时间
}


/**简易文案实体*/
message SimpleCopywriting {
  optional int32 type = 1; //文案类型 0 分享文案 1 开播文案
  optional string trailerTitle = 2; //预告文案标题
  optional string trailerContent = 3; //预告文案内容
  optional string liveTitle = 4; //直播文案标题/开播文案标题
  optional string liveContent = 5; //直播文案内容/直播文案内容
  optional string tags = 6; //文案对应的标签
  optional int32 delAble = 7; //是否可刪除 0 否 1是
}

// LiveCopywritingManagerService.java
// 分页查询文案信息
// domain = 364, op = 1451
message RequestGetCopywritingList {
  optional int32 page = 1; // 哪一页
  optional int32 pageSize = 2; // 页大小，最大不超过 100
  optional int32 type = 3;  //文案类型 0 分享文案 1 开播文案
  optional int32 delAble = 4;  //是否可刪除 0 否 1是
  optional int32 status = 5;  //状态 0刪除 1有效
}
// rCode==0 (SUCCESS) == 成功
// rCode==1 (FAIL) == 失败
message ResponseGetCopywritingList {
  repeated Copywriting copywriting = 1; // 文案列表
}


// LiveCopywritingManagerService.java
// 删除文案
// domain = 364, op = 1452
message RequestDeleteCopywriting{
  optional int32 id = 1;//主键id
}

// LiveCopywritingManagerService.java
// 删除文案
// domain = 364, op = 1453
// rCode==0 (SUCCESS) == 成功
// rCode==1 (FAIL) == 失败
message RequestUpdateCopywriting{
  optional int32 id = 1;  //主键ID
  optional string tags = 2;  //文案对应的标签
  optional string trailerTitle = 3;  //预告文案标题
  optional string trailerContent = 4;  //预告文案内容
  optional string liveTitle = 5;  //直播文案标题/开播文案标题
  optional string liveContent = 6;  //直播文案内容/开播文案内容
}

// LiveCopywritingManagerService.java
// 新增文案
// domain = 364, op = 1454
message RequestAddCopywriting{
  optional int32 type = 1; //文案类型 0 分享文案 1 开播文案
  optional string trailerTitle = 2; //预告文案标题
  optional string trailerContent = 3; //预告文案内容
  optional string liveTitle = 4; //直播文案标题/开播文案标题
  optional string liveContent = 5; //直播文案内容/开播文案内容
  optional string tags = 6; //文案对应的标签
}

// LiveCopywritingManagerService.java
// 文案总数响应
// domain = 364, op = 1455
message RequestCopywritingTotal{
  optional int32 type = 1;//类型
  optional int32 delAble = 2;//是否可删除
  optional int32 status = 3;//状态
}
// rCode==0 (SUCCESS) == 成功
// rCode==1 (FAIL) == 失败
message ResponseCopywritingTotal{
  optional int64 total = 1;//状态
}

// LiveCopywritingManagerService.java
// 文案总数响应
// domain = 364, op = 1456
// rCode==0 (SUCCESS) == 成功
message RequestGetCopywriting{
  optional int32 id = 1;//主键
}
// rCode==0 (SUCCESS) == 成功
// rCode==1 (FAIL) == 失败
message ResponseGetCopywriting{
  optional Copywriting copywriting = 1;//文案
}

// LiveCopywritingManagerService.java
// 获取所有标签响应
// domain = 364, op = 1457
message RequestGetTags{
  optional int32 type = 1;//文案类型
}
// rCode==0 (SUCCESS) == 成功
// rCode==1 (FAIL) == 失败
message ResponseGetTags{
  repeated string tags = 1;//文案
}


// LiveCopywritingService.java
// 获取文案请求
// domain = 364, op = 1461
message RequestCopywriting {
  optional string tag = 1; //节目的TAG
  optional int32 type = 2; //文案类型 0 分享文案 1 开播文案
}
// rCode==0 (SUCCESS) == 成功
// rCode==1 (FAIL) == 失败
message ResponseCopywriting {
  optional SimpleCopywriting copywriting = 1 ;
}