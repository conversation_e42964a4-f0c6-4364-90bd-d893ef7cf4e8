package fm.lizhi.live.oss.pp.protocol;

option java_package = "fm.lizhi.xm.vip.protocol.oss";
option java_outer_classname = "LiveEmotionProto";

/**表情图片*/
message EmotionImages {
    optional string imageUrl = 1;
}

message EmotionInfo {
    optional int64 emotionId = 1; //表情id
    optional string name = 2; //表情名称
    optional float aspect = 3; //宽高比，即宽比高，客户端定高
    optional float factor = 4; //尺寸系数，用于放大缩小显示，默认为1
    optional int64 svgaPackageId = 5; //SVGA动画包ID
    optional int32 repeatCount = 6; //重复次数。0表示无限重复
    repeated EmotionImages emotionImages = 7; //重复停止图片列表，用于骰子、剪刀石头布的结果显示
    optional string image = 8 ; // 显示图片
    optional string gifImageUrl = 9; // gift图片地址
    optional string webConfig = 10 ;//针对web扩展配置
    optional string ppImage = 11 ;// PP图片
}

// LiveEmotionService.java
// 根据表情id获取表情
// domain = 20013, op = 561
message RequestGetEmotionInfo {
    optional int64 emotionId = 1; //表情id
}


// rCode==0 (SUCCESS) == 成功
// rCode==1 (FAIL) == 失败
message ResponseGetEmotionInfo {
   optional EmotionInfo emotionInfo = 1;

}




// LiveEmotionService.java
// 表情列表
// domain = 20013, op = 562
message RequestGetEmotionList {
}

// rCode==0 (SUCCESS) == 成功
// rCode==1 (FAIL) == 失败
message ResponseGetEmotionList {
    repeated EmotionInfo emotions = 1;
}