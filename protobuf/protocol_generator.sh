#!/usr/bin/env bash
# user
#protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc common_base.proto > protoc_info.log 2> protoc_error.log
#protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc common_request.proto > protoc_info.log 2> protoc_error.log
#protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc common.proto > protoc_info.log 2> protoc_error.log
protoc --java_out=. --include_source_info --descriptor_set_out=descriptor.desc protocol_live_enternotice.proto > protoc_info.log 2> protoc_error.log