package fm.lizhi.hy.operating.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestGetMedalList;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.ResponseGetMedalList;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestGetMedalTotal;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.ResponseGetMedalTotal;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestAddMedal;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestUpdateMedal;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestGetMedal;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.ResponseGetMedal;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.TaskAward;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.TaskAwardBean;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.AwardContentBean;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.Medal;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.Guard;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface MedalService {
	
	
	/**
	 *  勋章列表
	 *
	 * @param taskId
	 *            任务ID(不一定用上)
	 * @param offset
	 *            偏移量
	 * @param count
	 *            数量
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 没有数据<br>
	 */
	@Service(domain = 20005, op = 40, request = RequestGetMedalList.class, response = ResponseGetMedalList.class)
	@Return(resultType = ResponseGetMedalList.class)
	Result<ResponseGetMedalList> getMedalList(@Attribute(name = "taskId") long taskId, @Attribute(name = "offset") int offset, @Attribute(name = "count") int count);
	
	
	/**
	 *  勋章总数
	 *
	 * @param taskId
	 *            任务ID
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 没有数据<br>
	 */
	@Service(domain = 20005, op = 41, request = RequestGetMedalTotal.class, response = ResponseGetMedalTotal.class)
	@Return(resultType = ResponseGetMedalTotal.class)
	Result<ResponseGetMedalTotal> getMedalTotal(@Attribute(name = "taskId") long taskId);
	
	
	/**
	 *  新增勋章
	 *
	 * @param medal
	 *            勋章
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 新增失败<br>
	 */
	@Service(domain = 20005, op = 42, request = RequestAddMedal.class)
	@Return(resultType = Void.class)
	Result<Void> addMedal(@Attribute(name = "medal") Medal medal);
	
	
	/**
	 *  更新勋章
	 *
	 * @param medal
	 *            勋章
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 更新失败<br>
	 */
	@Service(domain = 20005, op = 43, request = RequestUpdateMedal.class)
	@Return(resultType = Void.class)
	Result<Void> updateMedal(@Attribute(name = "medal") Medal medal);
	
	
	/**
	 *  获取勋章详情
	 *
	 * @param id
	 *            id
	 * @return 
	 */
	@Service(domain = 20005, op = 44, request = RequestGetMedal.class, response = ResponseGetMedal.class)
	@Return(resultType = ResponseGetMedal.class)
	Result<ResponseGetMedal> getMedal(@Attribute(name = "id") long id);
	
	
	public static final int GET_MEDAL_LIST_INVALID_PARAM = 1; // 参数非法
	public static final int GET_MEDAL_LIST_NO_DATA = 2; // 没有数据

	public static final int GET_MEDAL_TOTAL_INVALID_PARAM = 1; // 参数非法
	public static final int GET_MEDAL_TOTAL_NO_DATA = 2; // 没有数据

	public static final int ADD_MEDAL_INVALID_PARAM = 1; // 参数非法
	public static final int ADD_MEDAL_ADD_FAIL = 2; // 新增失败

	public static final int UPDATE_MEDAL_INVALID_PARAM = 1; // 参数非法
	public static final int UPDATE_MEDAL_UPDATE_FAIL = 2; // 更新失败


}