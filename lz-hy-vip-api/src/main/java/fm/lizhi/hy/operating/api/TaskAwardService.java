package fm.lizhi.hy.operating.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestGetTaskAwardByType;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.ResponseGetTaskAwardByType;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestGetTaskAwardBean;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.ResponseGetTaskAwardBean;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestGetTaskAwardTotal;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.ResponseGetTaskAwardTotal;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestGetTaskAwardList;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.ResponseGetTaskAwardList;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestAddTaskAward;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestUpdateTaskAward;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestGetTaskAward;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.ResponseGetTaskAward;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.RequestGetTaskAwardByTaskId;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.ResponseGetTaskAwardByTaskId;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.TaskAward;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.TaskAwardBean;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.AwardContentBean;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.Medal;
import fm.lizhi.hy.operating.protocol.AwardServiceProto.Guard;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface TaskAwardService {
	
	
	/**
	 *  根据类型获取奖励
	 *
	 * @param awardType
	 *            奖励类型：0勋章，1背景，2包裹，3礼物，4私信卡片
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 没有数据<br>
	 */
	@Service(domain = 20005, op = 30, request = RequestGetTaskAwardByType.class, response = ResponseGetTaskAwardByType.class)
	@Return(resultType = ResponseGetTaskAwardByType.class)
	Result<ResponseGetTaskAwardByType> getTaskAwardByType(@Attribute(name = "awardType") int awardType);
	
	
	/**
	 *  根据任务ID获取奖励
	 *
	 * @param taskId
	 *            任务ID
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 没有数据<br>
	 */
	@Service(domain = 20005, op = 31, request = RequestGetTaskAwardBean.class, response = ResponseGetTaskAwardBean.class)
	@Return(resultType = ResponseGetTaskAwardBean.class)
	Result<ResponseGetTaskAwardBean> getTaskAwardBean(@Attribute(name = "taskId") long taskId);
	
	
	/**
	 *  任务奖励列表
	 *
	 * @param taskId
	 *            任务ID
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 */
	@Service(domain = 20005, op = 32, request = RequestGetTaskAwardTotal.class, response = ResponseGetTaskAwardTotal.class)
	@Return(resultType = ResponseGetTaskAwardTotal.class)
	Result<ResponseGetTaskAwardTotal> getTaskAwardTotal(@Attribute(name = "taskId") long taskId);
	
	
	/**
	 *  任务奖励列表
	 *
	 * @param taskId
	 *            任务ID
	 * @param offset
	 *            偏移量
	 * @param count
	 *            数量
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 没有数据<br>
	 */
	@Service(domain = 20005, op = 33, request = RequestGetTaskAwardList.class, response = ResponseGetTaskAwardList.class)
	@Return(resultType = ResponseGetTaskAwardList.class)
	Result<ResponseGetTaskAwardList> getTaskAwardList(@Attribute(name = "taskId") long taskId, @Attribute(name = "offset") int offset, @Attribute(name = "count") int count);
	
	
	/**
	 *  新增任务奖励
	 *
	 * @param taskAward
	 *            任务奖励
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 新增失败<br>
	 */
	@Service(domain = 20005, op = 34, request = RequestAddTaskAward.class)
	@Return(resultType = Void.class)
	Result<Void> addTaskAward(@Attribute(name = "taskAward") TaskAward taskAward);
	
	
	/**
	 *  新增任务奖励
	 *
	 * @param taskAward
	 *            任务奖励
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 更新失败<br>
	 */
	@Service(domain = 20005, op = 35, request = RequestUpdateTaskAward.class)
	@Return(resultType = Void.class)
	Result<Void> updateTaskAward(@Attribute(name = "taskAward") TaskAward taskAward);
	
	
	/**
	 *  获取任务奖励
	 *
	 * @param id
	 *            ID
	 * @return 
	 *     //if rcode == 1 木有数据<br>
	 */
	@Service(domain = 20005, op = 36, request = RequestGetTaskAward.class, response = ResponseGetTaskAward.class)
	@Return(resultType = ResponseGetTaskAward.class)
	Result<ResponseGetTaskAward> getTaskAward(@Attribute(name = "id") long id);
	
	
	/**
	 *  获取任务Id获取任务奖励
	 *
	 * @param taskId
	 *            任务ID
	 * @return 
	 *     //if rcode == 1 木有数据<br>
	 */
	@Service(domain = 20005, op = 37, request = RequestGetTaskAwardByTaskId.class, response = ResponseGetTaskAwardByTaskId.class)
	@Return(resultType = ResponseGetTaskAwardByTaskId.class)
	Result<ResponseGetTaskAwardByTaskId> getTaskAwardByTaskId(@Attribute(name = "taskId") long taskId);
	
	
	public static final int GET_TASK_AWARD_BY_TYPE_INVALID_PARAM = 1; // 参数非法
	public static final int GET_TASK_AWARD_BY_TYPE_NO_DATA = 2; // 没有数据

	public static final int GET_TASK_AWARD_BEAN_INVALID_PARAM = 1; // 参数非法
	public static final int GET_TASK_AWARD_BEAN_NO_DATA = 2; // 没有数据

	public static final int GET_TASK_AWARD_TOTAL_INVALID_PARAM = 1; // 参数非法

	public static final int GET_TASK_AWARD_LIST_INVALID_PARAM = 1; // 参数非法
	public static final int GET_TASK_AWARD_LIST_NO_DATA = 2; // 没有数据

	public static final int ADD_TASK_AWARD_INVALID_PARAM = 1; // 参数非法
	public static final int ADD_TASK_AWARD_ADD_FAIL = 2; // 新增失败

	public static final int UPDATE_TASK_AWARD_INVALID_PARAM = 1; // 参数非法
	public static final int UPDATE_TASK_AWARD_UPDATE_FAIL = 2; // 更新失败

	public static final int GET_TASK_AWARD_NO_DATA = 1; // 木有数据

	public static final int GET_TASK_AWARD_BY_TASK_ID_NO_DATA = 1; // 木有数据


}