package fm.lizhi.hy.amusement.api;


import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestCreateDressUpGoods;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestQueryDressUpGoods;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.ResponseQueryDressUpGoods;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestUpdateDressUpGoods;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestGetDressUpGoodsOnMall;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.ResponseGetDressUpGoodsOnMall;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestGetDressUpTypeJson;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.ResponseGetDressUpTypeJson;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestGetUserDressUpByType;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.ResponseGetUserDressUpByType;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestBuyProduct;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.ResponseBuyProduct;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestOperateUserProduct;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.ResponseOperateUserProduct;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestAddUserRights;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.ResponseAddUserRights;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestPageGetUserRightsLog;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.ResponsePageGetUserRightsLog;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestGetTreasureTypeList;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.ResponseGetTreasureTypeList;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestGetTreasureList;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.ResponseGetTreasureList;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.RequestDelUserRights;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.ResponseDelUserRights;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.DressUpInfo;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.QueryDressUpGoodsParam;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.DressUpGoods;
import fm.lizhi.hy.amusement.protocol.DressUpGoodsProto.UpdateDressUpGoodsParam;

/**
 * this file generated by autoapi-maven-plugin
 *
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 *
 */
public interface DressUpGoodsService {

   /**
    *  创建装扮商品
    *
    * @param dressUpId    装扮id
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 401, request = RequestCreateDressUpGoods.class)
	@Return(resultType = Void.class)
	Result<Void> createDressUpGoods(@Attribute(name = "dressUpId") long dressUpId);

   /**
    *  查询装扮商品
    *
    * @param param    
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 402, request = RequestQueryDressUpGoods.class, response = ResponseQueryDressUpGoods.class)
	@Return(resultType = ResponseQueryDressUpGoods.class)
	Result<ResponseQueryDressUpGoods> queryDressUpGoods(@Attribute(name = "param") QueryDressUpGoodsParam param);

   /**
    *  更新装扮商品
    *
    * @param param    
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 403, request = RequestUpdateDressUpGoods.class)
	@Return(resultType = Void.class)
	Result<Void> updateDressUpGoods(@Attribute(name = "param") UpdateDressUpGoodsParam param);

   /**
    *  获取商城装扮商品
    *
    * @param dressUpType    
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 404, request = RequestGetDressUpGoodsOnMall.class, response = ResponseGetDressUpGoodsOnMall.class)
	@Return(resultType = ResponseGetDressUpGoodsOnMall.class)
	Result<ResponseGetDressUpGoodsOnMall> getDressUpGoodsOnMall(@Attribute(name = "dressUpType") int dressUpType);

   /**
    *  获取商城装扮类型，H5
    *
    * @param meUid    观测者uid
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 405, request = RequestGetDressUpTypeJson.class, response = ResponseGetDressUpTypeJson.class)
	@Return(resultType = ResponseGetDressUpTypeJson.class)
	Result<ResponseGetDressUpTypeJson> getDressUpTypeJson(@Attribute(name = "meUid") long meUid);

   /**
    *  获取用户装扮，H5
    *
    * @param uid    
    * @param typeId    装扮类型Id
    * @param mine    是否查询我的物品，0仅查商城，1额外查我的
    * @param pageNumber    页码从1开始
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 406, request = RequestGetUserDressUpByType.class, response = ResponseGetUserDressUpByType.class)
	@Return(resultType = ResponseGetUserDressUpByType.class)
	Result<ResponseGetUserDressUpByType> getUserDressUpByType(@Attribute(name = "uid") long uid, @Attribute(name = "typeId") long typeId, @Attribute(name = "mine") boolean mine, @Attribute(name = "pageNumber") int pageNumber);

   /**
    *  购买商品，H5
    *
    * @param uid    
    * @param dressUpId    
    * @param num    数量
    * @param mallDiscountId    装扮折扣id
    * @param mallDiscountUpdateTime    折扣更新时间戳
    * @param couponId    优惠券ID
    * @param mallDiscount    时长折扣率，辅助折扣校验时间，计算价格不会用到
    * @param frontBuyCoin    前端计算的金币，为了校验服务端计算的金额（前段计算金额会掉失精度）
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 407, request = RequestBuyProduct.class, response = ResponseBuyProduct.class)
	@Return(resultType = ResponseBuyProduct.class)
	Result<ResponseBuyProduct> buyProduct(@Attribute(name = "uid") long uid, @Attribute(name = "dressUpId") long dressUpId, @Attribute(name = "num") int num, @Attribute(name = "mallDiscountId") long mallDiscountId, @Attribute(name = "mallDiscountUpdateTime") long mallDiscountUpdateTime, @Attribute(name = "couponId") long couponId, @Attribute(name = "mallDiscount") String mallDiscount, @Attribute(name = "frontBuyCoin") int frontBuyCoin);

   /**
    *  商品操作，卸下佩戴，H5
    *
    * @param uid    
    * @param dressUpId    
    * @param op    0卸下，1佩戴
    * @param source    来源
    * @param liveId    直播间id
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 408, request = RequestOperateUserProduct.class, response = ResponseOperateUserProduct.class)
	@Return(resultType = ResponseOperateUserProduct.class)
	Result<ResponseOperateUserProduct> operateUserProduct(@Attribute(name = "uid") long uid, @Attribute(name = "dressUpId") long dressUpId, @Attribute(name = "op") int op, @Attribute(name = "source") String source, @Attribute(name = "liveId") String liveId);

   /**
    *  发放特权，立即生效
    *
    * @param uid    
    * @param dressUpId    
    * @param num    数量
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 409, request = RequestAddUserRights.class, response = ResponseAddUserRights.class)
	@Return(resultType = ResponseAddUserRights.class)
	Result<ResponseAddUserRights> addUserRights(@Attribute(name = "uid") long uid, @Attribute(name = "dressUpId") long dressUpId, @Attribute(name = "num") int num);

   /**
    *  查询用户特权流水记录
    *
    * @param uid    
    * @param pageNo    
    * @param pageSize    
    * @param orderBy    数据排序
    * @param createTimeStart    
    * @param createTimeEnd    
    * @param bizType    装扮类型
    * @param bizId    
    * @param type    发放类型。不传/-1：所有。0：平台订单数据，1：运营下发。3：自动发放
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 410, request = RequestPageGetUserRightsLog.class, response = ResponsePageGetUserRightsLog.class)
	@Return(resultType = ResponsePageGetUserRightsLog.class)
	Result<ResponsePageGetUserRightsLog> pageGetUserRightsLog(@Attribute(name = "uid") long uid, @Attribute(name = "pageNo") int pageNo, @Attribute(name = "pageSize") int pageSize, @Attribute(name = "orderBy") String orderBy, @Attribute(name = "createTimeStart") String createTimeStart, @Attribute(name = "createTimeEnd") String createTimeEnd, @Attribute(name = "bizType") String bizType, @Attribute(name = "bizId") long bizId, @Attribute(name = "type") int type);

   /**
    *  获取资料页珍宝类型
    *
    * @param tgtUid    
    * @param meUid    
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 411, request = RequestGetTreasureTypeList.class, response = ResponseGetTreasureTypeList.class)
	@Return(resultType = ResponseGetTreasureTypeList.class)
	Result<ResponseGetTreasureTypeList> getTreasureTypeList(@Attribute(name = "tgtUid") long tgtUid, @Attribute(name = "meUid") long meUid);

   /**
    *  获取资料页珍宝列表
    *
    * @param tgtUid    
    * @param typeId    
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 412, request = RequestGetTreasureList.class, response = ResponseGetTreasureList.class)
	@Return(resultType = ResponseGetTreasureList.class)
	Result<ResponseGetTreasureList> getTreasureList(@Attribute(name = "tgtUid") long tgtUid, @Attribute(name = "typeId") long typeId);

   /**
    *  过期特权
    *
    * @param uid    
    * @param dressUpId    
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 413, request = RequestDelUserRights.class, response = ResponseDelUserRights.class)
	@Return(resultType = ResponseDelUserRights.class)
	Result<ResponseDelUserRights> delUserRights(@Attribute(name = "uid") long uid, @Attribute(name = "dressUpId") long dressUpId);

	int CREATE_DRESS_UP_GOODS_ILLEGAL_PARAMS = 1; // 参数非法
	int CREATE_DRESS_UP_GOODS_FAIL = 2; // 失败

	int QUERY_DRESS_UP_GOODS_ILLEGAL_PARAMS = 1; // 参数非法
	int QUERY_DRESS_UP_GOODS_FAIL = 2; // 失败

	int UPDATE_DRESS_UP_GOODS_ILLEGAL_PARAMS = 1; // 参数非法
	int UPDATE_DRESS_UP_GOODS_FAIL = 2; // 失败

	int GET_DRESS_UP_GOODS_ON_MALL_ILLEGAL_PARAMS = 1; // 参数非法
	int GET_DRESS_UP_GOODS_ON_MALL_FAIL = 2; // 失败

	int GET_DRESS_UP_TYPE_JSON_ILLEGAL_PARAMS = 1; // 参数非法
	int GET_DRESS_UP_TYPE_JSON_FAIL = 2; // 失败

	int GET_USER_DRESS_UP_BY_TYPE_ILLEGAL_PARAMS = 1; // 参数非法
	int GET_USER_DRESS_UP_BY_TYPE_FAIL = 2; // 失败

	int BUY_PRODUCT_ILLEGAL_PARAMS = 1; // 参数非法
	int BUY_PRODUCT_FAIL = 2; // 失败

	int OPERATE_USER_PRODUCT_ILLEGAL_PARAMS = 1; // 参数非法
	int OPERATE_USER_PRODUCT_FAIL = 2; // 失败

	int ADD_USER_RIGHTS_ILLEGAL_PARAMS = 1; // 参数非法
	int ADD_USER_RIGHTS_FAIL = 2; // 失败

	int PAGE_GET_USER_RIGHTS_LOG_ILLEGAL_PARAMS = 1; // 参数非法
	int PAGE_GET_USER_RIGHTS_LOG_FAIL = 2; // 失败

	int GET_TREASURE_TYPE_LIST_ILLEGAL_PARAMS = 1; // 参数非法
	int GET_TREASURE_TYPE_LIST_FAIL = 2; // 失败

	int GET_TREASURE_LIST_ILLEGAL_PARAMS = 1; // 参数非法
	int GET_TREASURE_LIST_FAIL = 2; // 失败

	int DEL_USER_RIGHTS_ILLEGAL_PARAMS = 1; // 参数非法
	int DEL_USER_RIGHTS_FAIL = 2; // 失败

}