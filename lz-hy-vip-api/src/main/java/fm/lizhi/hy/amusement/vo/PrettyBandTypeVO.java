package fm.lizhi.hy.amusement.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 靓号类型。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrettyBandTypeVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    private String title;
    private List<PrettyBandTypeVO> children = new ArrayList<>();
}
