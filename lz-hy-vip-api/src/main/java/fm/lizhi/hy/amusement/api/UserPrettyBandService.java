package fm.lizhi.hy.amusement.api;


import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.RequestGetPrettyBandList;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.ResponseGetPrettyBandList;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.RequestUpdPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.ResponseUpdPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.RequestGetUserPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.ResponseGetUserPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.RequestGetUserPrettyBandRecord;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.ResponseGetUserPrettyBandRecord;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.RequestSendUserPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.ResponseSendUserPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.RequestGetUserPrettyBandStatus;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.ResponseGetUserPrettyBandStatus;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.RequestGetUserPrettyBandByBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.ResponseGetUserPrettyBandByBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.RequestImportPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.ResponseImportPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.RequestGetUserWearingPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.ResponseGetUserWearingPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.RequestCheckPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.ResponseCheckPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.RequestGetSendPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.ResponseGetSendPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.RequestAddUserPrettyExchangeAmount;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.ResponseAddUserPrettyExchangeAmount;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.UserPrettyBandInfo;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.QueryPrettyBandRecordParam;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.UserPrettyBandRecord;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.UserPrettyBand;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.UserPrettyBandBo;
import fm.lizhi.hy.amusement.protocol.UserPrettyBandProto.QueryPrettyBandParam;

/**
 * this file generated by autoapi-maven-plugin
 *
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 *
 */
public interface UserPrettyBandService {

   /**
    *  获取列表
    *
    * @param queryPrettyBandParam    查询参数
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 801, request = RequestGetPrettyBandList.class, response = ResponseGetPrettyBandList.class)
	@Return(resultType = ResponseGetPrettyBandList.class)
	Result<ResponseGetPrettyBandList> getPrettyBandList(@Attribute(name = "queryPrettyBandParam") QueryPrettyBandParam queryPrettyBandParam);

   /**
    *  更新靓号
    *
    * @param userPrettyBandList    靓号信息
    * @return
    *     //if rcode == 1 参数非法<br>
    *     //if rcode == 2 失败<br>
    */
	@Service(domain = 20005, op = 802, request = RequestUpdPrettyBand.class, response = ResponseUpdPrettyBand.class)
	@Return(resultType = ResponseUpdPrettyBand.class)
	Result<ResponseUpdPrettyBand> updPrettyBand(@Attribute(name = "userPrettyBandList", genericType = UserPrettyBand.class) List<UserPrettyBand> userPrettyBandList);

   /**
    *  查询用户当前使用的靓号
    *
    * @param userId    用户ID
    * @param band    用户波段号
    * @return
    */
	@Service(domain = 20005, op = 803, request = RequestGetUserPrettyBand.class, response = ResponseGetUserPrettyBand.class)
	@Return(resultType = ResponseGetUserPrettyBand.class)
	Result<ResponseGetUserPrettyBand> getUserPrettyBand(@Attribute(name = "userId") long userId, @Attribute(name = "band") String band);

   /**
    *  查询发放靓号列表
    *
    * @param queryPrettyBandRecordParam    查询参数
    * @return
    */
	@Service(domain = 20005, op = 804, request = RequestGetUserPrettyBandRecord.class, response = ResponseGetUserPrettyBandRecord.class)
	@Return(resultType = ResponseGetUserPrettyBandRecord.class)
	Result<ResponseGetUserPrettyBandRecord> getUserPrettyBandRecord(@Attribute(name = "queryPrettyBandRecordParam") QueryPrettyBandRecordParam queryPrettyBandRecordParam);

   /**
    *  发放靓号
    *
    * @param userPrettyBandRecord    查询参数
    * @return
    *     //if rcode == 1 已被占用,发放失败<br>
    *     //if rcode == 2 营销账号余额不足,发放失败<br>
    *     //if rcode == 3 营销账号支付异常<br>
    *     //if rcode == 4 靓号不存在<br>
    */
	@Service(domain = 20005, op = 805, request = RequestSendUserPrettyBand.class, response = ResponseSendUserPrettyBand.class)
	@Return(resultType = ResponseSendUserPrettyBand.class)
	Result<ResponseSendUserPrettyBand> sendUserPrettyBand(@Attribute(name = "userPrettyBandRecord") UserPrettyBandRecord userPrettyBandRecord);

   /**
    *  查询靓号占用状态
    *
    * @param prettyBand    用户波段号
    * @return
    */
	@Service(domain = 20005, op = 806, request = RequestGetUserPrettyBandStatus.class, response = ResponseGetUserPrettyBandStatus.class)
	@Return(resultType = ResponseGetUserPrettyBandStatus.class)
	Result<ResponseGetUserPrettyBandStatus> getUserPrettyBandStatus(@Attribute(name = "prettyBand") String prettyBand);

   /**
    *  查询靓号信息
    *
    * @param prettyBand    用户波段号
    * @return
    */
	@Service(domain = 20005, op = 807, request = RequestGetUserPrettyBandByBand.class, response = ResponseGetUserPrettyBandByBand.class)
	@Return(resultType = ResponseGetUserPrettyBandByBand.class)
	Result<ResponseGetUserPrettyBandByBand> getUserPrettyBandByBand(@Attribute(name = "prettyBand") String prettyBand);

   /**
    *  管理后台导入靓号
    *
    * @param prettyBands    用户波段号列表
    * @return
    */
	@Service(domain = 20005, op = 808, request = RequestImportPrettyBand.class, response = ResponseImportPrettyBand.class)
	@Return(resultType = ResponseImportPrettyBand.class)
	Result<ResponseImportPrettyBand> importPrettyBand(@Attribute(name = "prettyBands") String prettyBands);

   /**
    *  查询用户佩戴中靓号
    *
    * @param uid    
    * @return
    */
	@Service(domain = 20005, op = 809, request = RequestGetUserWearingPrettyBand.class, response = ResponseGetUserWearingPrettyBand.class)
	@Return(resultType = ResponseGetUserWearingPrettyBand.class)
	Result<ResponseGetUserWearingPrettyBand> getUserWearingPrettyBand(@Attribute(name = "uid") long uid);

   /**
    *  管理后台导入发放靓号前置校验
    *
    * @param prettyBands    用户波段号列表
    * @return
    */
	@Service(domain = 20005, op = 810, request = RequestCheckPrettyBand.class, response = ResponseCheckPrettyBand.class)
	@Return(resultType = ResponseCheckPrettyBand.class)
	Result<ResponseCheckPrettyBand> checkPrettyBand(@Attribute(name = "prettyBands") String prettyBands);

   /**
    *  导入发放靓号预览
    *
    * @param prettyBands    导入的波段号列表
    * @return
    */
	@Service(domain = 20005, op = 811, request = RequestGetSendPrettyBand.class, response = ResponseGetSendPrettyBand.class)
	@Return(resultType = ResponseGetSendPrettyBand.class)
	Result<ResponseGetSendPrettyBand> getSendPrettyBand(@Attribute(name = "prettyBands", genericType = String.class) List<String> prettyBands);

   /**
    *  增加用户兑换值
    *
    * @param userId    
    * @param addExchangeAmount    
    * @return
    */
	@Service(domain = 20005, op = 812, request = RequestAddUserPrettyExchangeAmount.class, response = ResponseAddUserPrettyExchangeAmount.class)
	@Return(resultType = ResponseAddUserPrettyExchangeAmount.class)
	Result<ResponseAddUserPrettyExchangeAmount> addUserPrettyExchangeAmount(@Attribute(name = "userId") long userId, @Attribute(name = "addExchangeAmount") int addExchangeAmount);

	int GET_PRETTY_BAND_LIST_ILLEGAL_PARAMS = 1; // 参数非法
	int GET_PRETTY_BAND_LIST_FAIL = 2; // 失败

	int UPD_PRETTY_BAND_ILLEGAL_PARAMS = 1; // 参数非法
	int UPD_PRETTY_BAND_FAIL = 2; // 失败

	int SEND_USER_PRETTY_BAND_ILLEGAL_PARAMS = 1; // 已被占用,发放失败
	int SEND_USER_PRETTY_BAND_ACCOUNT_NOT_ENONGH = 2; // 营销账号余额不足,发放失败
	int SEND_USER_PRETTY_BAND_PAY_ERROR = 3; // 营销账号支付异常
	int SEND_USER_PRETTY_BAND_BAND_NOT_EXIST = 4; // 靓号不存在

}