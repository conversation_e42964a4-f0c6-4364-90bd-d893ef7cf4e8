// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: star_level.proto

package fm.lizhi.hy.vip.protocol;

public final class StarLevelProto {
  private StarLevelProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface PrivilegeTypeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int32 condition = 1;
    /**
     * <code>optional int32 condition = 1;</code>
     *
     * <pre>
     *特权开启等级
     * </pre>
     */
    boolean hasCondition();
    /**
     * <code>optional int32 condition = 1;</code>
     *
     * <pre>
     *特权开启等级
     * </pre>
     */
    int getCondition();

    // optional string title = 2;
    /**
     * <code>optional string title = 2;</code>
     *
     * <pre>
     *特权名称
     * </pre>
     */
    boolean hasTitle();
    /**
     * <code>optional string title = 2;</code>
     *
     * <pre>
     *特权名称
     * </pre>
     */
    java.lang.String getTitle();
    /**
     * <code>optional string title = 2;</code>
     *
     * <pre>
     *特权名称
     * </pre>
     */
    com.google.protobuf.ByteString
        getTitleBytes();
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.PrivilegeType}
   *
   * <pre>
   * 明星等级特权
   * </pre>
   */
  public static final class PrivilegeType extends
      com.google.protobuf.GeneratedMessage
      implements PrivilegeTypeOrBuilder {
    // Use PrivilegeType.newBuilder() to construct.
    private PrivilegeType(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PrivilegeType(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PrivilegeType defaultInstance;
    public static PrivilegeType getDefaultInstance() {
      return defaultInstance;
    }

    public PrivilegeType getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PrivilegeType(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              condition_ = input.readInt32();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              title_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_PrivilegeType_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_PrivilegeType_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.class, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.Builder.class);
    }

    public static com.google.protobuf.Parser<PrivilegeType> PARSER =
        new com.google.protobuf.AbstractParser<PrivilegeType>() {
      public PrivilegeType parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PrivilegeType(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PrivilegeType> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int32 condition = 1;
    public static final int CONDITION_FIELD_NUMBER = 1;
    private int condition_;
    /**
     * <code>optional int32 condition = 1;</code>
     *
     * <pre>
     *特权开启等级
     * </pre>
     */
    public boolean hasCondition() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int32 condition = 1;</code>
     *
     * <pre>
     *特权开启等级
     * </pre>
     */
    public int getCondition() {
      return condition_;
    }

    // optional string title = 2;
    public static final int TITLE_FIELD_NUMBER = 2;
    private java.lang.Object title_;
    /**
     * <code>optional string title = 2;</code>
     *
     * <pre>
     *特权名称
     * </pre>
     */
    public boolean hasTitle() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string title = 2;</code>
     *
     * <pre>
     *特权名称
     * </pre>
     */
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          title_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string title = 2;</code>
     *
     * <pre>
     *特权名称
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      condition_ = 0;
      title_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, condition_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getTitleBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, condition_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getTitleBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.PrivilegeType}
     *
     * <pre>
     * 明星等级特权
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeTypeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_PrivilegeType_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_PrivilegeType_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.class, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        condition_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        title_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_PrivilegeType_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType build() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType buildPartial() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType result = new fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.condition_ = condition_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.title_ = title_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType other) {
        if (other == fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.getDefaultInstance()) return this;
        if (other.hasCondition()) {
          setCondition(other.getCondition());
        }
        if (other.hasTitle()) {
          bitField0_ |= 0x00000002;
          title_ = other.title_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int32 condition = 1;
      private int condition_ ;
      /**
       * <code>optional int32 condition = 1;</code>
       *
       * <pre>
       *特权开启等级
       * </pre>
       */
      public boolean hasCondition() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int32 condition = 1;</code>
       *
       * <pre>
       *特权开启等级
       * </pre>
       */
      public int getCondition() {
        return condition_;
      }
      /**
       * <code>optional int32 condition = 1;</code>
       *
       * <pre>
       *特权开启等级
       * </pre>
       */
      public Builder setCondition(int value) {
        bitField0_ |= 0x00000001;
        condition_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 condition = 1;</code>
       *
       * <pre>
       *特权开启等级
       * </pre>
       */
      public Builder clearCondition() {
        bitField0_ = (bitField0_ & ~0x00000001);
        condition_ = 0;
        onChanged();
        return this;
      }

      // optional string title = 2;
      private java.lang.Object title_ = "";
      /**
       * <code>optional string title = 2;</code>
       *
       * <pre>
       *特权名称
       * </pre>
       */
      public boolean hasTitle() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string title = 2;</code>
       *
       * <pre>
       *特权名称
       * </pre>
       */
      public java.lang.String getTitle() {
        java.lang.Object ref = title_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          title_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string title = 2;</code>
       *
       * <pre>
       *特权名称
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTitleBytes() {
        java.lang.Object ref = title_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          title_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string title = 2;</code>
       *
       * <pre>
       *特权名称
       * </pre>
       */
      public Builder setTitle(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        title_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string title = 2;</code>
       *
       * <pre>
       *特权名称
       * </pre>
       */
      public Builder clearTitle() {
        bitField0_ = (bitField0_ & ~0x00000002);
        title_ = getDefaultInstance().getTitle();
        onChanged();
        return this;
      }
      /**
       * <code>optional string title = 2;</code>
       *
       * <pre>
       *特权名称
       * </pre>
       */
      public Builder setTitleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        title_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.PrivilegeType)
    }

    static {
      defaultInstance = new PrivilegeType(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.PrivilegeType)
  }

  public interface RequestGetStarGradeInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    long getUserId();
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.RequestGetStarGradeInfo}
   *
   * <pre>
   * StarLevelService.java
   * 获取明星等级信息请求
   * domain = 20005, op = 261
   * </pre>
   */
  public static final class RequestGetStarGradeInfo extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetStarGradeInfoOrBuilder {
    // Use RequestGetStarGradeInfo.newBuilder() to construct.
    private RequestGetStarGradeInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetStarGradeInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetStarGradeInfo defaultInstance;
    public static RequestGetStarGradeInfo getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetStarGradeInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetStarGradeInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestGetStarGradeInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestGetStarGradeInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo.class, fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetStarGradeInfo> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetStarGradeInfo>() {
      public RequestGetStarGradeInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetStarGradeInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetStarGradeInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    private void initFields() {
      userId_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.RequestGetStarGradeInfo}
     *
     * <pre>
     * StarLevelService.java
     * 获取明星等级信息请求
     * domain = 20005, op = 261
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestGetStarGradeInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestGetStarGradeInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo.class, fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestGetStarGradeInfo_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo build() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo buildPartial() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo result = new fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo other) {
        if (other == fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetStarGradeInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.RequestGetStarGradeInfo)
    }

    static {
      defaultInstance = new RequestGetStarGradeInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.RequestGetStarGradeInfo)
  }

  public interface ResponseGetStarGradeInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    long getUserId();

    // optional int32 grade = 2;
    /**
     * <code>optional int32 grade = 2;</code>
     *
     * <pre>
     *当前等级
     * </pre>
     */
    boolean hasGrade();
    /**
     * <code>optional int32 grade = 2;</code>
     *
     * <pre>
     *当前等级
     * </pre>
     */
    int getGrade();

    // optional int64 experience = 3;
    /**
     * <code>optional int64 experience = 3;</code>
     *
     * <pre>
     *当前经验值
     * </pre>
     */
    boolean hasExperience();
    /**
     * <code>optional int64 experience = 3;</code>
     *
     * <pre>
     *当前经验值
     * </pre>
     */
    long getExperience();

    // optional int64 nextLevelValue = 4;
    /**
     * <code>optional int64 nextLevelValue = 4;</code>
     *
     * <pre>
     *下一级经验值
     * </pre>
     */
    boolean hasNextLevelValue();
    /**
     * <code>optional int64 nextLevelValue = 4;</code>
     *
     * <pre>
     *下一级经验值
     * </pre>
     */
    long getNextLevelValue();

    // optional int32 upgradeProgress = 5;
    /**
     * <code>optional int32 upgradeProgress = 5;</code>
     *
     * <pre>
     *升级进度 0-99
     * </pre>
     */
    boolean hasUpgradeProgress();
    /**
     * <code>optional int32 upgradeProgress = 5;</code>
     *
     * <pre>
     *升级进度 0-99
     * </pre>
     */
    int getUpgradeProgress();

    // optional int64 distanceExpreience = 6;
    /**
     * <code>optional int64 distanceExpreience = 6;</code>
     *
     * <pre>
     *距离升级差的经验值
     * </pre>
     */
    boolean hasDistanceExpreience();
    /**
     * <code>optional int64 distanceExpreience = 6;</code>
     *
     * <pre>
     *距离升级差的经验值
     * </pre>
     */
    long getDistanceExpreience();

    // optional string starLevelBadge = 7;
    /**
     * <code>optional string starLevelBadge = 7;</code>
     *
     * <pre>
     *明星等级图标
     * </pre>
     */
    boolean hasStarLevelBadge();
    /**
     * <code>optional string starLevelBadge = 7;</code>
     *
     * <pre>
     *明星等级图标
     * </pre>
     */
    java.lang.String getStarLevelBadge();
    /**
     * <code>optional string starLevelBadge = 7;</code>
     *
     * <pre>
     *明星等级图标
     * </pre>
     */
    com.google.protobuf.ByteString
        getStarLevelBadgeBytes();

    // optional float starLevelBadgeAspect = 8;
    /**
     * <code>optional float starLevelBadgeAspect = 8;</code>
     *
     * <pre>
     *明星等级图标宽高比
     * </pre>
     */
    boolean hasStarLevelBadgeAspect();
    /**
     * <code>optional float starLevelBadgeAspect = 8;</code>
     *
     * <pre>
     *明星等级图标宽高比
     * </pre>
     */
    float getStarLevelBadgeAspect();
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.ResponseGetStarGradeInfo}
   */
  public static final class ResponseGetStarGradeInfo extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetStarGradeInfoOrBuilder {
    // Use ResponseGetStarGradeInfo.newBuilder() to construct.
    private ResponseGetStarGradeInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetStarGradeInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetStarGradeInfo defaultInstance;
    public static ResponseGetStarGradeInfo getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetStarGradeInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetStarGradeInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              grade_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              experience_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              nextLevelValue_ = input.readInt64();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              upgradeProgress_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              distanceExpreience_ = input.readInt64();
              break;
            }
            case 58: {
              bitField0_ |= 0x00000040;
              starLevelBadge_ = input.readBytes();
              break;
            }
            case 69: {
              bitField0_ |= 0x00000080;
              starLevelBadgeAspect_ = input.readFloat();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseGetStarGradeInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseGetStarGradeInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo.class, fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetStarGradeInfo> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetStarGradeInfo>() {
      public ResponseGetStarGradeInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetStarGradeInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetStarGradeInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *用户ID
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    // optional int32 grade = 2;
    public static final int GRADE_FIELD_NUMBER = 2;
    private int grade_;
    /**
     * <code>optional int32 grade = 2;</code>
     *
     * <pre>
     *当前等级
     * </pre>
     */
    public boolean hasGrade() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 grade = 2;</code>
     *
     * <pre>
     *当前等级
     * </pre>
     */
    public int getGrade() {
      return grade_;
    }

    // optional int64 experience = 3;
    public static final int EXPERIENCE_FIELD_NUMBER = 3;
    private long experience_;
    /**
     * <code>optional int64 experience = 3;</code>
     *
     * <pre>
     *当前经验值
     * </pre>
     */
    public boolean hasExperience() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int64 experience = 3;</code>
     *
     * <pre>
     *当前经验值
     * </pre>
     */
    public long getExperience() {
      return experience_;
    }

    // optional int64 nextLevelValue = 4;
    public static final int NEXTLEVELVALUE_FIELD_NUMBER = 4;
    private long nextLevelValue_;
    /**
     * <code>optional int64 nextLevelValue = 4;</code>
     *
     * <pre>
     *下一级经验值
     * </pre>
     */
    public boolean hasNextLevelValue() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int64 nextLevelValue = 4;</code>
     *
     * <pre>
     *下一级经验值
     * </pre>
     */
    public long getNextLevelValue() {
      return nextLevelValue_;
    }

    // optional int32 upgradeProgress = 5;
    public static final int UPGRADEPROGRESS_FIELD_NUMBER = 5;
    private int upgradeProgress_;
    /**
     * <code>optional int32 upgradeProgress = 5;</code>
     *
     * <pre>
     *升级进度 0-99
     * </pre>
     */
    public boolean hasUpgradeProgress() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 upgradeProgress = 5;</code>
     *
     * <pre>
     *升级进度 0-99
     * </pre>
     */
    public int getUpgradeProgress() {
      return upgradeProgress_;
    }

    // optional int64 distanceExpreience = 6;
    public static final int DISTANCEEXPREIENCE_FIELD_NUMBER = 6;
    private long distanceExpreience_;
    /**
     * <code>optional int64 distanceExpreience = 6;</code>
     *
     * <pre>
     *距离升级差的经验值
     * </pre>
     */
    public boolean hasDistanceExpreience() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional int64 distanceExpreience = 6;</code>
     *
     * <pre>
     *距离升级差的经验值
     * </pre>
     */
    public long getDistanceExpreience() {
      return distanceExpreience_;
    }

    // optional string starLevelBadge = 7;
    public static final int STARLEVELBADGE_FIELD_NUMBER = 7;
    private java.lang.Object starLevelBadge_;
    /**
     * <code>optional string starLevelBadge = 7;</code>
     *
     * <pre>
     *明星等级图标
     * </pre>
     */
    public boolean hasStarLevelBadge() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional string starLevelBadge = 7;</code>
     *
     * <pre>
     *明星等级图标
     * </pre>
     */
    public java.lang.String getStarLevelBadge() {
      java.lang.Object ref = starLevelBadge_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          starLevelBadge_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string starLevelBadge = 7;</code>
     *
     * <pre>
     *明星等级图标
     * </pre>
     */
    public com.google.protobuf.ByteString
        getStarLevelBadgeBytes() {
      java.lang.Object ref = starLevelBadge_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        starLevelBadge_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional float starLevelBadgeAspect = 8;
    public static final int STARLEVELBADGEASPECT_FIELD_NUMBER = 8;
    private float starLevelBadgeAspect_;
    /**
     * <code>optional float starLevelBadgeAspect = 8;</code>
     *
     * <pre>
     *明星等级图标宽高比
     * </pre>
     */
    public boolean hasStarLevelBadgeAspect() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional float starLevelBadgeAspect = 8;</code>
     *
     * <pre>
     *明星等级图标宽高比
     * </pre>
     */
    public float getStarLevelBadgeAspect() {
      return starLevelBadgeAspect_;
    }

    private void initFields() {
      userId_ = 0L;
      grade_ = 0;
      experience_ = 0L;
      nextLevelValue_ = 0L;
      upgradeProgress_ = 0;
      distanceExpreience_ = 0L;
      starLevelBadge_ = "";
      starLevelBadgeAspect_ = 0F;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, grade_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(3, experience_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt64(4, nextLevelValue_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, upgradeProgress_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt64(6, distanceExpreience_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeBytes(7, getStarLevelBadgeBytes());
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeFloat(8, starLevelBadgeAspect_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, grade_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, experience_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, nextLevelValue_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, upgradeProgress_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, distanceExpreience_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, getStarLevelBadgeBytes());
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(8, starLevelBadgeAspect_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.ResponseGetStarGradeInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseGetStarGradeInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseGetStarGradeInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo.class, fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        grade_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        experience_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        nextLevelValue_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        upgradeProgress_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        distanceExpreience_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        starLevelBadge_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        starLevelBadgeAspect_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseGetStarGradeInfo_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo build() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo buildPartial() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo result = new fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.grade_ = grade_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.experience_ = experience_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.nextLevelValue_ = nextLevelValue_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.upgradeProgress_ = upgradeProgress_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.distanceExpreience_ = distanceExpreience_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.starLevelBadge_ = starLevelBadge_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.starLevelBadgeAspect_ = starLevelBadgeAspect_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo other) {
        if (other == fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasGrade()) {
          setGrade(other.getGrade());
        }
        if (other.hasExperience()) {
          setExperience(other.getExperience());
        }
        if (other.hasNextLevelValue()) {
          setNextLevelValue(other.getNextLevelValue());
        }
        if (other.hasUpgradeProgress()) {
          setUpgradeProgress(other.getUpgradeProgress());
        }
        if (other.hasDistanceExpreience()) {
          setDistanceExpreience(other.getDistanceExpreience());
        }
        if (other.hasStarLevelBadge()) {
          bitField0_ |= 0x00000040;
          starLevelBadge_ = other.starLevelBadge_;
          onChanged();
        }
        if (other.hasStarLevelBadgeAspect()) {
          setStarLevelBadgeAspect(other.getStarLevelBadgeAspect());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetStarGradeInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *用户ID
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 grade = 2;
      private int grade_ ;
      /**
       * <code>optional int32 grade = 2;</code>
       *
       * <pre>
       *当前等级
       * </pre>
       */
      public boolean hasGrade() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 grade = 2;</code>
       *
       * <pre>
       *当前等级
       * </pre>
       */
      public int getGrade() {
        return grade_;
      }
      /**
       * <code>optional int32 grade = 2;</code>
       *
       * <pre>
       *当前等级
       * </pre>
       */
      public Builder setGrade(int value) {
        bitField0_ |= 0x00000002;
        grade_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 grade = 2;</code>
       *
       * <pre>
       *当前等级
       * </pre>
       */
      public Builder clearGrade() {
        bitField0_ = (bitField0_ & ~0x00000002);
        grade_ = 0;
        onChanged();
        return this;
      }

      // optional int64 experience = 3;
      private long experience_ ;
      /**
       * <code>optional int64 experience = 3;</code>
       *
       * <pre>
       *当前经验值
       * </pre>
       */
      public boolean hasExperience() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int64 experience = 3;</code>
       *
       * <pre>
       *当前经验值
       * </pre>
       */
      public long getExperience() {
        return experience_;
      }
      /**
       * <code>optional int64 experience = 3;</code>
       *
       * <pre>
       *当前经验值
       * </pre>
       */
      public Builder setExperience(long value) {
        bitField0_ |= 0x00000004;
        experience_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 experience = 3;</code>
       *
       * <pre>
       *当前经验值
       * </pre>
       */
      public Builder clearExperience() {
        bitField0_ = (bitField0_ & ~0x00000004);
        experience_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 nextLevelValue = 4;
      private long nextLevelValue_ ;
      /**
       * <code>optional int64 nextLevelValue = 4;</code>
       *
       * <pre>
       *下一级经验值
       * </pre>
       */
      public boolean hasNextLevelValue() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int64 nextLevelValue = 4;</code>
       *
       * <pre>
       *下一级经验值
       * </pre>
       */
      public long getNextLevelValue() {
        return nextLevelValue_;
      }
      /**
       * <code>optional int64 nextLevelValue = 4;</code>
       *
       * <pre>
       *下一级经验值
       * </pre>
       */
      public Builder setNextLevelValue(long value) {
        bitField0_ |= 0x00000008;
        nextLevelValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 nextLevelValue = 4;</code>
       *
       * <pre>
       *下一级经验值
       * </pre>
       */
      public Builder clearNextLevelValue() {
        bitField0_ = (bitField0_ & ~0x00000008);
        nextLevelValue_ = 0L;
        onChanged();
        return this;
      }

      // optional int32 upgradeProgress = 5;
      private int upgradeProgress_ ;
      /**
       * <code>optional int32 upgradeProgress = 5;</code>
       *
       * <pre>
       *升级进度 0-99
       * </pre>
       */
      public boolean hasUpgradeProgress() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 upgradeProgress = 5;</code>
       *
       * <pre>
       *升级进度 0-99
       * </pre>
       */
      public int getUpgradeProgress() {
        return upgradeProgress_;
      }
      /**
       * <code>optional int32 upgradeProgress = 5;</code>
       *
       * <pre>
       *升级进度 0-99
       * </pre>
       */
      public Builder setUpgradeProgress(int value) {
        bitField0_ |= 0x00000010;
        upgradeProgress_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 upgradeProgress = 5;</code>
       *
       * <pre>
       *升级进度 0-99
       * </pre>
       */
      public Builder clearUpgradeProgress() {
        bitField0_ = (bitField0_ & ~0x00000010);
        upgradeProgress_ = 0;
        onChanged();
        return this;
      }

      // optional int64 distanceExpreience = 6;
      private long distanceExpreience_ ;
      /**
       * <code>optional int64 distanceExpreience = 6;</code>
       *
       * <pre>
       *距离升级差的经验值
       * </pre>
       */
      public boolean hasDistanceExpreience() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional int64 distanceExpreience = 6;</code>
       *
       * <pre>
       *距离升级差的经验值
       * </pre>
       */
      public long getDistanceExpreience() {
        return distanceExpreience_;
      }
      /**
       * <code>optional int64 distanceExpreience = 6;</code>
       *
       * <pre>
       *距离升级差的经验值
       * </pre>
       */
      public Builder setDistanceExpreience(long value) {
        bitField0_ |= 0x00000020;
        distanceExpreience_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 distanceExpreience = 6;</code>
       *
       * <pre>
       *距离升级差的经验值
       * </pre>
       */
      public Builder clearDistanceExpreience() {
        bitField0_ = (bitField0_ & ~0x00000020);
        distanceExpreience_ = 0L;
        onChanged();
        return this;
      }

      // optional string starLevelBadge = 7;
      private java.lang.Object starLevelBadge_ = "";
      /**
       * <code>optional string starLevelBadge = 7;</code>
       *
       * <pre>
       *明星等级图标
       * </pre>
       */
      public boolean hasStarLevelBadge() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional string starLevelBadge = 7;</code>
       *
       * <pre>
       *明星等级图标
       * </pre>
       */
      public java.lang.String getStarLevelBadge() {
        java.lang.Object ref = starLevelBadge_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          starLevelBadge_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string starLevelBadge = 7;</code>
       *
       * <pre>
       *明星等级图标
       * </pre>
       */
      public com.google.protobuf.ByteString
          getStarLevelBadgeBytes() {
        java.lang.Object ref = starLevelBadge_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          starLevelBadge_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string starLevelBadge = 7;</code>
       *
       * <pre>
       *明星等级图标
       * </pre>
       */
      public Builder setStarLevelBadge(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        starLevelBadge_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string starLevelBadge = 7;</code>
       *
       * <pre>
       *明星等级图标
       * </pre>
       */
      public Builder clearStarLevelBadge() {
        bitField0_ = (bitField0_ & ~0x00000040);
        starLevelBadge_ = getDefaultInstance().getStarLevelBadge();
        onChanged();
        return this;
      }
      /**
       * <code>optional string starLevelBadge = 7;</code>
       *
       * <pre>
       *明星等级图标
       * </pre>
       */
      public Builder setStarLevelBadgeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        starLevelBadge_ = value;
        onChanged();
        return this;
      }

      // optional float starLevelBadgeAspect = 8;
      private float starLevelBadgeAspect_ ;
      /**
       * <code>optional float starLevelBadgeAspect = 8;</code>
       *
       * <pre>
       *明星等级图标宽高比
       * </pre>
       */
      public boolean hasStarLevelBadgeAspect() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional float starLevelBadgeAspect = 8;</code>
       *
       * <pre>
       *明星等级图标宽高比
       * </pre>
       */
      public float getStarLevelBadgeAspect() {
        return starLevelBadgeAspect_;
      }
      /**
       * <code>optional float starLevelBadgeAspect = 8;</code>
       *
       * <pre>
       *明星等级图标宽高比
       * </pre>
       */
      public Builder setStarLevelBadgeAspect(float value) {
        bitField0_ |= 0x00000080;
        starLevelBadgeAspect_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional float starLevelBadgeAspect = 8;</code>
       *
       * <pre>
       *明星等级图标宽高比
       * </pre>
       */
      public Builder clearStarLevelBadgeAspect() {
        bitField0_ = (bitField0_ & ~0x00000080);
        starLevelBadgeAspect_ = 0F;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.ResponseGetStarGradeInfo)
    }

    static {
      defaultInstance = new ResponseGetStarGradeInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.ResponseGetStarGradeInfo)
  }

  public interface RequestAddStarValueOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *主播id
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *主播id
     * </pre>
     */
    long getUserId();

    // optional int64 coinAmount = 2;
    /**
     * <code>optional int64 coinAmount = 2;</code>
     *
     * <pre>
     *金币数
     * </pre>
     */
    boolean hasCoinAmount();
    /**
     * <code>optional int64 coinAmount = 2;</code>
     *
     * <pre>
     *金币数
     * </pre>
     */
    long getCoinAmount();

    // optional int64 starValue = 3;
    /**
     * <code>optional int64 starValue = 3;</code>
     *
     * <pre>
     * 经验值
     * </pre>
     */
    boolean hasStarValue();
    /**
     * <code>optional int64 starValue = 3;</code>
     *
     * <pre>
     * 经验值
     * </pre>
     */
    long getStarValue();
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.RequestAddStarValue}
   *
   * <pre>
   * StarLevelService.java
   * 增加经验值
   * domain = 20005, op = 262
   * </pre>
   */
  public static final class RequestAddStarValue extends
      com.google.protobuf.GeneratedMessage
      implements RequestAddStarValueOrBuilder {
    // Use RequestAddStarValue.newBuilder() to construct.
    private RequestAddStarValue(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestAddStarValue(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestAddStarValue defaultInstance;
    public static RequestAddStarValue getDefaultInstance() {
      return defaultInstance;
    }

    public RequestAddStarValue getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestAddStarValue(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              coinAmount_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              starValue_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestAddStarValue_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestAddStarValue_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue.class, fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestAddStarValue> PARSER =
        new com.google.protobuf.AbstractParser<RequestAddStarValue>() {
      public RequestAddStarValue parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestAddStarValue(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestAddStarValue> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *主播id
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *主播id
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    // optional int64 coinAmount = 2;
    public static final int COINAMOUNT_FIELD_NUMBER = 2;
    private long coinAmount_;
    /**
     * <code>optional int64 coinAmount = 2;</code>
     *
     * <pre>
     *金币数
     * </pre>
     */
    public boolean hasCoinAmount() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 coinAmount = 2;</code>
     *
     * <pre>
     *金币数
     * </pre>
     */
    public long getCoinAmount() {
      return coinAmount_;
    }

    // optional int64 starValue = 3;
    public static final int STARVALUE_FIELD_NUMBER = 3;
    private long starValue_;
    /**
     * <code>optional int64 starValue = 3;</code>
     *
     * <pre>
     * 经验值
     * </pre>
     */
    public boolean hasStarValue() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int64 starValue = 3;</code>
     *
     * <pre>
     * 经验值
     * </pre>
     */
    public long getStarValue() {
      return starValue_;
    }

    private void initFields() {
      userId_ = 0L;
      coinAmount_ = 0L;
      starValue_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, coinAmount_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(3, starValue_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, coinAmount_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, starValue_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.RequestAddStarValue}
     *
     * <pre>
     * StarLevelService.java
     * 增加经验值
     * domain = 20005, op = 262
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValueOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestAddStarValue_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestAddStarValue_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue.class, fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        coinAmount_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        starValue_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestAddStarValue_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue build() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue buildPartial() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue result = new fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.coinAmount_ = coinAmount_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.starValue_ = starValue_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue other) {
        if (other == fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        if (other.hasCoinAmount()) {
          setCoinAmount(other.getCoinAmount());
        }
        if (other.hasStarValue()) {
          setStarValue(other.getStarValue());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.StarLevelProto.RequestAddStarValue) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *主播id
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *主播id
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *主播id
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *主播id
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 coinAmount = 2;
      private long coinAmount_ ;
      /**
       * <code>optional int64 coinAmount = 2;</code>
       *
       * <pre>
       *金币数
       * </pre>
       */
      public boolean hasCoinAmount() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 coinAmount = 2;</code>
       *
       * <pre>
       *金币数
       * </pre>
       */
      public long getCoinAmount() {
        return coinAmount_;
      }
      /**
       * <code>optional int64 coinAmount = 2;</code>
       *
       * <pre>
       *金币数
       * </pre>
       */
      public Builder setCoinAmount(long value) {
        bitField0_ |= 0x00000002;
        coinAmount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 coinAmount = 2;</code>
       *
       * <pre>
       *金币数
       * </pre>
       */
      public Builder clearCoinAmount() {
        bitField0_ = (bitField0_ & ~0x00000002);
        coinAmount_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 starValue = 3;
      private long starValue_ ;
      /**
       * <code>optional int64 starValue = 3;</code>
       *
       * <pre>
       * 经验值
       * </pre>
       */
      public boolean hasStarValue() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int64 starValue = 3;</code>
       *
       * <pre>
       * 经验值
       * </pre>
       */
      public long getStarValue() {
        return starValue_;
      }
      /**
       * <code>optional int64 starValue = 3;</code>
       *
       * <pre>
       * 经验值
       * </pre>
       */
      public Builder setStarValue(long value) {
        bitField0_ |= 0x00000004;
        starValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 starValue = 3;</code>
       *
       * <pre>
       * 经验值
       * </pre>
       */
      public Builder clearStarValue() {
        bitField0_ = (bitField0_ & ~0x00000004);
        starValue_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.RequestAddStarValue)
    }

    static {
      defaultInstance = new RequestAddStarValue(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.RequestAddStarValue)
  }

  public interface RequestGetPrivilegeListOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.RequestGetPrivilegeList}
   *
   * <pre>
   * StarLevelService.java
   * 获取明星等级特权列表
   * domain = 20005, op = 263
   * </pre>
   */
  public static final class RequestGetPrivilegeList extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetPrivilegeListOrBuilder {
    // Use RequestGetPrivilegeList.newBuilder() to construct.
    private RequestGetPrivilegeList(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetPrivilegeList(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetPrivilegeList defaultInstance;
    public static RequestGetPrivilegeList getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetPrivilegeList getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetPrivilegeList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestGetPrivilegeList_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestGetPrivilegeList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList.class, fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetPrivilegeList> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetPrivilegeList>() {
      public RequestGetPrivilegeList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetPrivilegeList(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetPrivilegeList> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.RequestGetPrivilegeList}
     *
     * <pre>
     * StarLevelService.java
     * 获取明星等级特权列表
     * domain = 20005, op = 263
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestGetPrivilegeList_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestGetPrivilegeList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList.class, fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestGetPrivilegeList_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList build() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList buildPartial() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList result = new fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList other) {
        if (other == fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.StarLevelProto.RequestGetPrivilegeList) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.RequestGetPrivilegeList)
    }

    static {
      defaultInstance = new RequestGetPrivilegeList(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.RequestGetPrivilegeList)
  }

  public interface ResponseGetPrivilegeListOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;
    /**
     * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
     */
    java.util.List<fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType> 
        getPrivilegeTypeList();
    /**
     * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
     */
    fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType getPrivilegeType(int index);
    /**
     * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
     */
    int getPrivilegeTypeCount();
    /**
     * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
     */
    java.util.List<? extends fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeTypeOrBuilder> 
        getPrivilegeTypeOrBuilderList();
    /**
     * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
     */
    fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeTypeOrBuilder getPrivilegeTypeOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.ResponseGetPrivilegeList}
   */
  public static final class ResponseGetPrivilegeList extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetPrivilegeListOrBuilder {
    // Use ResponseGetPrivilegeList.newBuilder() to construct.
    private ResponseGetPrivilegeList(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetPrivilegeList(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetPrivilegeList defaultInstance;
    public static ResponseGetPrivilegeList getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetPrivilegeList getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetPrivilegeList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                privilegeType_ = new java.util.ArrayList<fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType>();
                mutable_bitField0_ |= 0x00000001;
              }
              privilegeType_.add(input.readMessage(fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          privilegeType_ = java.util.Collections.unmodifiableList(privilegeType_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseGetPrivilegeList_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseGetPrivilegeList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList.class, fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetPrivilegeList> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetPrivilegeList>() {
      public ResponseGetPrivilegeList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetPrivilegeList(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetPrivilegeList> getParserForType() {
      return PARSER;
    }

    // repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;
    public static final int PRIVILEGETYPE_FIELD_NUMBER = 1;
    private java.util.List<fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType> privilegeType_;
    /**
     * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
     */
    public java.util.List<fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType> getPrivilegeTypeList() {
      return privilegeType_;
    }
    /**
     * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
     */
    public java.util.List<? extends fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeTypeOrBuilder> 
        getPrivilegeTypeOrBuilderList() {
      return privilegeType_;
    }
    /**
     * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
     */
    public int getPrivilegeTypeCount() {
      return privilegeType_.size();
    }
    /**
     * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
     */
    public fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType getPrivilegeType(int index) {
      return privilegeType_.get(index);
    }
    /**
     * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
     */
    public fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeTypeOrBuilder getPrivilegeTypeOrBuilder(
        int index) {
      return privilegeType_.get(index);
    }

    private void initFields() {
      privilegeType_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < privilegeType_.size(); i++) {
        output.writeMessage(1, privilegeType_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < privilegeType_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, privilegeType_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.ResponseGetPrivilegeList}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseGetPrivilegeList_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseGetPrivilegeList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList.class, fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getPrivilegeTypeFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (privilegeTypeBuilder_ == null) {
          privilegeType_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          privilegeTypeBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseGetPrivilegeList_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList build() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList buildPartial() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList result = new fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList(this);
        int from_bitField0_ = bitField0_;
        if (privilegeTypeBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            privilegeType_ = java.util.Collections.unmodifiableList(privilegeType_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.privilegeType_ = privilegeType_;
        } else {
          result.privilegeType_ = privilegeTypeBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList other) {
        if (other == fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList.getDefaultInstance()) return this;
        if (privilegeTypeBuilder_ == null) {
          if (!other.privilegeType_.isEmpty()) {
            if (privilegeType_.isEmpty()) {
              privilegeType_ = other.privilegeType_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePrivilegeTypeIsMutable();
              privilegeType_.addAll(other.privilegeType_);
            }
            onChanged();
          }
        } else {
          if (!other.privilegeType_.isEmpty()) {
            if (privilegeTypeBuilder_.isEmpty()) {
              privilegeTypeBuilder_.dispose();
              privilegeTypeBuilder_ = null;
              privilegeType_ = other.privilegeType_;
              bitField0_ = (bitField0_ & ~0x00000001);
              privilegeTypeBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getPrivilegeTypeFieldBuilder() : null;
            } else {
              privilegeTypeBuilder_.addAllMessages(other.privilegeType_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseGetPrivilegeList) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;
      private java.util.List<fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType> privilegeType_ =
        java.util.Collections.emptyList();
      private void ensurePrivilegeTypeIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          privilegeType_ = new java.util.ArrayList<fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType>(privilegeType_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.Builder, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeTypeOrBuilder> privilegeTypeBuilder_;

      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public java.util.List<fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType> getPrivilegeTypeList() {
        if (privilegeTypeBuilder_ == null) {
          return java.util.Collections.unmodifiableList(privilegeType_);
        } else {
          return privilegeTypeBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public int getPrivilegeTypeCount() {
        if (privilegeTypeBuilder_ == null) {
          return privilegeType_.size();
        } else {
          return privilegeTypeBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType getPrivilegeType(int index) {
        if (privilegeTypeBuilder_ == null) {
          return privilegeType_.get(index);
        } else {
          return privilegeTypeBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public Builder setPrivilegeType(
          int index, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType value) {
        if (privilegeTypeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegeTypeIsMutable();
          privilegeType_.set(index, value);
          onChanged();
        } else {
          privilegeTypeBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public Builder setPrivilegeType(
          int index, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.Builder builderForValue) {
        if (privilegeTypeBuilder_ == null) {
          ensurePrivilegeTypeIsMutable();
          privilegeType_.set(index, builderForValue.build());
          onChanged();
        } else {
          privilegeTypeBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public Builder addPrivilegeType(fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType value) {
        if (privilegeTypeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegeTypeIsMutable();
          privilegeType_.add(value);
          onChanged();
        } else {
          privilegeTypeBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public Builder addPrivilegeType(
          int index, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType value) {
        if (privilegeTypeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegeTypeIsMutable();
          privilegeType_.add(index, value);
          onChanged();
        } else {
          privilegeTypeBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public Builder addPrivilegeType(
          fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.Builder builderForValue) {
        if (privilegeTypeBuilder_ == null) {
          ensurePrivilegeTypeIsMutable();
          privilegeType_.add(builderForValue.build());
          onChanged();
        } else {
          privilegeTypeBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public Builder addPrivilegeType(
          int index, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.Builder builderForValue) {
        if (privilegeTypeBuilder_ == null) {
          ensurePrivilegeTypeIsMutable();
          privilegeType_.add(index, builderForValue.build());
          onChanged();
        } else {
          privilegeTypeBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public Builder addAllPrivilegeType(
          java.lang.Iterable<? extends fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType> values) {
        if (privilegeTypeBuilder_ == null) {
          ensurePrivilegeTypeIsMutable();
          super.addAll(values, privilegeType_);
          onChanged();
        } else {
          privilegeTypeBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public Builder clearPrivilegeType() {
        if (privilegeTypeBuilder_ == null) {
          privilegeType_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          privilegeTypeBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public Builder removePrivilegeType(int index) {
        if (privilegeTypeBuilder_ == null) {
          ensurePrivilegeTypeIsMutable();
          privilegeType_.remove(index);
          onChanged();
        } else {
          privilegeTypeBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.Builder getPrivilegeTypeBuilder(
          int index) {
        return getPrivilegeTypeFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeTypeOrBuilder getPrivilegeTypeOrBuilder(
          int index) {
        if (privilegeTypeBuilder_ == null) {
          return privilegeType_.get(index);  } else {
          return privilegeTypeBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public java.util.List<? extends fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeTypeOrBuilder> 
           getPrivilegeTypeOrBuilderList() {
        if (privilegeTypeBuilder_ != null) {
          return privilegeTypeBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(privilegeType_);
        }
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.Builder addPrivilegeTypeBuilder() {
        return getPrivilegeTypeFieldBuilder().addBuilder(
            fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.Builder addPrivilegeTypeBuilder(
          int index) {
        return getPrivilegeTypeFieldBuilder().addBuilder(
            index, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.hy.vip.protocol.PrivilegeType privilegeType = 1;</code>
       */
      public java.util.List<fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.Builder> 
           getPrivilegeTypeBuilderList() {
        return getPrivilegeTypeFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.Builder, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeTypeOrBuilder> 
          getPrivilegeTypeFieldBuilder() {
        if (privilegeTypeBuilder_ == null) {
          privilegeTypeBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeType.Builder, fm.lizhi.hy.vip.protocol.StarLevelProto.PrivilegeTypeOrBuilder>(
                  privilegeType_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          privilegeType_ = null;
        }
        return privilegeTypeBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.ResponseGetPrivilegeList)
    }

    static {
      defaultInstance = new ResponseGetPrivilegeList(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.ResponseGetPrivilegeList)
  }

  public interface RequestIsCreateFansGroupOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *主播id
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *主播id
     * </pre>
     */
    long getUserId();
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.RequestIsCreateFansGroup}
   *
   * <pre>
   * StarLevelService.java
   * 判断主播是否能够开通粉丝群
   * domain = 20005, op = 264
   * </pre>
   */
  public static final class RequestIsCreateFansGroup extends
      com.google.protobuf.GeneratedMessage
      implements RequestIsCreateFansGroupOrBuilder {
    // Use RequestIsCreateFansGroup.newBuilder() to construct.
    private RequestIsCreateFansGroup(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestIsCreateFansGroup(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestIsCreateFansGroup defaultInstance;
    public static RequestIsCreateFansGroup getDefaultInstance() {
      return defaultInstance;
    }

    public RequestIsCreateFansGroup getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestIsCreateFansGroup(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateFansGroup_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateFansGroup_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup.class, fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestIsCreateFansGroup> PARSER =
        new com.google.protobuf.AbstractParser<RequestIsCreateFansGroup>() {
      public RequestIsCreateFansGroup parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestIsCreateFansGroup(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestIsCreateFansGroup> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *主播id
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *主播id
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    private void initFields() {
      userId_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.RequestIsCreateFansGroup}
     *
     * <pre>
     * StarLevelService.java
     * 判断主播是否能够开通粉丝群
     * domain = 20005, op = 264
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroupOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateFansGroup_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateFansGroup_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup.class, fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateFansGroup_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup build() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup buildPartial() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup result = new fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup other) {
        if (other == fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateFansGroup) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *主播id
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *主播id
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *主播id
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *主播id
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.RequestIsCreateFansGroup)
    }

    static {
      defaultInstance = new RequestIsCreateFansGroup(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.RequestIsCreateFansGroup)
  }

  public interface ResponseIsCreateFansGroupOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional bool isRight = 1;
    /**
     * <code>optional bool isRight = 1;</code>
     *
     * <pre>
     * 是否有权限
     * </pre>
     */
    boolean hasIsRight();
    /**
     * <code>optional bool isRight = 1;</code>
     *
     * <pre>
     * 是否有权限
     * </pre>
     */
    boolean getIsRight();
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.ResponseIsCreateFansGroup}
   */
  public static final class ResponseIsCreateFansGroup extends
      com.google.protobuf.GeneratedMessage
      implements ResponseIsCreateFansGroupOrBuilder {
    // Use ResponseIsCreateFansGroup.newBuilder() to construct.
    private ResponseIsCreateFansGroup(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseIsCreateFansGroup(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseIsCreateFansGroup defaultInstance;
    public static ResponseIsCreateFansGroup getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseIsCreateFansGroup getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseIsCreateFansGroup(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              isRight_ = input.readBool();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateFansGroup_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateFansGroup_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup.class, fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseIsCreateFansGroup> PARSER =
        new com.google.protobuf.AbstractParser<ResponseIsCreateFansGroup>() {
      public ResponseIsCreateFansGroup parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseIsCreateFansGroup(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseIsCreateFansGroup> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional bool isRight = 1;
    public static final int ISRIGHT_FIELD_NUMBER = 1;
    private boolean isRight_;
    /**
     * <code>optional bool isRight = 1;</code>
     *
     * <pre>
     * 是否有权限
     * </pre>
     */
    public boolean hasIsRight() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bool isRight = 1;</code>
     *
     * <pre>
     * 是否有权限
     * </pre>
     */
    public boolean getIsRight() {
      return isRight_;
    }

    private void initFields() {
      isRight_ = false;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBool(1, isRight_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isRight_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.ResponseIsCreateFansGroup}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroupOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateFansGroup_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateFansGroup_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup.class, fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        isRight_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateFansGroup_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup build() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup buildPartial() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup result = new fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.isRight_ = isRight_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup other) {
        if (other == fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup.getDefaultInstance()) return this;
        if (other.hasIsRight()) {
          setIsRight(other.getIsRight());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateFansGroup) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional bool isRight = 1;
      private boolean isRight_ ;
      /**
       * <code>optional bool isRight = 1;</code>
       *
       * <pre>
       * 是否有权限
       * </pre>
       */
      public boolean hasIsRight() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bool isRight = 1;</code>
       *
       * <pre>
       * 是否有权限
       * </pre>
       */
      public boolean getIsRight() {
        return isRight_;
      }
      /**
       * <code>optional bool isRight = 1;</code>
       *
       * <pre>
       * 是否有权限
       * </pre>
       */
      public Builder setIsRight(boolean value) {
        bitField0_ |= 0x00000001;
        isRight_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isRight = 1;</code>
       *
       * <pre>
       * 是否有权限
       * </pre>
       */
      public Builder clearIsRight() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isRight_ = false;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.ResponseIsCreateFansGroup)
    }

    static {
      defaultInstance = new ResponseIsCreateFansGroup(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.ResponseIsCreateFansGroup)
  }

  public interface RequestIsCreateExclusiveAnchorOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *主播id
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *主播id
     * </pre>
     */
    long getUserId();
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.RequestIsCreateExclusiveAnchor}
   *
   * <pre>
   * StarLevelService.java
   * 判断主播是否能够开通独家主播
   * domain = 20005, op = 265
   * </pre>
   */
  public static final class RequestIsCreateExclusiveAnchor extends
      com.google.protobuf.GeneratedMessage
      implements RequestIsCreateExclusiveAnchorOrBuilder {
    // Use RequestIsCreateExclusiveAnchor.newBuilder() to construct.
    private RequestIsCreateExclusiveAnchor(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestIsCreateExclusiveAnchor(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestIsCreateExclusiveAnchor defaultInstance;
    public static RequestIsCreateExclusiveAnchor getDefaultInstance() {
      return defaultInstance;
    }

    public RequestIsCreateExclusiveAnchor getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestIsCreateExclusiveAnchor(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateExclusiveAnchor_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateExclusiveAnchor_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor.class, fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestIsCreateExclusiveAnchor> PARSER =
        new com.google.protobuf.AbstractParser<RequestIsCreateExclusiveAnchor>() {
      public RequestIsCreateExclusiveAnchor parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestIsCreateExclusiveAnchor(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestIsCreateExclusiveAnchor> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *主播id
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     *主播id
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    private void initFields() {
      userId_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.RequestIsCreateExclusiveAnchor}
     *
     * <pre>
     * StarLevelService.java
     * 判断主播是否能够开通独家主播
     * domain = 20005, op = 265
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchorOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateExclusiveAnchor_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateExclusiveAnchor_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor.class, fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateExclusiveAnchor_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor build() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor buildPartial() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor result = new fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor other) {
        if (other == fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.StarLevelProto.RequestIsCreateExclusiveAnchor) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *主播id
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *主播id
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *主播id
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       *主播id
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.RequestIsCreateExclusiveAnchor)
    }

    static {
      defaultInstance = new RequestIsCreateExclusiveAnchor(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.RequestIsCreateExclusiveAnchor)
  }

  public interface ResponseIsCreateExclusiveAnchorOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional bool isRight = 1;
    /**
     * <code>optional bool isRight = 1;</code>
     *
     * <pre>
     * 是否有权限
     * </pre>
     */
    boolean hasIsRight();
    /**
     * <code>optional bool isRight = 1;</code>
     *
     * <pre>
     * 是否有权限
     * </pre>
     */
    boolean getIsRight();
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.ResponseIsCreateExclusiveAnchor}
   */
  public static final class ResponseIsCreateExclusiveAnchor extends
      com.google.protobuf.GeneratedMessage
      implements ResponseIsCreateExclusiveAnchorOrBuilder {
    // Use ResponseIsCreateExclusiveAnchor.newBuilder() to construct.
    private ResponseIsCreateExclusiveAnchor(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseIsCreateExclusiveAnchor(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseIsCreateExclusiveAnchor defaultInstance;
    public static ResponseIsCreateExclusiveAnchor getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseIsCreateExclusiveAnchor getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseIsCreateExclusiveAnchor(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              isRight_ = input.readBool();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateExclusiveAnchor_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateExclusiveAnchor_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor.class, fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseIsCreateExclusiveAnchor> PARSER =
        new com.google.protobuf.AbstractParser<ResponseIsCreateExclusiveAnchor>() {
      public ResponseIsCreateExclusiveAnchor parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseIsCreateExclusiveAnchor(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseIsCreateExclusiveAnchor> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional bool isRight = 1;
    public static final int ISRIGHT_FIELD_NUMBER = 1;
    private boolean isRight_;
    /**
     * <code>optional bool isRight = 1;</code>
     *
     * <pre>
     * 是否有权限
     * </pre>
     */
    public boolean hasIsRight() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bool isRight = 1;</code>
     *
     * <pre>
     * 是否有权限
     * </pre>
     */
    public boolean getIsRight() {
      return isRight_;
    }

    private void initFields() {
      isRight_ = false;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBool(1, isRight_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isRight_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.ResponseIsCreateExclusiveAnchor}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchorOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateExclusiveAnchor_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateExclusiveAnchor_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor.class, fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        isRight_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateExclusiveAnchor_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor build() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor buildPartial() {
        fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor result = new fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.isRight_ = isRight_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor other) {
        if (other == fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor.getDefaultInstance()) return this;
        if (other.hasIsRight()) {
          setIsRight(other.getIsRight());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.StarLevelProto.ResponseIsCreateExclusiveAnchor) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional bool isRight = 1;
      private boolean isRight_ ;
      /**
       * <code>optional bool isRight = 1;</code>
       *
       * <pre>
       * 是否有权限
       * </pre>
       */
      public boolean hasIsRight() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bool isRight = 1;</code>
       *
       * <pre>
       * 是否有权限
       * </pre>
       */
      public boolean getIsRight() {
        return isRight_;
      }
      /**
       * <code>optional bool isRight = 1;</code>
       *
       * <pre>
       * 是否有权限
       * </pre>
       */
      public Builder setIsRight(boolean value) {
        bitField0_ |= 0x00000001;
        isRight_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isRight = 1;</code>
       *
       * <pre>
       * 是否有权限
       * </pre>
       */
      public Builder clearIsRight() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isRight_ = false;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.ResponseIsCreateExclusiveAnchor)
    }

    static {
      defaultInstance = new ResponseIsCreateExclusiveAnchor(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.ResponseIsCreateExclusiveAnchor)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_PrivilegeType_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_PrivilegeType_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_RequestGetStarGradeInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_RequestGetStarGradeInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_ResponseGetStarGradeInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_ResponseGetStarGradeInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_RequestAddStarValue_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_RequestAddStarValue_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_RequestGetPrivilegeList_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_RequestGetPrivilegeList_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_ResponseGetPrivilegeList_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_ResponseGetPrivilegeList_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateFansGroup_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateFansGroup_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateFansGroup_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateFansGroup_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateExclusiveAnchor_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateExclusiveAnchor_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateExclusiveAnchor_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateExclusiveAnchor_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\020star_level.proto\022\030fm.lizhi.hy.vip.prot" +
      "ocol\"1\n\rPrivilegeType\022\021\n\tcondition\030\001 \001(\005" +
      "\022\r\n\005title\030\002 \001(\t\")\n\027RequestGetStarGradeIn" +
      "fo\022\016\n\006userId\030\001 \001(\003\"\320\001\n\030ResponseGetStarGr" +
      "adeInfo\022\016\n\006userId\030\001 \001(\003\022\r\n\005grade\030\002 \001(\005\022\022" +
      "\n\nexperience\030\003 \001(\003\022\026\n\016nextLevelValue\030\004 \001" +
      "(\003\022\027\n\017upgradeProgress\030\005 \001(\005\022\032\n\022distanceE" +
      "xpreience\030\006 \001(\003\022\026\n\016starLevelBadge\030\007 \001(\t\022" +
      "\034\n\024starLevelBadgeAspect\030\010 \001(\002\"L\n\023Request" +
      "AddStarValue\022\016\n\006userId\030\001 \001(\003\022\022\n\ncoinAmou",
      "nt\030\002 \001(\003\022\021\n\tstarValue\030\003 \001(\003\"\031\n\027RequestGe" +
      "tPrivilegeList\"Z\n\030ResponseGetPrivilegeLi" +
      "st\022>\n\rprivilegeType\030\001 \003(\0132\'.fm.lizhi.hy." +
      "vip.protocol.PrivilegeType\"*\n\030RequestIsC" +
      "reateFansGroup\022\016\n\006userId\030\001 \001(\003\",\n\031Respon" +
      "seIsCreateFansGroup\022\017\n\007isRight\030\001 \001(\010\"0\n\036" +
      "RequestIsCreateExclusiveAnchor\022\016\n\006userId" +
      "\030\001 \001(\003\"2\n\037ResponseIsCreateExclusiveAncho" +
      "r\022\017\n\007isRight\030\001 \001(\010B*\n\030fm.lizhi.hy.vip.pr" +
      "otocolB\016StarLevelProto"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_hy_vip_protocol_PrivilegeType_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_hy_vip_protocol_PrivilegeType_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_PrivilegeType_descriptor,
              new java.lang.String[] { "Condition", "Title", });
          internal_static_fm_lizhi_hy_vip_protocol_RequestGetStarGradeInfo_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_hy_vip_protocol_RequestGetStarGradeInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_RequestGetStarGradeInfo_descriptor,
              new java.lang.String[] { "UserId", });
          internal_static_fm_lizhi_hy_vip_protocol_ResponseGetStarGradeInfo_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_hy_vip_protocol_ResponseGetStarGradeInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_ResponseGetStarGradeInfo_descriptor,
              new java.lang.String[] { "UserId", "Grade", "Experience", "NextLevelValue", "UpgradeProgress", "DistanceExpreience", "StarLevelBadge", "StarLevelBadgeAspect", });
          internal_static_fm_lizhi_hy_vip_protocol_RequestAddStarValue_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_fm_lizhi_hy_vip_protocol_RequestAddStarValue_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_RequestAddStarValue_descriptor,
              new java.lang.String[] { "UserId", "CoinAmount", "StarValue", });
          internal_static_fm_lizhi_hy_vip_protocol_RequestGetPrivilegeList_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_fm_lizhi_hy_vip_protocol_RequestGetPrivilegeList_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_RequestGetPrivilegeList_descriptor,
              new java.lang.String[] { });
          internal_static_fm_lizhi_hy_vip_protocol_ResponseGetPrivilegeList_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_fm_lizhi_hy_vip_protocol_ResponseGetPrivilegeList_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_ResponseGetPrivilegeList_descriptor,
              new java.lang.String[] { "PrivilegeType", });
          internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateFansGroup_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateFansGroup_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateFansGroup_descriptor,
              new java.lang.String[] { "UserId", });
          internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateFansGroup_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateFansGroup_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateFansGroup_descriptor,
              new java.lang.String[] { "IsRight", });
          internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateExclusiveAnchor_descriptor =
            getDescriptor().getMessageTypes().get(8);
          internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateExclusiveAnchor_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_RequestIsCreateExclusiveAnchor_descriptor,
              new java.lang.String[] { "UserId", });
          internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateExclusiveAnchor_descriptor =
            getDescriptor().getMessageTypes().get(9);
          internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateExclusiveAnchor_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_ResponseIsCreateExclusiveAnchor_descriptor,
              new java.lang.String[] { "IsRight", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
