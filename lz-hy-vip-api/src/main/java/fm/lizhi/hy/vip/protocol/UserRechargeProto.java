// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user_recharge.proto

package fm.lizhi.hy.vip.protocol;

public final class UserRechargeProto {
  private UserRechargeProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface HadRechargedParamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 userId = 1;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    long getUserId();
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.HadRechargedParam}
   *
   * <pre>
   * 查询用户是否充值过请求参数
   * </pre>
   */
  public static final class HadRechargedParam extends
      com.google.protobuf.GeneratedMessage
      implements HadRechargedParamOrBuilder {
    // Use HadRechargedParam.newBuilder() to construct.
    private HadRechargedParam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private HadRechargedParam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final HadRechargedParam defaultInstance;
    public static HadRechargedParam getDefaultInstance() {
      return defaultInstance;
    }

    public HadRechargedParam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private HadRechargedParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_HadRechargedParam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_HadRechargedParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.class, fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.Builder.class);
    }

    public static com.google.protobuf.Parser<HadRechargedParam> PARSER =
        new com.google.protobuf.AbstractParser<HadRechargedParam>() {
      public HadRechargedParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new HadRechargedParam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<HadRechargedParam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    private void initFields() {
      userId_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.HadRechargedParam}
     *
     * <pre>
     * 查询用户是否充值过请求参数
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_HadRechargedParam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_HadRechargedParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.class, fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_HadRechargedParam_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam build() {
        fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam buildPartial() {
        fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam result = new fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam other) {
        if (other == fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 userId = 1;
      private long userId_ ;
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.HadRechargedParam)
    }

    static {
      defaultInstance = new HadRechargedParam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.HadRechargedParam)
  }

  public interface RequestHadRechargedOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;
    /**
     * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
     */
    boolean hasParam();
    /**
     * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
     */
    fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam getParam();
    /**
     * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
     */
    fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParamOrBuilder getParamOrBuilder();
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.RequestHadRecharged}
   *
   * <pre>
   * UserRechargeService.class
   * 查询用户是否充值过
   * domain = 20005, op = 291
   * </pre>
   */
  public static final class RequestHadRecharged extends
      com.google.protobuf.GeneratedMessage
      implements RequestHadRechargedOrBuilder {
    // Use RequestHadRecharged.newBuilder() to construct.
    private RequestHadRecharged(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestHadRecharged(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestHadRecharged defaultInstance;
    public static RequestHadRecharged getDefaultInstance() {
      return defaultInstance;
    }

    public RequestHadRecharged getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestHadRecharged(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = param_.toBuilder();
              }
              param_ = input.readMessage(fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(param_);
                param_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_RequestHadRecharged_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_RequestHadRecharged_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged.class, fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestHadRecharged> PARSER =
        new com.google.protobuf.AbstractParser<RequestHadRecharged>() {
      public RequestHadRecharged parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestHadRecharged(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestHadRecharged> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;
    public static final int PARAM_FIELD_NUMBER = 1;
    private fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam param_;
    /**
     * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
     */
    public boolean hasParam() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
     */
    public fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam getParam() {
      return param_;
    }
    /**
     * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
     */
    public fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParamOrBuilder getParamOrBuilder() {
      return param_;
    }

    private void initFields() {
      param_ = fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasParam()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, param_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, param_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.RequestHadRecharged}
     *
     * <pre>
     * UserRechargeService.class
     * 查询用户是否充值过
     * domain = 20005, op = 291
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRechargedOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_RequestHadRecharged_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_RequestHadRecharged_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged.class, fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getParamFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.getDefaultInstance();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_RequestHadRecharged_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged build() {
        fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged buildPartial() {
        fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged result = new fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (paramBuilder_ == null) {
          result.param_ = param_;
        } else {
          result.param_ = paramBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged other) {
        if (other == fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged.getDefaultInstance()) return this;
        if (other.hasParam()) {
          mergeParam(other.getParam());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasParam()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.UserRechargeProto.RequestHadRecharged) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;
      private fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam param_ = fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam, fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.Builder, fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParamOrBuilder> paramBuilder_;
      /**
       * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
       */
      public boolean hasParam() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
       */
      public fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam getParam() {
        if (paramBuilder_ == null) {
          return param_;
        } else {
          return paramBuilder_.getMessage();
        }
      }
      /**
       * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
       */
      public Builder setParam(fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam value) {
        if (paramBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          param_ = value;
          onChanged();
        } else {
          paramBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
       */
      public Builder setParam(
          fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.Builder builderForValue) {
        if (paramBuilder_ == null) {
          param_ = builderForValue.build();
          onChanged();
        } else {
          paramBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
       */
      public Builder mergeParam(fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam value) {
        if (paramBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              param_ != fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.getDefaultInstance()) {
            param_ =
              fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.newBuilder(param_).mergeFrom(value).buildPartial();
          } else {
            param_ = value;
          }
          onChanged();
        } else {
          paramBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
       */
      public Builder clearParam() {
        if (paramBuilder_ == null) {
          param_ = fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.getDefaultInstance();
          onChanged();
        } else {
          paramBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
       */
      public fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.Builder getParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getParamFieldBuilder().getBuilder();
      }
      /**
       * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
       */
      public fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParamOrBuilder getParamOrBuilder() {
        if (paramBuilder_ != null) {
          return paramBuilder_.getMessageOrBuilder();
        } else {
          return param_;
        }
      }
      /**
       * <code>required .fm.lizhi.hy.vip.protocol.HadRechargedParam param = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam, fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.Builder, fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParamOrBuilder> 
          getParamFieldBuilder() {
        if (paramBuilder_ == null) {
          paramBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam, fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParam.Builder, fm.lizhi.hy.vip.protocol.UserRechargeProto.HadRechargedParamOrBuilder>(
                  param_,
                  getParentForChildren(),
                  isClean());
          param_ = null;
        }
        return paramBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.RequestHadRecharged)
    }

    static {
      defaultInstance = new RequestHadRecharged(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.RequestHadRecharged)
  }

  public interface ResponseHadRechargedOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional bool hadRecharged = 1;
    /**
     * <code>optional bool hadRecharged = 1;</code>
     *
     * <pre>
     * 是否充值过
     * </pre>
     */
    boolean hasHadRecharged();
    /**
     * <code>optional bool hadRecharged = 1;</code>
     *
     * <pre>
     * 是否充值过
     * </pre>
     */
    boolean getHadRecharged();
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.ResponseHadRecharged}
   *
   * <pre>
   * rCode == 0 (SUCCESS) == 成功
   * rCode == 1 (PARAM_ERROR) == 参数异常
   * rCode == 2 (FAIL) == 失败
   * </pre>
   */
  public static final class ResponseHadRecharged extends
      com.google.protobuf.GeneratedMessage
      implements ResponseHadRechargedOrBuilder {
    // Use ResponseHadRecharged.newBuilder() to construct.
    private ResponseHadRecharged(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseHadRecharged(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseHadRecharged defaultInstance;
    public static ResponseHadRecharged getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseHadRecharged getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseHadRecharged(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              hadRecharged_ = input.readBool();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseHadRecharged_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseHadRecharged_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged.class, fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseHadRecharged> PARSER =
        new com.google.protobuf.AbstractParser<ResponseHadRecharged>() {
      public ResponseHadRecharged parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseHadRecharged(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseHadRecharged> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional bool hadRecharged = 1;
    public static final int HADRECHARGED_FIELD_NUMBER = 1;
    private boolean hadRecharged_;
    /**
     * <code>optional bool hadRecharged = 1;</code>
     *
     * <pre>
     * 是否充值过
     * </pre>
     */
    public boolean hasHadRecharged() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bool hadRecharged = 1;</code>
     *
     * <pre>
     * 是否充值过
     * </pre>
     */
    public boolean getHadRecharged() {
      return hadRecharged_;
    }

    private void initFields() {
      hadRecharged_ = false;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBool(1, hadRecharged_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, hadRecharged_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.ResponseHadRecharged}
     *
     * <pre>
     * rCode == 0 (SUCCESS) == 成功
     * rCode == 1 (PARAM_ERROR) == 参数异常
     * rCode == 2 (FAIL) == 失败
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRechargedOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseHadRecharged_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseHadRecharged_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged.class, fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        hadRecharged_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.UserRechargeProto.internal_static_fm_lizhi_hy_vip_protocol_ResponseHadRecharged_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged build() {
        fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged buildPartial() {
        fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged result = new fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.hadRecharged_ = hadRecharged_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged other) {
        if (other == fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged.getDefaultInstance()) return this;
        if (other.hasHadRecharged()) {
          setHadRecharged(other.getHadRecharged());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.UserRechargeProto.ResponseHadRecharged) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional bool hadRecharged = 1;
      private boolean hadRecharged_ ;
      /**
       * <code>optional bool hadRecharged = 1;</code>
       *
       * <pre>
       * 是否充值过
       * </pre>
       */
      public boolean hasHadRecharged() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bool hadRecharged = 1;</code>
       *
       * <pre>
       * 是否充值过
       * </pre>
       */
      public boolean getHadRecharged() {
        return hadRecharged_;
      }
      /**
       * <code>optional bool hadRecharged = 1;</code>
       *
       * <pre>
       * 是否充值过
       * </pre>
       */
      public Builder setHadRecharged(boolean value) {
        bitField0_ |= 0x00000001;
        hadRecharged_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool hadRecharged = 1;</code>
       *
       * <pre>
       * 是否充值过
       * </pre>
       */
      public Builder clearHadRecharged() {
        bitField0_ = (bitField0_ & ~0x00000001);
        hadRecharged_ = false;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.ResponseHadRecharged)
    }

    static {
      defaultInstance = new ResponseHadRecharged(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.ResponseHadRecharged)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_HadRechargedParam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_HadRechargedParam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_RequestHadRecharged_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_RequestHadRecharged_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_ResponseHadRecharged_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_ResponseHadRecharged_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023user_recharge.proto\022\030fm.lizhi.hy.vip.p" +
      "rotocol\"#\n\021HadRechargedParam\022\016\n\006userId\030\001" +
      " \001(\003\"Q\n\023RequestHadRecharged\022:\n\005param\030\001 \002" +
      "(\0132+.fm.lizhi.hy.vip.protocol.HadRecharg" +
      "edParam\",\n\024ResponseHadRecharged\022\024\n\014hadRe" +
      "charged\030\001 \001(\010B-\n\030fm.lizhi.hy.vip.protoco" +
      "lB\021UserRechargeProto"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_hy_vip_protocol_HadRechargedParam_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_hy_vip_protocol_HadRechargedParam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_HadRechargedParam_descriptor,
              new java.lang.String[] { "UserId", });
          internal_static_fm_lizhi_hy_vip_protocol_RequestHadRecharged_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_fm_lizhi_hy_vip_protocol_RequestHadRecharged_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_RequestHadRecharged_descriptor,
              new java.lang.String[] { "Param", });
          internal_static_fm_lizhi_hy_vip_protocol_ResponseHadRecharged_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_fm_lizhi_hy_vip_protocol_ResponseHadRecharged_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_ResponseHadRecharged_descriptor,
              new java.lang.String[] { "HadRecharged", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
