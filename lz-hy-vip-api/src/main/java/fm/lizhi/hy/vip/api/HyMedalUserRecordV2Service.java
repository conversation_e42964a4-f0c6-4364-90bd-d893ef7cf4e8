package fm.lizhi.hy.vip.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.vip.protocol.HyMedalBaseV2Proto;
import fm.lizhi.hy.vip.protocol.HyMedalUserRecordV2Proto;
import fm.lizhi.hy.vip.protocol.HyMedalUserRecordV2Proto.RequestMedalUserRecordList;
import fm.lizhi.hy.vip.protocol.HyMedalUserRecordV2Proto.ResponseMedalUserRecordList;
import fm.lizhi.hy.vip.protocol.HyMedalUserRecordV2Proto.RequestMedalUserRecordUserList;
import fm.lizhi.hy.vip.protocol.HyMedalUserRecordV2Proto.ResponseMedalUserRecordUserList;
import fm.lizhi.hy.vip.protocol.HyMedalUserRecordV2Proto.RequestGetMedalUserRecordList;
import fm.lizhi.hy.vip.protocol.HyMedalUserRecordV2Proto.ResponseGetMedalUserRecordList;
import fm.lizhi.hy.vip.protocol.HyMedalUserRecordV2Proto.RequestGetUserMedalList;
import fm.lizhi.hy.vip.protocol.HyMedalUserRecordV2Proto.ResponseGetUserMedalList;
import fm.lizhi.hy.vip.protocol.HyMedalUserRecordV2Proto.ResponseUserDataMigration;
import fm.lizhi.hy.vip.protocol.HyMedalUserRecordV2Proto.RequestUserDataMigration;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface HyMedalUserRecordV2Service {
	
	
	/**
	 *  获取勋章发放记录列表
	 *
	 * @param params
	 *            
	 * @return 
	 *     //if rcode == 0 成功<br>
	 *     //if rcode == 1 失败<br>
	 *     //if rcode == 2 非法参数<br>
	 */
	@Service(domain = 20005, op = 5001, request = RequestMedalUserRecordList.class, response = ResponseMedalUserRecordList.class)
	@Return(resultType = ResponseMedalUserRecordList.class)
	Result<ResponseMedalUserRecordList> medalUserRecordList(@Attribute(name = "params") HyMedalBaseV2Proto.QueryParams params);
	
	
	/**
	 *  获取用户勋章发放记录列表
	 *
	 * @param userId
	 *            用户ID
	 * @param typeId
	 *            类型ID
	 * @return 
	 *     //if rcode == 0 成功<br>
	 *     //if rcode == 1 失败<br>
	 *     //if rcode == 2 非法参数<br>
	 */
	@Service(domain = 20005, op = 5002, request = RequestMedalUserRecordUserList.class, response = ResponseMedalUserRecordUserList.class)
	@Return(resultType = ResponseMedalUserRecordUserList.class)
	Result<ResponseMedalUserRecordUserList> medalUserRecordUserList(@Attribute(name = "userId") long userId, @Attribute(name = "typeId") long typeId);
	
	
	/**
	 *  获取用户勋章记录 - 通过勋章组ID
	 *
	 * @param userId
	 *            用户ID
	 * @param groupId
	 *            勋章组ID
	 * @return 
	 *     //if rcode == 0 成功<br>
	 *     //if rcode == 1 失败<br>
	 *     //if rcode == 2 不存在<br>
	 */
	@Service(domain = 20005, op = 5003, request = RequestGetMedalUserRecordList.class, response = ResponseGetMedalUserRecordList.class)
	@Return(resultType = ResponseGetMedalUserRecordList.class)
	Result<ResponseGetMedalUserRecordList> getMedalUserRecordList(@Attribute(name = "userId") long userId, @Attribute(name = "groupId") long groupId);
	
	
	/**
	 *  通过userId获取勋章列表
	 *
	 * @param request
	 *            
	 * @return 
	 *     //if rcode == 0 成功<br>
	 *     //if rcode == 1 失败<br>
	 *     //if rcode == 2 非法参数<br>
	 */
	@Service(domain = 20005, op = 5004, request = RequestGetUserMedalList.class, response = ResponseGetUserMedalList.class)
	@Return(resultType = ResponseGetUserMedalList.class)
	Result<ResponseGetUserMedalList> getUserMedalList(@Attribute(name = "request") HyMedalBaseV2Proto.GetMedalListRequest request);

	/**
	 *  用户勋章迁移
	 *
	 * @param oldUserId
	 * @param newUserId
	 * @param description
	 * @return
	 */
	@Service(domain = 20005, op = 5005, request = RequestUserDataMigration.class, response = ResponseUserDataMigration.class)
	@Return(resultType = ResponseUserDataMigration.class)
	Result<ResponseUserDataMigration> userDataMigration(@Attribute(name = "oldUserId") long oldUserId, @Attribute(name = "newUserId") long newUserId, @Attribute(name = "description") String description);
	
	public static final int MEDAL_USER_RECORD_LIST_SUCCESS = 0; // 成功
	public static final int MEDAL_USER_RECORD_LIST_FAIL = 1; // 失败
	public static final int MEDAL_USER_RECORD_LIST_ILLEGAL_PARAMS = 2; // 非法参数

	public static final int MEDAL_USER_RECORD_USER_LIST_SUCCESS = 0; // 成功
	public static final int MEDAL_USER_RECORD_USER_LIST_FAIL = 1; // 失败
	public static final int MEDAL_USER_RECORD_USER_LIST_ILLEGAL_PARAMS = 2; // 非法参数

	public static final int GET_MEDAL_USER_RECORD_LIST_SUCCESS = 0; // 成功
	public static final int GET_MEDAL_USER_RECORD_LIST_FAIL = 1; // 失败
	public static final int GET_MEDAL_USER_RECORD_LIST_NO_EXIST = 2; // 不存在

	public static final int GET_USER_MEDAL_LIST_SUCCESS = 0; // 成功
	public static final int GET_USER_MEDAL_LIST_FAIL = 1; // 失败
	public static final int GET_USER_MEDAL_LIST_ILLEGAL_PARAMS = 2; // 非法参数


}