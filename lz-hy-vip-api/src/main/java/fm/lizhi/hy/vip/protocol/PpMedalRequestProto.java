// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: medal_request.proto

package fm.lizhi.hy.vip.protocol;

public final class PpMedalRequestProto {
  private PpMedalRequestProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface GetMedalListRequestOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 userId = 1;
    /**
     * <code>required int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    boolean hasUserId();
    /**
     * <code>required int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    long getUserId();
  }
  /**
   * Protobuf type {@code fm.lizhi.hy.vip.protocol.GetMedalListRequest}
   *
   * <pre>
   * 获取勋章列表请求
   * </pre>
   */
  public static final class GetMedalListRequest extends
      com.google.protobuf.GeneratedMessage
      implements GetMedalListRequestOrBuilder {
    // Use GetMedalListRequest.newBuilder() to construct.
    private GetMedalListRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GetMedalListRequest(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GetMedalListRequest defaultInstance;
    public static GetMedalListRequest getDefaultInstance() {
      return defaultInstance;
    }

    public GetMedalListRequest getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GetMedalListRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              userId_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.hy.vip.protocol.PpMedalRequestProto.internal_static_fm_lizhi_hy_vip_protocol_GetMedalListRequest_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.hy.vip.protocol.PpMedalRequestProto.internal_static_fm_lizhi_hy_vip_protocol_GetMedalListRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest.class, fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest.Builder.class);
    }

    public static com.google.protobuf.Parser<GetMedalListRequest> PARSER =
        new com.google.protobuf.AbstractParser<GetMedalListRequest>() {
      public GetMedalListRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetMedalListRequest(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GetMedalListRequest> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 userId = 1;
    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_;
    /**
     * <code>required int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 userId = 1;</code>
     *
     * <pre>
     * 用户ID
     * </pre>
     */
    public long getUserId() {
      return userId_;
    }

    private void initFields() {
      userId_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasUserId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, userId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fm.lizhi.hy.vip.protocol.GetMedalListRequest}
     *
     * <pre>
     * 获取勋章列表请求
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.hy.vip.protocol.PpMedalRequestProto.internal_static_fm_lizhi_hy_vip_protocol_GetMedalListRequest_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.hy.vip.protocol.PpMedalRequestProto.internal_static_fm_lizhi_hy_vip_protocol_GetMedalListRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest.class, fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest.Builder.class);
      }

      // Construct using fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        userId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.hy.vip.protocol.PpMedalRequestProto.internal_static_fm_lizhi_hy_vip_protocol_GetMedalListRequest_descriptor;
      }

      public fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest getDefaultInstanceForType() {
        return fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest.getDefaultInstance();
      }

      public fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest build() {
        fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest buildPartial() {
        fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest result = new fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.userId_ = userId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest) {
          return mergeFrom((fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest other) {
        if (other == fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest.getDefaultInstance()) return this;
        if (other.hasUserId()) {
          setUserId(other.getUserId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasUserId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.hy.vip.protocol.PpMedalRequestProto.GetMedalListRequest) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 userId = 1;
      private long userId_ ;
      /**
       * <code>required int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>required int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public Builder setUserId(long value) {
        bitField0_ |= 0x00000001;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 userId = 1;</code>
       *
       * <pre>
       * 用户ID
       * </pre>
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:fm.lizhi.hy.vip.protocol.GetMedalListRequest)
    }

    static {
      defaultInstance = new GetMedalListRequest(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:fm.lizhi.hy.vip.protocol.GetMedalListRequest)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_fm_lizhi_hy_vip_protocol_GetMedalListRequest_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_fm_lizhi_hy_vip_protocol_GetMedalListRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023medal_request.proto\022\030fm.lizhi.hy.vip.p" +
      "rotocol\032\020medal_base.proto\"%\n\023GetMedalLis" +
      "tRequest\022\016\n\006userId\030\001 \002(\003B/\n\030fm.lizhi.hy." +
      "vip.protocolB\023PpMedalRequestProto"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_fm_lizhi_hy_vip_protocol_GetMedalListRequest_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_fm_lizhi_hy_vip_protocol_GetMedalListRequest_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_fm_lizhi_hy_vip_protocol_GetMedalListRequest_descriptor,
              new java.lang.String[] { "UserId", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          fm.lizhi.hy.vip.protocol.PpMedalBaseProto.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
