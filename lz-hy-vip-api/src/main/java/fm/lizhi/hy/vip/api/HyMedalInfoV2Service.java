package fm.lizhi.hy.vip.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.hy.vip.protocol.HyMedalBaseV2Proto;
import fm.lizhi.hy.vip.protocol.HyMedalInfoV2Proto.RequestMedalInfoList;
import fm.lizhi.hy.vip.protocol.HyMedalInfoV2Proto.ResponseMedalInfoList;
import fm.lizhi.hy.vip.protocol.HyMedalInfoV2Proto.RequestMedalInfoAdd;
import fm.lizhi.hy.vip.protocol.HyMedalInfoV2Proto.ResponseMedalInfoAdd;
import fm.lizhi.hy.vip.protocol.HyMedalInfoV2Proto.RequestMedalInfoEdit;
import fm.lizhi.hy.vip.protocol.HyMedalInfoV2Proto.ResponseMedalInfoEdit;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface HyMedalInfoV2Service {
	
	
	/**
	 *  获取勋章信息列表
	 *
	 * @param params
	 *            
	 * @return 
	 *     //if rcode == 0 成功<br>
	 *     //if rcode == 1 失败<br>
	 *     //if rcode == 2 非法参数<br>
	 */
	@Service(domain = 20005, op = 4001, request = RequestMedalInfoList.class, response = ResponseMedalInfoList.class)
	@Return(resultType = ResponseMedalInfoList.class)
	Result<ResponseMedalInfoList> medalInfoList(@Attribute(name = "params") HyMedalBaseV2Proto.QueryParams params);
	
	
	/**
	 *  添加勋章信息
	 *
	 * @param medalInfo
	 *            
	 * @return 
	 *     //if rcode == 0 成功<br>
	 *     //if rcode == 1 失败<br>
	 *     //if rcode == 2 非法参数<br>
	 */
	@Service(domain = 20005, op = 4002, request = RequestMedalInfoAdd.class, response = ResponseMedalInfoAdd.class)
	@Return(resultType = ResponseMedalInfoAdd.class)
	Result<ResponseMedalInfoAdd> medalInfoAdd(@Attribute(name = "medalInfo") HyMedalBaseV2Proto.MedalInfoV2 medalInfo);
	
	
	/**
	 *  编辑勋章信息
	 *
	 * @param medalInfo
	 *            
	 * @return 
	 *     //if rcode == 0 成功<br>
	 *     //if rcode == 1 失败<br>
	 *     //if rcode == 2 非法参数<br>
	 */
	@Service(domain = 20005, op = 4003, request = RequestMedalInfoEdit.class, response = ResponseMedalInfoEdit.class)
	@Return(resultType = ResponseMedalInfoEdit.class)
	Result<ResponseMedalInfoEdit> medalInfoEdit(@Attribute(name = "medalInfo") HyMedalBaseV2Proto.MedalInfoV2 medalInfo);
	
	
	public static final int MEDAL_INFO_LIST_SUCCESS = 0; // 成功
	public static final int MEDAL_INFO_LIST_FAIL = 1; // 失败
	public static final int MEDAL_INFO_LIST_ILLEGAL_PARAMS = 2; // 非法参数

	public static final int MEDAL_INFO_ADD_SUCCESS = 0; // 成功
	public static final int MEDAL_INFO_ADD_FAIL = 1; // 失败
	public static final int MEDAL_INFO_ADD_ILLEGAL_PARAMS = 2; // 非法参数

	public static final int MEDAL_INFO_EDIT_SUCCESS = 0; // 成功
	public static final int MEDAL_INFO_EDIT_FAIL = 1; // 失败
	public static final int MEDAL_INFO_EDIT_ILLEGAL_PARAMS = 2; // 非法参数


}