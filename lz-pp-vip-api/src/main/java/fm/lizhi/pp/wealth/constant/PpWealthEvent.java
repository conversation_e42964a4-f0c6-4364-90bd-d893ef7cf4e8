package fm.lizhi.pp.wealth.constant;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PpWealthEvent {
    /**
     * 业务ID，幂等
     */
    private long historyId;
    /**
     * 增加的用户ID
     */
    private long userId;
    /**
     * 发生时间
     */
    private long happendTime;
    /**
     * 增加的数量
     */
    private long amount;

    /**
     * 业务事件
     */
    private int bizCode;

    public long getHistoryId() {
        return historyId;
    }

    public void setHistoryId(long historyId) {
        this.historyId = historyId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public long getHappendTime() {
        return happendTime;
    }

    public void setHappendTime(long happendTime) {
        this.happendTime = happendTime;
    }

    public long getAmount() {
        return amount;
    }

    public void setAmount(long amount) {
        this.amount = amount;
    }

    public int getBizCode() {
        return bizCode;
    }

    public void setBizCode(int bizCode) {
        this.bizCode = bizCode;
    }

    @Override
    public String toString() {
        return "PpWealthEvent{" +
                "historyId=" + historyId +
                ", userId=" + userId +
                ", happendTime=" + happendTime +
                ", amount=" + amount +
                ", bizCode=" + bizCode +
                '}';
    }
}
