package fm.lizhi.pp.vip.bean.req;

import lombok.Data;

import java.util.List;

/**
 * 获取装饰列表请求
 * <AUTHOR>
 * @date 2023/7/12 下午4:14
 * @description
 */
@Data
public class GetDecorateListReq {

    /**
     * 主键
     */
    private Long id;
    /**
     * id列表
     */
    private List<Long> ids;
    /**
     * 物品类型 1：座驾、2：气泡、3：直播间背景、4：头像框、5：CP背景
     */
    private Integer type;
    /**
     * 物品名称
     */
    private String name;
    /**
     * 第几页
     */
    private int pageNum;
    /**
     * 每页大小
     */
    private int pageSize;

}
