package fm.lizhi.pp.vip.api;

import java.util.List;
import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.pp.vip.protocol.MountProto.RequestGetUsersMountIsUsing;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseGetUsersMountIsUsing;
import fm.lizhi.pp.vip.protocol.MountProto.RequestGetUsersMount;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseGetUsersMount;
import fm.lizhi.pp.vip.protocol.MountProto.RequestBatchGetUsersMountIsUsing;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseBatchGetUsersMountIsUsing;
import fm.lizhi.pp.vip.protocol.MountProto.RequestBatchGetUsersMount;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseBatchGetUsersMount;
import fm.lizhi.pp.vip.protocol.MountProto.RequestGiveUsersMount;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseGiveUsersMount;
import fm.lizhi.pp.vip.protocol.MountProto.RequestBuyMount;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseBuyMount;
import fm.lizhi.pp.vip.protocol.MountProto.RequestBatchGetUserEnterMsg;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseBatchGetUserEnterMsg;
import fm.lizhi.pp.vip.protocol.MountProto.RequestGetUserEnterMsg;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseGetUserEnterMsg;
import fm.lizhi.pp.vip.protocol.MountProto.RequestAddUserEnterMsg;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseAddUserEnterMsg;
import fm.lizhi.pp.vip.protocol.MountProto.RequestUseMount;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseUseMount;
import fm.lizhi.pp.vip.protocol.MountProto.RequestAddUsersMount;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseAddUsersMount;
import fm.lizhi.pp.vip.protocol.MountProto.RequestAddUsersMountInAct;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseAddUsersMountInAct;
import fm.lizhi.pp.vip.protocol.MountProto.RequestBatchAddUserMount;
import fm.lizhi.pp.vip.protocol.MountProto.ResponseBatchAddUserMount;
import fm.lizhi.pp.vip.protocol.MountProto.UserMount;
import fm.lizhi.pp.vip.protocol.MountProto.UserMsgEnter;
import fm.lizhi.pp.vip.protocol.MountProto.UserMsg;
import fm.lizhi.pp.vip.protocol.MountProto.UserMounts;
import fm.lizhi.pp.vip.protocol.MountProto.batchAddUserMountParam;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface MountService {
	
	
	/**
	 *  获取用户正在使用的座驾
	 *
	 * @param userId
	 *            用户ID
	 * @return 
	 */
	@Service(domain = 364, op = 1001, request = RequestGetUsersMountIsUsing.class, response = ResponseGetUsersMountIsUsing.class)
	@Return(resultType = ResponseGetUsersMountIsUsing.class)
	Result<ResponseGetUsersMountIsUsing> getUsersMountIsUsing(@Attribute(name = "userId") long userId);
	
	
	/**
	 *  获取用户拥有的座驾
	 *
	 * @param userId
	 *            用户ID
	 * @return 
	 */
	@Service(domain = 364, op = 1002, request = RequestGetUsersMount.class, response = ResponseGetUsersMount.class)
	@Return(resultType = ResponseGetUsersMount.class)
	Result<ResponseGetUsersMount> getUsersMount(@Attribute(name = "userId") long userId);
	
	
	/**
	 *  批量获取用户正在使用的座驾
	 *
	 * @param userIds
	 *            用户ID
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 失败<br>
	 */
	@Service(domain = 364, op = 1003, request = RequestBatchGetUsersMountIsUsing.class, response = ResponseBatchGetUsersMountIsUsing.class)
	@Return(resultType = ResponseBatchGetUsersMountIsUsing.class)
	Result<ResponseBatchGetUsersMountIsUsing> batchGetUsersMountIsUsing(@Attribute(name = "userIds", genericType = Long.class) List<Long> userIds);
	
	
	/**
	 *  批量获取用户拥有的座驾
	 *
	 * @param userIds
	 *            用户ID
	 * @return 
	 *     //if rcode == 1 参数非法<br>
	 *     //if rcode == 2 失败<br>
	 */
	@Service(domain = 364, op = 1004, request = RequestBatchGetUsersMount.class, response = ResponseBatchGetUsersMount.class)
	@Return(resultType = ResponseBatchGetUsersMount.class)
	Result<ResponseBatchGetUsersMount> batchGetUsersMount(@Attribute(name = "userIds", genericType = Long.class) List<Long> userIds);
	
	
	/**
	 *  （运营后台）给用户发放指定座驾(下面有可以指定发放座驾的个数)
	 *
	 * @param userId
	 *            用户ID
	 * @param mountId
	 *            座驾ID
	 * @param beginTime
	 *            开始时间 0:以座驾信息为准,时间叠加 ps:1538115523000
	 * @param endTime
	 *            结束时间 0:以座驾信息为准,时间叠加 ps:1538115523000
	 * @param useNow
	 *            是否让用户立马使用该座驾
	 * @return 
	 */
	@Service(domain = 364, op = 1005, request = RequestGiveUsersMount.class, response = ResponseGiveUsersMount.class)
	@Return(resultType = ResponseGiveUsersMount.class)
	Result<ResponseGiveUsersMount> giveUsersMount(@Attribute(name = "userId", genericType = Long.class) List<Long> userId, @Attribute(name = "mountId") long mountId, @Attribute(name = "beginTime") long beginTime, @Attribute(name = "endTime") long endTime, @Attribute(name = "useNow") boolean useNow);
	
	
	/**
	 *  购买座驾
	 *
	 * @param userId
	 *            用户ID
	 * @param mountId
	 *            座驾ID
	 * @param count
	 *            个数
	 * @param costType
	 *            续费的消费类型：0金币 1碎片
	 * @param operationType
	 *            操作类型。0购买 1续费
	 * @return 
	 *     //if rcode == 1 座驾下架(不存在)<br>
	 *     //if rcode == 2 碎片不足<br>
	 *     //if rcode == 3 金币不足<br>
	 *     //if rcode == 4 已购买<br>
	 *     //if rcode == 5 没有座驾不能续费<br>
	 *     //if rcode == 10 参数非法<br>
	 *     //if rcode == 500 内部错误<br>
	 */
	@Service(domain = 364, op = 1006, request = RequestBuyMount.class, response = ResponseBuyMount.class)
	@Return(resultType = ResponseBuyMount.class)
	Result<ResponseBuyMount> buyMount(@Attribute(name = "userId") long userId, @Attribute(name = "mountId") long mountId, @Attribute(name = "count") int count, @Attribute(name = "costType") int costType, @Attribute(name = "operationType") int operationType);
	
	
	/**
	 *  批量获取进房公告文本用户座驾
	 *
	 * @param userMsgEnter
	 *            用户进房消息
	 * @return 
	 */
	@Service(domain = 364, op = 1007, request = RequestBatchGetUserEnterMsg.class, response = ResponseBatchGetUserEnterMsg.class)
	@Return(resultType = ResponseBatchGetUserEnterMsg.class)
	Result<ResponseBatchGetUserEnterMsg> batchGetUserEnterMsg(@Attribute(name = "userMsgEnter", genericType = UserMsgEnter.class) List<UserMsgEnter> userMsgEnter);
	
	
	/**
	 *  获取进房公告文本用户座驾
	 *
	 * @param userMsgEnter
	 *            用户进房消息
	 * @return 
	 */
	@Service(domain = 364, op = 1008, request = RequestGetUserEnterMsg.class, response = ResponseGetUserEnterMsg.class)
	@Return(resultType = ResponseGetUserEnterMsg.class)
	Result<ResponseGetUserEnterMsg> getUserEnterMsg(@Attribute(name = "userMsgEnter") UserMsgEnter userMsgEnter);
	
	
	/**
	 *  添加用户进房公告文本
	 *
	 * @param id
	 *            关联id
	 * @param msg
	 *            文本
	 * @return 
	 */
	@Service(domain = 364, op = 1009, request = RequestAddUserEnterMsg.class, response = ResponseAddUserEnterMsg.class)
	@Return(resultType = ResponseAddUserEnterMsg.class)
	Result<ResponseAddUserEnterMsg> addUserEnterMsg(@Attribute(name = "id") long id, @Attribute(name = "msg") String msg);
	
	
	/**
	 *  使用座驾
	 *
	 * @param userId
	 *            用户ID
	 * @param mountId
	 *            座驾ID
	 * @return 
	 *     //if rcode == 1 未拥有改座驾<br>
	 *     //if rcode == 10 参数非法<br>
	 *     //if rcode == 500 内部错误<br>
	 */
	@Service(domain = 364, op = 1010, request = RequestUseMount.class, response = ResponseUseMount.class)
	@Return(resultType = ResponseUseMount.class)
	Result<ResponseUseMount> useMount(@Attribute(name = "userId") long userId, @Attribute(name = "mountId") long mountId);
	
	
	/**
	 *  给用户添加指定座驾
	 *
	 * @param userId
	 *            用户ID
	 * @param mountId
	 *            座驾ID
	 * @param count
	 *            座驾个数 开始时间和结束时间为0的时候才可以生效
	 * @param beginTime
	 *            开始时间 0:以座驾信息为准,时间叠加 ps:1538115523000
	 * @param endTime
	 *            结束时间 0:以座驾信息为准,时间叠加 ps:1538115523000
	 * @return 
	 *     //if rcode == 1 该座驾不存在<br>
	 *     //if rcode == 2 参数有误<br>
	 */
	@Service(domain = 364, op = 1011, request = RequestAddUsersMount.class, response = ResponseAddUsersMount.class)
	@Return(resultType = ResponseAddUsersMount.class)
	Result<ResponseAddUsersMount> addUsersMount(@Attribute(name = "userId", genericType = Long.class) List<Long> userId, @Attribute(name = "mountId") long mountId, @Attribute(name = "count") int count, @Attribute(name = "beginTime") long beginTime, @Attribute(name = "endTime") long endTime);
	
	
	/**
	 *  给用户添加指定座驾(活动专用)
	 *
	 * @param userIds
	 *            用户ID
	 * @param mountId
	 *            座驾ID
	 * @param time
	 *            发放时间(单位：毫秒)
	 * @param count
	 *            座驾个数 【 有效时间 = time + count * 座驾有效期 】
	 * @return 
	 *     //if rcode == 1 该座驾不存在<br>
	 *     //if rcode == 2 参数有误<br>
	 */
	@Service(domain = 364, op = 1012, request = RequestAddUsersMountInAct.class, response = ResponseAddUsersMountInAct.class)
	@Return(resultType = ResponseAddUsersMountInAct.class)
	Result<ResponseAddUsersMountInAct> addUsersMountInAct(@Attribute(name = "userIds", genericType = Long.class) List<Long> userIds, @Attribute(name = "mountId") long mountId, @Attribute(name = "time") long time, @Attribute(name = "count") int count);
	
	
	/**
	 *  批量给用户添加指定座驾(一个发失败不会导致后面失败，但是rCode会返回非0数字)
	 *
	 * @param param
	 *            批量发用户座驾参数
	 * @return 
	 *     //if rcode == 1 该座驾不存在<br>
	 *     //if rcode == 2 参数有误<br>
	 */
	@Service(domain = 364, op = 1013, request = RequestBatchAddUserMount.class, response = ResponseBatchAddUserMount.class)
	@Return(resultType = ResponseBatchAddUserMount.class)
	Result<ResponseBatchAddUserMount> batchAddUserMount(@Attribute(name = "param", genericType = batchAddUserMountParam.class) List<batchAddUserMountParam> param);
	
	
	public static final int BATCH_GET_USERS_MOUNT_IS_USING_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int BATCH_GET_USERS_MOUNT_IS_USING_FAIL = 2; // 失败

	public static final int BATCH_GET_USERS_MOUNT_ILLEGAL_PARAMS = 1; // 参数非法
	public static final int BATCH_GET_USERS_MOUNT_FAIL = 2; // 失败

	public static final int BUY_MOUNT_MOUNT_NOT_FOUND = 1; // 座驾下架(不存在)
	public static final int BUY_MOUNT_FRAGMENT_NOT_ENOUGH = 2; // 碎片不足
	public static final int BUY_MOUNT_COIN_NOT_ENOUGH = 3; // 金币不足
	public static final int BUY_MOUNT_ALWARY_HAS_MOUNT = 4; // 已购买
	public static final int BUY_MOUNT_HAS_NOT_MOUNT = 5; // 没有座驾不能续费
	public static final int BUY_MOUNT_ILLEGAL_PARAMS = 10; // 参数非法
	public static final int BUY_MOUNT_ERROR = 500; // 内部错误

	public static final int USE_MOUNT_MOUNT_NOT_FOUND = 1; // 未拥有改座驾
	public static final int USE_MOUNT_ILLEGAL_PARAMS = 10; // 参数非法
	public static final int USE_MOUNT_ERROR = 500; // 内部错误

	public static final int ADD_USERS_MOUNT_MOUNT_NOT_FOUND = 1; // 该座驾不存在
	public static final int ADD_USERS_MOUNT_ILLEGAL_PARAMS = 2; // 参数有误

	public static final int ADD_USERS_MOUNT_IN_ACT_MOUNT_NOT_FOUND = 1; // 该座驾不存在
	public static final int ADD_USERS_MOUNT_IN_ACT_ILLEGAL_PARAMS = 2; // 参数有误

	public static final int BATCH_ADD_USER_MOUNT_MOUNT_NOT_FOUND = 1; // 该座驾不存在
	public static final int BATCH_ADD_USER_MOUNT_ILLEGAL_PARAMS = 2; // 参数有误


}