// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protocol_vehicle_enter.proto

package fm.lizhi.live.enternotice.protocol;

public final class VehicleEnterProto {
  private VehicleEnterProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestVehicleEnterLiveOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated string enterEnties = 1;
    /**
     * <code>repeated string enterEnties = 1;</code>
     *
     * <pre>
     *请求发送进房公告的实体 (EnterVo的Json串)
     * </pre>
     */
    java.util.List<java.lang.String>
    getEnterEntiesList();
    /**
     * <code>repeated string enterEnties = 1;</code>
     *
     * <pre>
     *请求发送进房公告的实体 (EnterVo的Json串)
     * </pre>
     */
    int getEnterEntiesCount();
    /**
     * <code>repeated string enterEnties = 1;</code>
     *
     * <pre>
     *请求发送进房公告的实体 (EnterVo的Json串)
     * </pre>
     */
    java.lang.String getEnterEnties(int index);
    /**
     * <code>repeated string enterEnties = 1;</code>
     *
     * <pre>
     *请求发送进房公告的实体 (EnterVo的Json串)
     * </pre>
     */
    com.google.protobuf.ByteString
        getEnterEntiesBytes(int index);
  }
  /**
   * Protobuf type {@code RequestVehicleEnterLive}
   *
   * <pre>
   * VehicleEnterService.java
   *发送座驾进房公告
   * domain = 616, op = 100
   * </pre>
   */
  public static final class RequestVehicleEnterLive extends
      com.google.protobuf.GeneratedMessage
      implements RequestVehicleEnterLiveOrBuilder {
    // Use RequestVehicleEnterLive.newBuilder() to construct.
    private RequestVehicleEnterLive(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestVehicleEnterLive(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestVehicleEnterLive defaultInstance;
    public static RequestVehicleEnterLive getDefaultInstance() {
      return defaultInstance;
    }

    public RequestVehicleEnterLive getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestVehicleEnterLive(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                enterEnties_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000001;
              }
              enterEnties_.add(input.readBytes());
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          enterEnties_ = new com.google.protobuf.UnmodifiableLazyStringList(enterEnties_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_RequestVehicleEnterLive_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_RequestVehicleEnterLive_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive.class, fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestVehicleEnterLive> PARSER =
        new com.google.protobuf.AbstractParser<RequestVehicleEnterLive>() {
      public RequestVehicleEnterLive parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestVehicleEnterLive(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestVehicleEnterLive> getParserForType() {
      return PARSER;
    }

    // repeated string enterEnties = 1;
    public static final int ENTERENTIES_FIELD_NUMBER = 1;
    private com.google.protobuf.LazyStringList enterEnties_;
    /**
     * <code>repeated string enterEnties = 1;</code>
     *
     * <pre>
     *请求发送进房公告的实体 (EnterVo的Json串)
     * </pre>
     */
    public java.util.List<java.lang.String>
        getEnterEntiesList() {
      return enterEnties_;
    }
    /**
     * <code>repeated string enterEnties = 1;</code>
     *
     * <pre>
     *请求发送进房公告的实体 (EnterVo的Json串)
     * </pre>
     */
    public int getEnterEntiesCount() {
      return enterEnties_.size();
    }
    /**
     * <code>repeated string enterEnties = 1;</code>
     *
     * <pre>
     *请求发送进房公告的实体 (EnterVo的Json串)
     * </pre>
     */
    public java.lang.String getEnterEnties(int index) {
      return enterEnties_.get(index);
    }
    /**
     * <code>repeated string enterEnties = 1;</code>
     *
     * <pre>
     *请求发送进房公告的实体 (EnterVo的Json串)
     * </pre>
     */
    public com.google.protobuf.ByteString
        getEnterEntiesBytes(int index) {
      return enterEnties_.getByteString(index);
    }

    private void initFields() {
      enterEnties_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < enterEnties_.size(); i++) {
        output.writeBytes(1, enterEnties_.getByteString(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < enterEnties_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(enterEnties_.getByteString(i));
        }
        size += dataSize;
        size += 1 * getEnterEntiesList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code RequestVehicleEnterLive}
     *
     * <pre>
     * VehicleEnterService.java
     *发送座驾进房公告
     * domain = 616, op = 100
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLiveOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_RequestVehicleEnterLive_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_RequestVehicleEnterLive_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive.class, fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive.Builder.class);
      }

      // Construct using fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        enterEnties_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_RequestVehicleEnterLive_descriptor;
      }

      public fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive getDefaultInstanceForType() {
        return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive.getDefaultInstance();
      }

      public fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive build() {
        fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive buildPartial() {
        fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive result = new fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          enterEnties_ = new com.google.protobuf.UnmodifiableLazyStringList(
              enterEnties_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.enterEnties_ = enterEnties_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive) {
          return mergeFrom((fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive other) {
        if (other == fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive.getDefaultInstance()) return this;
        if (!other.enterEnties_.isEmpty()) {
          if (enterEnties_.isEmpty()) {
            enterEnties_ = other.enterEnties_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureEnterEntiesIsMutable();
            enterEnties_.addAll(other.enterEnties_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterLive) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated string enterEnties = 1;
      private com.google.protobuf.LazyStringList enterEnties_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureEnterEntiesIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          enterEnties_ = new com.google.protobuf.LazyStringArrayList(enterEnties_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated string enterEnties = 1;</code>
       *
       * <pre>
       *请求发送进房公告的实体 (EnterVo的Json串)
       * </pre>
       */
      public java.util.List<java.lang.String>
          getEnterEntiesList() {
        return java.util.Collections.unmodifiableList(enterEnties_);
      }
      /**
       * <code>repeated string enterEnties = 1;</code>
       *
       * <pre>
       *请求发送进房公告的实体 (EnterVo的Json串)
       * </pre>
       */
      public int getEnterEntiesCount() {
        return enterEnties_.size();
      }
      /**
       * <code>repeated string enterEnties = 1;</code>
       *
       * <pre>
       *请求发送进房公告的实体 (EnterVo的Json串)
       * </pre>
       */
      public java.lang.String getEnterEnties(int index) {
        return enterEnties_.get(index);
      }
      /**
       * <code>repeated string enterEnties = 1;</code>
       *
       * <pre>
       *请求发送进房公告的实体 (EnterVo的Json串)
       * </pre>
       */
      public com.google.protobuf.ByteString
          getEnterEntiesBytes(int index) {
        return enterEnties_.getByteString(index);
      }
      /**
       * <code>repeated string enterEnties = 1;</code>
       *
       * <pre>
       *请求发送进房公告的实体 (EnterVo的Json串)
       * </pre>
       */
      public Builder setEnterEnties(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureEnterEntiesIsMutable();
        enterEnties_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string enterEnties = 1;</code>
       *
       * <pre>
       *请求发送进房公告的实体 (EnterVo的Json串)
       * </pre>
       */
      public Builder addEnterEnties(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureEnterEntiesIsMutable();
        enterEnties_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string enterEnties = 1;</code>
       *
       * <pre>
       *请求发送进房公告的实体 (EnterVo的Json串)
       * </pre>
       */
      public Builder addAllEnterEnties(
          java.lang.Iterable<java.lang.String> values) {
        ensureEnterEntiesIsMutable();
        super.addAll(values, enterEnties_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string enterEnties = 1;</code>
       *
       * <pre>
       *请求发送进房公告的实体 (EnterVo的Json串)
       * </pre>
       */
      public Builder clearEnterEnties() {
        enterEnties_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string enterEnties = 1;</code>
       *
       * <pre>
       *请求发送进房公告的实体 (EnterVo的Json串)
       * </pre>
       */
      public Builder addEnterEntiesBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureEnterEntiesIsMutable();
        enterEnties_.add(value);
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:RequestVehicleEnterLive)
    }

    static {
      defaultInstance = new RequestVehicleEnterLive(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:RequestVehicleEnterLive)
  }

  public interface RequestVehicleEnterNoticesByRangeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 liveId = 1;
    /**
     * <code>optional int64 liveId = 1;</code>
     *
     * <pre>
     *liveId 直播节目Id
     * </pre>
     */
    boolean hasLiveId();
    /**
     * <code>optional int64 liveId = 1;</code>
     *
     * <pre>
     *liveId 直播节目Id
     * </pre>
     */
    long getLiveId();

    // optional int64 startTimeStamp = 2;
    /**
     * <code>optional int64 startTimeStamp = 2;</code>
     *
     * <pre>
     *段开始的时间戳 微秒数
     * </pre>
     */
    boolean hasStartTimeStamp();
    /**
     * <code>optional int64 startTimeStamp = 2;</code>
     *
     * <pre>
     *段开始的时间戳 微秒数
     * </pre>
     */
    long getStartTimeStamp();

    // optional int64 endTimeStamp = 3;
    /**
     * <code>optional int64 endTimeStamp = 3;</code>
     *
     * <pre>
     *段结束的时间戳 微秒数
     * </pre>
     */
    boolean hasEndTimeStamp();
    /**
     * <code>optional int64 endTimeStamp = 3;</code>
     *
     * <pre>
     *段结束的时间戳 微秒数
     * </pre>
     */
    long getEndTimeStamp();
  }
  /**
   * Protobuf type {@code RequestVehicleEnterNoticesByRange}
   *
   * <pre>
   * VehicleEnterService.java
   *请求获取座驾进房公告
   * domain = 616, op = 101
   * </pre>
   */
  public static final class RequestVehicleEnterNoticesByRange extends
      com.google.protobuf.GeneratedMessage
      implements RequestVehicleEnterNoticesByRangeOrBuilder {
    // Use RequestVehicleEnterNoticesByRange.newBuilder() to construct.
    private RequestVehicleEnterNoticesByRange(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestVehicleEnterNoticesByRange(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestVehicleEnterNoticesByRange defaultInstance;
    public static RequestVehicleEnterNoticesByRange getDefaultInstance() {
      return defaultInstance;
    }

    public RequestVehicleEnterNoticesByRange getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestVehicleEnterNoticesByRange(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              liveId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              startTimeStamp_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              endTimeStamp_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_RequestVehicleEnterNoticesByRange_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_RequestVehicleEnterNoticesByRange_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange.class, fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestVehicleEnterNoticesByRange> PARSER =
        new com.google.protobuf.AbstractParser<RequestVehicleEnterNoticesByRange>() {
      public RequestVehicleEnterNoticesByRange parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestVehicleEnterNoticesByRange(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestVehicleEnterNoticesByRange> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 liveId = 1;
    public static final int LIVEID_FIELD_NUMBER = 1;
    private long liveId_;
    /**
     * <code>optional int64 liveId = 1;</code>
     *
     * <pre>
     *liveId 直播节目Id
     * </pre>
     */
    public boolean hasLiveId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 liveId = 1;</code>
     *
     * <pre>
     *liveId 直播节目Id
     * </pre>
     */
    public long getLiveId() {
      return liveId_;
    }

    // optional int64 startTimeStamp = 2;
    public static final int STARTTIMESTAMP_FIELD_NUMBER = 2;
    private long startTimeStamp_;
    /**
     * <code>optional int64 startTimeStamp = 2;</code>
     *
     * <pre>
     *段开始的时间戳 微秒数
     * </pre>
     */
    public boolean hasStartTimeStamp() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 startTimeStamp = 2;</code>
     *
     * <pre>
     *段开始的时间戳 微秒数
     * </pre>
     */
    public long getStartTimeStamp() {
      return startTimeStamp_;
    }

    // optional int64 endTimeStamp = 3;
    public static final int ENDTIMESTAMP_FIELD_NUMBER = 3;
    private long endTimeStamp_;
    /**
     * <code>optional int64 endTimeStamp = 3;</code>
     *
     * <pre>
     *段结束的时间戳 微秒数
     * </pre>
     */
    public boolean hasEndTimeStamp() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int64 endTimeStamp = 3;</code>
     *
     * <pre>
     *段结束的时间戳 微秒数
     * </pre>
     */
    public long getEndTimeStamp() {
      return endTimeStamp_;
    }

    private void initFields() {
      liveId_ = 0L;
      startTimeStamp_ = 0L;
      endTimeStamp_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, liveId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, startTimeStamp_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(3, endTimeStamp_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, liveId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, startTimeStamp_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, endTimeStamp_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code RequestVehicleEnterNoticesByRange}
     *
     * <pre>
     * VehicleEnterService.java
     *请求获取座驾进房公告
     * domain = 616, op = 101
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRangeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_RequestVehicleEnterNoticesByRange_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_RequestVehicleEnterNoticesByRange_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange.class, fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange.Builder.class);
      }

      // Construct using fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        liveId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        startTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        endTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_RequestVehicleEnterNoticesByRange_descriptor;
      }

      public fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange getDefaultInstanceForType() {
        return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange.getDefaultInstance();
      }

      public fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange build() {
        fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange buildPartial() {
        fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange result = new fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.liveId_ = liveId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.startTimeStamp_ = startTimeStamp_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.endTimeStamp_ = endTimeStamp_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange) {
          return mergeFrom((fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange other) {
        if (other == fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange.getDefaultInstance()) return this;
        if (other.hasLiveId()) {
          setLiveId(other.getLiveId());
        }
        if (other.hasStartTimeStamp()) {
          setStartTimeStamp(other.getStartTimeStamp());
        }
        if (other.hasEndTimeStamp()) {
          setEndTimeStamp(other.getEndTimeStamp());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.live.enternotice.protocol.VehicleEnterProto.RequestVehicleEnterNoticesByRange) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 liveId = 1;
      private long liveId_ ;
      /**
       * <code>optional int64 liveId = 1;</code>
       *
       * <pre>
       *liveId 直播节目Id
       * </pre>
       */
      public boolean hasLiveId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 liveId = 1;</code>
       *
       * <pre>
       *liveId 直播节目Id
       * </pre>
       */
      public long getLiveId() {
        return liveId_;
      }
      /**
       * <code>optional int64 liveId = 1;</code>
       *
       * <pre>
       *liveId 直播节目Id
       * </pre>
       */
      public Builder setLiveId(long value) {
        bitField0_ |= 0x00000001;
        liveId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 liveId = 1;</code>
       *
       * <pre>
       *liveId 直播节目Id
       * </pre>
       */
      public Builder clearLiveId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        liveId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 startTimeStamp = 2;
      private long startTimeStamp_ ;
      /**
       * <code>optional int64 startTimeStamp = 2;</code>
       *
       * <pre>
       *段开始的时间戳 微秒数
       * </pre>
       */
      public boolean hasStartTimeStamp() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 startTimeStamp = 2;</code>
       *
       * <pre>
       *段开始的时间戳 微秒数
       * </pre>
       */
      public long getStartTimeStamp() {
        return startTimeStamp_;
      }
      /**
       * <code>optional int64 startTimeStamp = 2;</code>
       *
       * <pre>
       *段开始的时间戳 微秒数
       * </pre>
       */
      public Builder setStartTimeStamp(long value) {
        bitField0_ |= 0x00000002;
        startTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 startTimeStamp = 2;</code>
       *
       * <pre>
       *段开始的时间戳 微秒数
       * </pre>
       */
      public Builder clearStartTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000002);
        startTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 endTimeStamp = 3;
      private long endTimeStamp_ ;
      /**
       * <code>optional int64 endTimeStamp = 3;</code>
       *
       * <pre>
       *段结束的时间戳 微秒数
       * </pre>
       */
      public boolean hasEndTimeStamp() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int64 endTimeStamp = 3;</code>
       *
       * <pre>
       *段结束的时间戳 微秒数
       * </pre>
       */
      public long getEndTimeStamp() {
        return endTimeStamp_;
      }
      /**
       * <code>optional int64 endTimeStamp = 3;</code>
       *
       * <pre>
       *段结束的时间戳 微秒数
       * </pre>
       */
      public Builder setEndTimeStamp(long value) {
        bitField0_ |= 0x00000004;
        endTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 endTimeStamp = 3;</code>
       *
       * <pre>
       *段结束的时间戳 微秒数
       * </pre>
       */
      public Builder clearEndTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        endTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:RequestVehicleEnterNoticesByRange)
    }

    static {
      defaultInstance = new RequestVehicleEnterNoticesByRange(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:RequestVehicleEnterNoticesByRange)
  }

  public interface ResponseVehicleEnterNoticesByRangeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int64 liveId = 1;
    /**
     * <code>optional int64 liveId = 1;</code>
     *
     * <pre>
     *liveId 直播节目Id
     * </pre>
     */
    boolean hasLiveId();
    /**
     * <code>optional int64 liveId = 1;</code>
     *
     * <pre>
     *liveId 直播节目Id
     * </pre>
     */
    long getLiveId();

    // optional int64 startTimeStamp = 2;
    /**
     * <code>optional int64 startTimeStamp = 2;</code>
     *
     * <pre>
     *开始时间戳，微秒数
     * </pre>
     */
    boolean hasStartTimeStamp();
    /**
     * <code>optional int64 startTimeStamp = 2;</code>
     *
     * <pre>
     *开始时间戳，微秒数
     * </pre>
     */
    long getStartTimeStamp();

    // optional int64 endTimeStamp = 3;
    /**
     * <code>optional int64 endTimeStamp = 3;</code>
     *
     * <pre>
     *结束时间戳，微秒数
     * </pre>
     */
    boolean hasEndTimeStamp();
    /**
     * <code>optional int64 endTimeStamp = 3;</code>
     *
     * <pre>
     *结束时间戳，微秒数
     * </pre>
     */
    long getEndTimeStamp();

    // repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;
    /**
     * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
     *
     * <pre>
     *进房公告的记录
     * </pre>
     */
    java.util.List<fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry> 
        getEnterNoticesList();
    /**
     * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
     *
     * <pre>
     *进房公告的记录
     * </pre>
     */
    fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry getEnterNotices(int index);
    /**
     * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
     *
     * <pre>
     *进房公告的记录
     * </pre>
     */
    int getEnterNoticesCount();
    /**
     * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
     *
     * <pre>
     *进房公告的记录
     * </pre>
     */
    java.util.List<? extends fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntryOrBuilder> 
        getEnterNoticesOrBuilderList();
    /**
     * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
     *
     * <pre>
     *进房公告的记录
     * </pre>
     */
    fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntryOrBuilder getEnterNoticesOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code ResponseVehicleEnterNoticesByRange}
   */
  public static final class ResponseVehicleEnterNoticesByRange extends
      com.google.protobuf.GeneratedMessage
      implements ResponseVehicleEnterNoticesByRangeOrBuilder {
    // Use ResponseVehicleEnterNoticesByRange.newBuilder() to construct.
    private ResponseVehicleEnterNoticesByRange(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseVehicleEnterNoticesByRange(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseVehicleEnterNoticesByRange defaultInstance;
    public static ResponseVehicleEnterNoticesByRange getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseVehicleEnterNoticesByRange getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseVehicleEnterNoticesByRange(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              liveId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              startTimeStamp_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              endTimeStamp_ = input.readInt64();
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                enterNotices_ = new java.util.ArrayList<fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry>();
                mutable_bitField0_ |= 0x00000008;
              }
              enterNotices_.add(input.readMessage(fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          enterNotices_ = java.util.Collections.unmodifiableList(enterNotices_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_ResponseVehicleEnterNoticesByRange_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_ResponseVehicleEnterNoticesByRange_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange.class, fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseVehicleEnterNoticesByRange> PARSER =
        new com.google.protobuf.AbstractParser<ResponseVehicleEnterNoticesByRange>() {
      public ResponseVehicleEnterNoticesByRange parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseVehicleEnterNoticesByRange(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseVehicleEnterNoticesByRange> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int64 liveId = 1;
    public static final int LIVEID_FIELD_NUMBER = 1;
    private long liveId_;
    /**
     * <code>optional int64 liveId = 1;</code>
     *
     * <pre>
     *liveId 直播节目Id
     * </pre>
     */
    public boolean hasLiveId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int64 liveId = 1;</code>
     *
     * <pre>
     *liveId 直播节目Id
     * </pre>
     */
    public long getLiveId() {
      return liveId_;
    }

    // optional int64 startTimeStamp = 2;
    public static final int STARTTIMESTAMP_FIELD_NUMBER = 2;
    private long startTimeStamp_;
    /**
     * <code>optional int64 startTimeStamp = 2;</code>
     *
     * <pre>
     *开始时间戳，微秒数
     * </pre>
     */
    public boolean hasStartTimeStamp() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 startTimeStamp = 2;</code>
     *
     * <pre>
     *开始时间戳，微秒数
     * </pre>
     */
    public long getStartTimeStamp() {
      return startTimeStamp_;
    }

    // optional int64 endTimeStamp = 3;
    public static final int ENDTIMESTAMP_FIELD_NUMBER = 3;
    private long endTimeStamp_;
    /**
     * <code>optional int64 endTimeStamp = 3;</code>
     *
     * <pre>
     *结束时间戳，微秒数
     * </pre>
     */
    public boolean hasEndTimeStamp() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int64 endTimeStamp = 3;</code>
     *
     * <pre>
     *结束时间戳，微秒数
     * </pre>
     */
    public long getEndTimeStamp() {
      return endTimeStamp_;
    }

    // repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;
    public static final int ENTERNOTICES_FIELD_NUMBER = 4;
    private java.util.List<fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry> enterNotices_;
    /**
     * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
     *
     * <pre>
     *进房公告的记录
     * </pre>
     */
    public java.util.List<fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry> getEnterNoticesList() {
      return enterNotices_;
    }
    /**
     * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
     *
     * <pre>
     *进房公告的记录
     * </pre>
     */
    public java.util.List<? extends fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntryOrBuilder> 
        getEnterNoticesOrBuilderList() {
      return enterNotices_;
    }
    /**
     * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
     *
     * <pre>
     *进房公告的记录
     * </pre>
     */
    public int getEnterNoticesCount() {
      return enterNotices_.size();
    }
    /**
     * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
     *
     * <pre>
     *进房公告的记录
     * </pre>
     */
    public fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry getEnterNotices(int index) {
      return enterNotices_.get(index);
    }
    /**
     * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
     *
     * <pre>
     *进房公告的记录
     * </pre>
     */
    public fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntryOrBuilder getEnterNoticesOrBuilder(
        int index) {
      return enterNotices_.get(index);
    }

    private void initFields() {
      liveId_ = 0L;
      startTimeStamp_ = 0L;
      endTimeStamp_ = 0L;
      enterNotices_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, liveId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, startTimeStamp_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(3, endTimeStamp_);
      }
      for (int i = 0; i < enterNotices_.size(); i++) {
        output.writeMessage(4, enterNotices_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, liveId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, startTimeStamp_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, endTimeStamp_);
      }
      for (int i = 0; i < enterNotices_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, enterNotices_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ResponseVehicleEnterNoticesByRange}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRangeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_ResponseVehicleEnterNoticesByRange_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_ResponseVehicleEnterNoticesByRange_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange.class, fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange.Builder.class);
      }

      // Construct using fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getEnterNoticesFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        liveId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        startTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        endTimeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (enterNoticesBuilder_ == null) {
          enterNotices_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          enterNoticesBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.internal_static_ResponseVehicleEnterNoticesByRange_descriptor;
      }

      public fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange getDefaultInstanceForType() {
        return fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange.getDefaultInstance();
      }

      public fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange build() {
        fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange buildPartial() {
        fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange result = new fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.liveId_ = liveId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.startTimeStamp_ = startTimeStamp_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.endTimeStamp_ = endTimeStamp_;
        if (enterNoticesBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008)) {
            enterNotices_ = java.util.Collections.unmodifiableList(enterNotices_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.enterNotices_ = enterNotices_;
        } else {
          result.enterNotices_ = enterNoticesBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange) {
          return mergeFrom((fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange other) {
        if (other == fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange.getDefaultInstance()) return this;
        if (other.hasLiveId()) {
          setLiveId(other.getLiveId());
        }
        if (other.hasStartTimeStamp()) {
          setStartTimeStamp(other.getStartTimeStamp());
        }
        if (other.hasEndTimeStamp()) {
          setEndTimeStamp(other.getEndTimeStamp());
        }
        if (enterNoticesBuilder_ == null) {
          if (!other.enterNotices_.isEmpty()) {
            if (enterNotices_.isEmpty()) {
              enterNotices_ = other.enterNotices_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureEnterNoticesIsMutable();
              enterNotices_.addAll(other.enterNotices_);
            }
            onChanged();
          }
        } else {
          if (!other.enterNotices_.isEmpty()) {
            if (enterNoticesBuilder_.isEmpty()) {
              enterNoticesBuilder_.dispose();
              enterNoticesBuilder_ = null;
              enterNotices_ = other.enterNotices_;
              bitField0_ = (bitField0_ & ~0x00000008);
              enterNoticesBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getEnterNoticesFieldBuilder() : null;
            } else {
              enterNoticesBuilder_.addAllMessages(other.enterNotices_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (fm.lizhi.live.enternotice.protocol.VehicleEnterProto.ResponseVehicleEnterNoticesByRange) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int64 liveId = 1;
      private long liveId_ ;
      /**
       * <code>optional int64 liveId = 1;</code>
       *
       * <pre>
       *liveId 直播节目Id
       * </pre>
       */
      public boolean hasLiveId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int64 liveId = 1;</code>
       *
       * <pre>
       *liveId 直播节目Id
       * </pre>
       */
      public long getLiveId() {
        return liveId_;
      }
      /**
       * <code>optional int64 liveId = 1;</code>
       *
       * <pre>
       *liveId 直播节目Id
       * </pre>
       */
      public Builder setLiveId(long value) {
        bitField0_ |= 0x00000001;
        liveId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 liveId = 1;</code>
       *
       * <pre>
       *liveId 直播节目Id
       * </pre>
       */
      public Builder clearLiveId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        liveId_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 startTimeStamp = 2;
      private long startTimeStamp_ ;
      /**
       * <code>optional int64 startTimeStamp = 2;</code>
       *
       * <pre>
       *开始时间戳，微秒数
       * </pre>
       */
      public boolean hasStartTimeStamp() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int64 startTimeStamp = 2;</code>
       *
       * <pre>
       *开始时间戳，微秒数
       * </pre>
       */
      public long getStartTimeStamp() {
        return startTimeStamp_;
      }
      /**
       * <code>optional int64 startTimeStamp = 2;</code>
       *
       * <pre>
       *开始时间戳，微秒数
       * </pre>
       */
      public Builder setStartTimeStamp(long value) {
        bitField0_ |= 0x00000002;
        startTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 startTimeStamp = 2;</code>
       *
       * <pre>
       *开始时间戳，微秒数
       * </pre>
       */
      public Builder clearStartTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000002);
        startTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      // optional int64 endTimeStamp = 3;
      private long endTimeStamp_ ;
      /**
       * <code>optional int64 endTimeStamp = 3;</code>
       *
       * <pre>
       *结束时间戳，微秒数
       * </pre>
       */
      public boolean hasEndTimeStamp() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int64 endTimeStamp = 3;</code>
       *
       * <pre>
       *结束时间戳，微秒数
       * </pre>
       */
      public long getEndTimeStamp() {
        return endTimeStamp_;
      }
      /**
       * <code>optional int64 endTimeStamp = 3;</code>
       *
       * <pre>
       *结束时间戳，微秒数
       * </pre>
       */
      public Builder setEndTimeStamp(long value) {
        bitField0_ |= 0x00000004;
        endTimeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 endTimeStamp = 3;</code>
       *
       * <pre>
       *结束时间戳，微秒数
       * </pre>
       */
      public Builder clearEndTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        endTimeStamp_ = 0L;
        onChanged();
        return this;
      }

      // repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;
      private java.util.List<fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry> enterNotices_ =
        java.util.Collections.emptyList();
      private void ensureEnterNoticesIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          enterNotices_ = new java.util.ArrayList<fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry>(enterNotices_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry, fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.Builder, fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntryOrBuilder> enterNoticesBuilder_;

      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public java.util.List<fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry> getEnterNoticesList() {
        if (enterNoticesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(enterNotices_);
        } else {
          return enterNoticesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public int getEnterNoticesCount() {
        if (enterNoticesBuilder_ == null) {
          return enterNotices_.size();
        } else {
          return enterNoticesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry getEnterNotices(int index) {
        if (enterNoticesBuilder_ == null) {
          return enterNotices_.get(index);
        } else {
          return enterNoticesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public Builder setEnterNotices(
          int index, fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry value) {
        if (enterNoticesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnterNoticesIsMutable();
          enterNotices_.set(index, value);
          onChanged();
        } else {
          enterNoticesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public Builder setEnterNotices(
          int index, fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.Builder builderForValue) {
        if (enterNoticesBuilder_ == null) {
          ensureEnterNoticesIsMutable();
          enterNotices_.set(index, builderForValue.build());
          onChanged();
        } else {
          enterNoticesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public Builder addEnterNotices(fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry value) {
        if (enterNoticesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnterNoticesIsMutable();
          enterNotices_.add(value);
          onChanged();
        } else {
          enterNoticesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public Builder addEnterNotices(
          int index, fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry value) {
        if (enterNoticesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnterNoticesIsMutable();
          enterNotices_.add(index, value);
          onChanged();
        } else {
          enterNoticesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public Builder addEnterNotices(
          fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.Builder builderForValue) {
        if (enterNoticesBuilder_ == null) {
          ensureEnterNoticesIsMutable();
          enterNotices_.add(builderForValue.build());
          onChanged();
        } else {
          enterNoticesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public Builder addEnterNotices(
          int index, fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.Builder builderForValue) {
        if (enterNoticesBuilder_ == null) {
          ensureEnterNoticesIsMutable();
          enterNotices_.add(index, builderForValue.build());
          onChanged();
        } else {
          enterNoticesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public Builder addAllEnterNotices(
          java.lang.Iterable<? extends fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry> values) {
        if (enterNoticesBuilder_ == null) {
          ensureEnterNoticesIsMutable();
          super.addAll(values, enterNotices_);
          onChanged();
        } else {
          enterNoticesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public Builder clearEnterNotices() {
        if (enterNoticesBuilder_ == null) {
          enterNotices_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          enterNoticesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public Builder removeEnterNotices(int index) {
        if (enterNoticesBuilder_ == null) {
          ensureEnterNoticesIsMutable();
          enterNotices_.remove(index);
          onChanged();
        } else {
          enterNoticesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.Builder getEnterNoticesBuilder(
          int index) {
        return getEnterNoticesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntryOrBuilder getEnterNoticesOrBuilder(
          int index) {
        if (enterNoticesBuilder_ == null) {
          return enterNotices_.get(index);  } else {
          return enterNoticesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public java.util.List<? extends fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntryOrBuilder> 
           getEnterNoticesOrBuilderList() {
        if (enterNoticesBuilder_ != null) {
          return enterNoticesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(enterNotices_);
        }
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.Builder addEnterNoticesBuilder() {
        return getEnterNoticesFieldBuilder().addBuilder(
            fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.Builder addEnterNoticesBuilder(
          int index) {
        return getEnterNoticesFieldBuilder().addBuilder(
            index, fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.getDefaultInstance());
      }
      /**
       * <code>repeated .fm.lizhi.live.enternotice.protocol.EnterNoticeEntry enterNotices = 4;</code>
       *
       * <pre>
       *进房公告的记录
       * </pre>
       */
      public java.util.List<fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.Builder> 
           getEnterNoticesBuilderList() {
        return getEnterNoticesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry, fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.Builder, fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntryOrBuilder> 
          getEnterNoticesFieldBuilder() {
        if (enterNoticesBuilder_ == null) {
          enterNoticesBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry, fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntry.Builder, fm.lizhi.live.enternotice.protocol.EnternoticeProto.EnterNoticeEntryOrBuilder>(
                  enterNotices_,
                  ((bitField0_ & 0x00000008) == 0x00000008),
                  getParentForChildren(),
                  isClean());
          enterNotices_ = null;
        }
        return enterNoticesBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:ResponseVehicleEnterNoticesByRange)
    }

    static {
      defaultInstance = new ResponseVehicleEnterNoticesByRange(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:ResponseVehicleEnterNoticesByRange)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_RequestVehicleEnterLive_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_RequestVehicleEnterLive_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_RequestVehicleEnterNoticesByRange_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_RequestVehicleEnterNoticesByRange_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_ResponseVehicleEnterNoticesByRange_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ResponseVehicleEnterNoticesByRange_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\034protocol_vehicle_enter.proto\032\037protocol" +
      "_live_enternotice.proto\".\n\027RequestVehicl" +
      "eEnterLive\022\023\n\013enterEnties\030\001 \003(\t\"a\n!Reque" +
      "stVehicleEnterNoticesByRange\022\016\n\006liveId\030\001" +
      " \001(\003\022\026\n\016startTimeStamp\030\002 \001(\003\022\024\n\014endTimeS" +
      "tamp\030\003 \001(\003\"\256\001\n\"ResponseVehicleEnterNotic" +
      "esByRange\022\016\n\006liveId\030\001 \001(\003\022\026\n\016startTimeSt" +
      "amp\030\002 \001(\003\022\024\n\014endTimeStamp\030\003 \001(\003\022J\n\014enter" +
      "Notices\030\004 \003(\01324.fm.lizhi.live.enternotic" +
      "e.protocol.EnterNoticeEntryB7\n\"fm.lizhi.",
      "live.enternotice.protocolB\021VehicleEnterP" +
      "roto"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_RequestVehicleEnterLive_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_RequestVehicleEnterLive_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_RequestVehicleEnterLive_descriptor,
              new java.lang.String[] { "EnterEnties", });
          internal_static_RequestVehicleEnterNoticesByRange_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_RequestVehicleEnterNoticesByRange_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_RequestVehicleEnterNoticesByRange_descriptor,
              new java.lang.String[] { "LiveId", "StartTimeStamp", "EndTimeStamp", });
          internal_static_ResponseVehicleEnterNoticesByRange_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_ResponseVehicleEnterNoticesByRange_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_ResponseVehicleEnterNoticesByRange_descriptor,
              new java.lang.String[] { "LiveId", "StartTimeStamp", "EndTimeStamp", "EnterNotices", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          fm.lizhi.live.enternotice.protocol.EnternoticeProto.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
