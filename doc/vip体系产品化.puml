@startuml
skinparam backgroundColor #EEEBDC
skinparam handwritten true
skinparam sequence {
ArrowColor DeepSkyBlue
ActorBorderColor DeepSkyBlue
LifeLineBorderColor blue
LifeLineBackgroundColor #A9DCDF

ParticipantBorderColor DeepSkyBlue
ParticipantBackgroundColor DodgerBlue
ParticipantFontName Impact
ParticipantFontSize 17
ParticipantFontColor #A9DCDF

ActorBackgroundColor aqua
ActorFontColor DeepSkyBlue
ActorFontSize 17
ActorFontName Aapex
}
database redis as redis
participant web_xm_h5 as h5
participant lz_xm_vip as vip
===前台h5功能==
h5-> user_account: 构建用户个人信息
h5->vip: 构建用户vip信息
vip->vip: 获取用户当前vip等级
vip->vip: 获取vip解锁条件与物料
vip->redis: 获取用户本周充值金额
vip->h5 :封装数据并返回
@enduml