-- 热更新功能

ALTER TABLE wave_version_push_config ADD deploy_env varchar(20) DEFAULT 'TEST' NOT NULL COMMENT '服务部署环境, TEST/PRE/PRO, 用于环境隔离';
ALTER TABLE wave_version_push_config ADD build_version bigint(20) DEFAULT 0 NOT NULL COMMENT 'build号';
ALTER TABLE wave_version_push_config ADD hot_update_min_version varchar(64) DEFAULT '' NOT NULL COMMENT '热更要求的最小版本号';
ALTER TABLE wave_version_push_config ADD hot_update_app_path varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' NOT NULL COMMENT '热更资源app.zip的下载路径, 相对路径, 斜杆开头';
ALTER TABLE wave_version_push_config ADD hot_update_app_sha varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' NOT NULL COMMENT '热更资源app.zip的sha512值';
ALTER TABLE wave_version_push_config ADD hot_update_app_size bigint(20) DEFAULT 0 NOT NULL COMMENT '热更资源app.zip的字节数';
ALTER TABLE wave_version_push_config ADD hot_update_assets_path varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' NOT NULL COMMENT '热更资源assets.zip的下载路径, 相对路径, 斜杆开头';
ALTER TABLE wave_version_push_config ADD hot_update_assets_sha varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' NOT NULL COMMENT '热更资源assets.zip的sha512值';
ALTER TABLE wave_version_push_config ADD hot_update_assets_size bigint(20) DEFAULT 0 NOT NULL COMMENT '热更资源assets.zip的字节数';
ALTER TABLE wave_version_push_config ADD hot_update_lib_path varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' NOT NULL COMMENT '热更资源lib.zip的下载路径, 相对路径, 斜杆开头';
ALTER TABLE wave_version_push_config ADD hot_update_lib_sha varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' NOT NULL COMMENT '热更资源lib.zip的sha512值';
ALTER TABLE wave_version_push_config ADD hot_update_lib_size bigint(20) DEFAULT 0 NOT NULL COMMENT '热更资源lib.zip的字节数';

ALTER TABLE wave_version_push_config DROP INDEX uq_client_version;
ALTER TABLE wave_version_push_config ADD UNIQUE INDEX uq_client_version_deploy_env(client_version,deploy_env);

ALTER TABLE wave_version_push_config_app ADD notice varchar(5012) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' NOT NULL COMMENT '应用公告, 优先取应用公告';
