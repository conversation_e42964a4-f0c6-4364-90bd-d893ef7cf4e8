@startuml
autonumber
autoactivate on
actor user
participant        前端  as html
participant        webh5  as webh5
participant        vip  as vip
=======用户珍宝宫殿页面========
user -> html: 查看珍宝宫殿页面
html -> webh5: 请求服务
webh5 -> webh5: 简单校验用户信息
webh5 -> vip: 校验请求数据，获取珍宝数据
alt 装扮类别下存在可展示的商品
vip -> vip: 获取某装扮TYPE下的可展示的商品
vip -> vip: 获取商品中价值最高的商品
vip -> vip: 获取商品对应的奖励
vip -> vip: 获取商品对应的排名前10，组装用户信息，\n组装第一名倒计时数据
else 获取下一个装扮类别
else 所有类别都不存在可暂时的商品，则返回空数据
end
vip --> webh5: 返回数据
webh5 --> html: 展示结果
html --> user: 查看珍宝数据
=======用户点击殿主藏馆========
user -> html: 查看殿主藏馆页面
html -> webh5: 请求服务
webh5 -> webh5: 简单校验用户信息，时间信息
webh5 -> vip: 校验请求数据，获取珍宝榜单历史数据
alt 装扮类别下存在可展示的商品
vip -> vip: 获取某装扮TYPE下的可展示的商品
vip -> vip: 获取商品中价值最高的商品
vip -> vip: 获取商品对应"月份"的榜首用户信息
else 获取下一个装扮类别
else 所有类别都不存在榜单，则返回空数据
end
vip --> webh5: 返回数据
webh5 --> html: 展示结果
html --> user: 查看榜首信息
@enduml