package fm.lizhi.pp.activity.handler.award;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.pp.activity.confg.PpActivityConfig;
import fm.lizhi.pp.activity.handler.dto.AwardDto;
import fm.lizhi.pp.activity.manager.decorate.DecorateManager;
import fm.lizhi.pp.constant.AwardType;
import fm.lizhi.pp.util.utils.ConfigUtil;
import fm.lizhi.pp.vip.api.AvatarWidgetService;
import fm.lizhi.pp.vip.protocol.AvatarWidgetProto;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021-02-03 14:21
 */
@Slf4j
@AutoBindSingleton
public class AvatarAwardHandler extends AbstractAwardHandler {

    @Inject
    private AvatarWidgetService avatarWidgetService;
    @Inject
    private PpActivityConfig ppActivityConfig;
    @Inject
    private DecorateManager decorateManager;

    @Override
    public String getAWardType() {
        return AwardType.AVATAR.name();
    }

    @Override
    public boolean sendAward0(AwardDto dto) {

        if (ConfigUtil.bizUtilsConf.getAvatarWidgetSwitch()) {
            // 新的发放逻辑
            int sendRcode = decorateManager.sendDecorate(dto.getAwardId(), String.valueOf(dto.getUserId()), 1, dto.getStartTime(), dto.getEndTime());
            return sendRcode == 0;
        }

        AvatarWidgetProto.UserAvatarWidget.Builder builder = AvatarWidgetProto.UserAvatarWidget.newBuilder();
        builder.setUserId(dto.getUserId());
        builder.setWidgetId(dto.getAwardId());
        builder.setBeginTime(System.currentTimeMillis() / 1000L);
        if (dto.getEndTime() > 0) {
            builder.setEndTime(dto.getEndTime());
        }
        Result<AvatarWidgetProto.ResponseAddUserActWidgetInAct> res = avatarWidgetService.addUserActWidgetInAct(builder.build());
        if (res.rCode() != 0) {
            log.warn("avatarWidgetService.addUserActWidgetInAct error. rCode={}, userId={}, avatarWidgetId={}", new Object[]{res.rCode(), dto.getUserId(), dto.getAwardId()});
        } else {
            log.info("avatarWidgetService.addUserActWidgetInAct success, userId={}, avatarWidgetId={}",
                    dto.getUserId(), dto.getAwardId());
        }
        return res.rCode() == 0;
    }
}
