package fm.lizhi.pp.activity.domain.playway.model;

import fm.lizhi.live.room.pp.protocol.LiveNewProto;
import fm.lizhi.pp.activity.domain.playway.enums.PlayWayGameModelEnums;
import fm.lizhi.pp.activity.domain.playway.enums.SpyGameStateEnum;
import fm.lizhi.pp.activity.domain.playway.event.PlayWayInitCommand;
import fm.lizhi.pp.activity.domain.playway.event.PlayerCommand;
import fm.lizhi.pp.activity.domain.playway.event.SpyPlayerCommand;
import fm.lizhi.pp.activity.domain.playway.model.valueobject.PlayWayGameConfig;
import fm.lizhi.pp.activity.domain.playway.model.valueobject.PlayerStatusInfo;
import fm.lizhi.pp.activity.domain.playway.model.valueobject.SpyGameConfig;
import fm.lizhi.pp.activity.domain.playway.model.valueobject.SpyGameTimeConfig;
import fm.lizhi.pp.activity.domain.playway.repository.PlayWayRepository;
import fm.lizhi.pp.activity.domain.playway.repository.SpyRepository;
import fm.lizhi.pp.constant.PlayMethodConstant;
import fm.lizhi.pp.constants.playway.GameInfoStatus;
import fm.lizhi.pp.constants.playway.PlayWayMsgType;
import fm.lizhi.pp.smallgame.constant.SmallGameState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class SpyGame extends PlayWayGame {
    /**
     * 卧底大师资源库
     */
    private SpyRepository spyRepository;

    /**
     * 构造函数
     *
     * @param playWayGameId     玩法游戏ID
     * @param playWayRepository 玩法 资源库
     */
    public SpyGame(Long playWayGameId, PlayWayRepository playWayRepository, SpyRepository spyRepository) {
        super(playWayGameId, playWayRepository, false);
        this.spyRepository = spyRepository;
    }
    /**
     * 初始化后置逻辑
     *
     */
    @Override
    public void postInit(PlayWayInitCommand playWayInitCommand, Optional<LiveNewProto.LiveModeRecord> modelOpt) {
        //初始化游戏记录
        modelOpt.ifPresent(liveModeRecord -> playWayRepository.storeRecordData(playWayInitCommand.getPlayWayId(),
                playWayInitCommand.getNjId(), playWayInitCommand.getLiveId(), playWayGameId, liveModeRecord.getLiveModeId(), 8));
    }
    /**
     * 开始游戏状态设置
     *
     * @return
     */
    @Override
    public int setStartGameStatus() {
        PlayWayGameConfig playWayGameConfig = getPlayWayGameConfig();
        // 随机获取词汇
        if (playWayGameConfig.getModel() == PlayWayGameModelEnums.RANDOM.getCode()) {
            randomGetWords(PlayWayGameModelEnums.RANDOM.getCode(), null, null);
            changeState(SpyGameStateEnum.WORD_SHOW.getCode(), spyRepository.getSpyTimeConfig().getWordShowTime());
            return spyRepository.getSpyTimeConfig().getWordShowTime();
        }
        // 等待主持人输入词汇
        changeState(SpyGameStateEnum.WORD_INPUT.getCode(), spyRepository.getSpyTimeConfig().getWordInputTime());
        playWayGameInfo.setCurrentPlayerId(playWayGameInfo.getHostId());
        return spyRepository.getSpyTimeConfig().getWordInputTime();
    }

    @Override
    protected void setEndGameStatus() {
        getPlayWayGameInfo().setGameState(SpyGameStateEnum.GAME_OVER.getCode());
    }

    /**
     * 游戏开始准备
     *
     * @param playerCommand 玩家操作命令
     */
    @Override
    boolean preCirculation(PlayerCommand playerCommand) {
        SpyPlayerCommand spyPlayerCommand = (SpyPlayerCommand) playerCommand;
        SpyGameStateEnum gameStateEnum = SpyGameStateEnum.from(playWayGameInfo.getGameState());
        switch (gameStateEnum) {
            // 词汇输入中，下发词汇
            case WORD_INPUT:
                if (playerCommand.isTimeout()) {
                    // 超时，随机下发词汇
                    randomGetWords(PlayWayGameModelEnums.RANDOM.getCode(), null, null);
                } else if (spyPlayerCommand.getOperateTypeEnum().equals(GameInfoStatus.GameOperateType.WORD_INPUT)) {
                    // 自主输入
                    randomGetWords(PlayWayGameModelEnums.FILL_IN_INDEPENDENTLY.getCode(), spyPlayerCommand.getSpyWord(),
                            spyPlayerCommand.getCivilianWord());
                }
                return true;

            // 发言中, 用户完成发言
            case PLAYER_ENTRY:
                if (playerCommand.isTimeout()
                        || (spyPlayerCommand.getOperateTypeEnum().equals(GameInfoStatus.GameOperateType.COMPLETE_ENTRY)
                        && spyPlayerCommand.getOpUserId() == playWayGameInfo.getCurrentPlayerId())) {
                    // 玩家发言超时 或者 点击完成发言，从队列中弹出玩家
                    playWayRepository.popCurrentUserId(playWayGameId);
                }
                return true;

            // 投票，计算投票结果
            case VOTING:
                // 操作为投票
                if (spyPlayerCommand.getOperateTypeEnum() != null
                        && spyPlayerCommand.getOperateTypeEnum().equals(GameInfoStatus.GameOperateType.VOTE)) {
                    // 投票是否成功
                    if (!vote(spyPlayerCommand.getOpUserId(), spyPlayerCommand.getVotedUserId())) {
                        return false;
                    }
                }

                // 超时或者所有玩家都投票完成，计算投票结果
                if (playerCommand.isTimeout() || CollectionUtils.isEmpty(playWayRepository.getGameRound(playWayGameId))) {
                    playWayRepository.removeAllGameRound(playWayGameId);
                    countVoteResult();
                }
                return true;
            default:
                break;
        }

        // 超时可以扭转到下一个状态，否则返回
        if (playerCommand.isTimeout()) {
            return true;
        }
        this.setOpCode(-1);
        this.setOpMsg("操作失败");
        return false;
    }

    /**
     * 游戏环节扭转
     *
     * @return 游戏是否进行中
     */
    @Override
    boolean playing() {
        SpyGameTimeConfig spyTimeConfig = spyRepository.getSpyTimeConfig();
        SpyGameStateEnum gameStateEnum = SpyGameStateEnum.from(playWayGameInfo.getGameState());
        switch (gameStateEnum) {
            case WORD_INPUT:
                // 设置下一流转状态：词汇展示
                changeState(SpyGameStateEnum.WORD_SHOW.getCode(), spyTimeConfig.getWordShowTime());
                return true;
            case WORD_SHOW:
                // 设置下一流转状态：玩家发言
                changeState(SpyGameStateEnum.PLAYER_ENTRY.getCode(), playWayGameConfig.getPlayerEntryTime());
                getNextPlayer(false);
                return true;
            case PLAYER_ENTRY:
                // 获取轮转队列，轮转队列为空则
                if (CollectionUtils.isEmpty(playWayRepository.getGameRound(playWayGameId))) {
                    // 修改用户状态
                    playWayPlayer.changePlayerVoteStatus(playWayPlayer.getPlayersId(), GameInfoStatus.VoteStatus.VOTING.getValue());
                    // 设置下一流转状态：投票
                    changeState(SpyGameStateEnum.VOTING.getCode(), spyTimeConfig.getVoteTime());
                    getNextPlayer(false);
                } else {
                    // 流转下一玩家
                    changeState(SpyGameStateEnum.PLAYER_ENTRY.getCode(), playWayGameConfig.getPlayerEntryTime());
                    Long currentUserId = playWayRepository.getCurrentUserId(playWayGameId);
                    playWayGameInfo.setCurrentPlayerId(currentUserId);
                }
                return true;
            case VOTING:
                if (CollectionUtils.isEmpty(playWayRepository.getGameRound(playWayGameId))) {
                    // 设置下一流转状态：投票结果展示
                    changeState(SpyGameStateEnum.VOTE_SHOW.getCode(), spyTimeConfig.getVoteShowTime());
                }
                return true;
            case VOTE_SHOW:
                int gameResult = playWayGameInfo.getGameResult();
                if (gameResult == GameInfoStatus.GameResult.CIVILIAN_WIN.getValue()
                        || gameResult == GameInfoStatus.GameResult.SPY_WIN.getValue()) {
                    // 设置下一流转状态：游戏结果展示
                    changeState(SpyGameStateEnum.CAME_RESULT_SHOW.getCode(), spyTimeConfig.getGameResultShowTime());
                } else {
                    // 设置下一流转状态：玩家发言
                    changeState(SpyGameStateEnum.PLAYER_ENTRY.getCode(), playWayGameConfig.getPlayerEntryTime());
                    getNextPlayer(true);
                }
                return true;
            case CAME_RESULT_SHOW:
                // 设置下一流转状态：惩罚时间
                changeState(SpyGameStateEnum.GAME_OVER_WITH_PENALTY.getCode(), playWayGameConfig.getPunishCount());
                // 设置惩罚用户列表
                playWayGameInfo.setPunishPlayers(playWayPlayer.getPunishPlayers());
                return false;
            default:
                break;
        }
        return false;
    }

    @Override
    public boolean allowToEndState(int state) {
        return SpyGameStateEnum.from(state).allowToEnd();
    }

    @Override
    public void postStartGame() {
        //发送开始游戏kafka
        playWayRepository.sendPlayWayMsg(playWayGameInfo.getNjId(), playWayGameInfo.getPlayType(), PlayWayMsgType.GAME_START.getValue(),
                0, 0, 0, 0, null, null, null, 1, 0);
        playWayRepository.updRecordData(playWayGameInfo.getPlayWayGameId(),  playWayGameInfo.getLiveId(), playWayGameInfo.getPlayWayId(), SmallGameState.PLAYING.getState());
        playWayRepository.saveLiveInteractiveGameType(playWayGameInfo.getLiveId(), PlayMethodConstant.SPY);

    }

    @Override
    public void postPunishmentPhase() {
        // 发送游戏结果kafka
        playWayRepository.sendPlayWayMsg(playWayGameInfo.getNjId(), playWayGameInfo.getPlayType(),
                PlayWayMsgType.GAME_RESULT.getValue(), playWayGameInfo.getGameResult(), 0,
                0, 0, playWayPlayer.getPlayersId(), playWayPlayer.getWinners(),
                playWayGameInfo.getPunishPlayers(), 2, 1);
    }

    @Override
    public void postEndGame() {
        playWayRepository.finishOrderByLive(playWayGameInfo.getLiveId());
        playWayRepository.removeLiveInteractiveGameType(playWayGameInfo.getLiveId(), PlayMethodConstant.SPY);
        playWayRepository.updRecordData(playWayGameInfo.getPlayWayGameId(),  playWayGameInfo.getLiveId(), playWayGameInfo.getPlayWayId(),
                SmallGameState.END.getState());
    }

    /**
     * 扭转状态
     *
     * @param nextGameState 下一状态
     * @param nextRoundTime 下一状态时间
     */
    private void changeState(int nextGameState, int nextRoundTime) {
        playWayGameInfo.setGameState(nextGameState);
        playWayGameConfig.setRoundOpTime(nextRoundTime);
        playWayGameInfo.setRoundEndTime(nextTime(nextRoundTime));
    }


    /**
     * 获取下一处理人
     *
     * @param ifConsiderDraw 是否考虑平局，true：获取平局玩家，false：获取未淘汰玩家
     * @return 当前操作玩家
     */
    protected Long getNextPlayer(boolean ifConsiderDraw) {
        List<Long> survivingPlayers = ifConsiderDraw ?
                playWayPlayer.getSurvivingPlayers() : playWayPlayer.getUnEliminatePlayers();
        if (!survivingPlayers.isEmpty()) {
            playWayRepository.joinGameRound(playWayGameId, survivingPlayers);
        }

        //在游戏中，设置下一处理人进行流转
        Long playerId = playWayRepository.getCurrentUserId(playWayGameId);
        playWayGameInfo.setCurrentPlayerId(playerId);
        return playerId;
    }

    /**
     * 下发词汇
     *
     * @param mode         模式
     * @param spyWord      卧底词汇
     * @param civilianWord 平民词汇
     */
    private void randomGetWords(int mode, String spyWord, String civilianWord) {
        if (mode == PlayWayGameModelEnums.RANDOM.getCode()) {
            // 获取随机下发词汇
            String[] words = spyRepository.randomGetWords(playWayGameInfo.getNjId());
            distributeWord(words[0], words[1]);
        } else {
            distributeWord(spyWord, civilianWord);
        }
    }

    /**
     * 给用户分发词汇
     *
     * @param spyWord
     * @param civilianWord
     */
    private void distributeWord(String spyWord, String civilianWord) {
        // 获取词汇状态
        SpyGameConfig spyGameConfig = (SpyGameConfig) getPlayWayGameConfig();
        if (StringUtils.isEmpty(spyGameConfig.getSpyWord()) || StringUtils.isEmpty(spyGameConfig.getCivilianWord())) {
            List<PlayerStatusInfo> players = playWayGameInfo.getPlayers();
            List<Long> spyIds = players.stream().map(PlayerStatusInfo::getUserId).collect(Collectors.toList());
            // 随机打乱
            Collections.shuffle(spyIds);
            // 获取配置
            int spyNum = spyRepository.getSpyNum(players.size());
            spyIds = spyIds.subList(0, spyNum);
            // 随机设置身份，卧底/平民
            for (PlayerStatusInfo player : players) {
                if (spyIds.contains(player.getUserId())) {
                    player.setIdentity(GameInfoStatus.PlayerIdentity.SPY.getValue());
                } else {
                    player.setIdentity(GameInfoStatus.PlayerIdentity.CIVILIAN.getValue());
                }
            }

            spyGameConfig.setSpyWord(spyWord);
            spyGameConfig.setCivilianWord(civilianWord);
        }
    }

    /**
     * 投票
     *
     * @param opUserId    操作用户id
     * @param votedUserId 被投票用户id
     */
    boolean vote(Long opUserId, Long votedUserId) {
        // 判断选中用户是否合法
        List<Long> survivingPlayers = playWayPlayer.getSurvivingPlayers();
        if (survivingPlayers.isEmpty() || !survivingPlayers.contains(votedUserId)) {
            this.setOpCode(-1);
            this.setOpMsg("选中用户不在投票范围");
            log.error("spyVote error, votedUserId is illegal, opUserId:{}`votedUserId:{}", opUserId, votedUserId);
            return false;
        }

        // 判断是否已经投票
        if (playWayRepository.removeGameRoundByUserId(playWayGameId, opUserId) <= 0) {
            log.warn("spyVote error, opUserId has voted, opUserId:{}`votedUserId:{}", opUserId, votedUserId);
            this.setOpCode(-1);
            this.setOpMsg("您已投票，请勿重复投票");
            return true;
        }
        // 投票
        playWayRepository.vote(playWayGameId, votedUserId);
        // 修改用户投票状态
        playWayPlayer.changePlayerVoteStatus(Arrays.asList(opUserId), GameInfoStatus.VoteStatus.VOTED.getValue());
        // 获取被投者的身份
        int votedIdentity = playWayGameInfo.getPlayers().stream()
                .filter(player -> player.getUserId() == (votedUserId)).findFirst().get().getIdentity();
        // 发送投票kafka
        playWayRepository.sendPlayWayMsg(playWayGameInfo.getNjId(), playWayGameInfo.getPlayType(),
                PlayWayMsgType.VOTE.getValue(), 0, opUserId, 0, votedIdentity, null, null, null, 1, 0);
        return true;
    }

    /**
     * 计算投票结果
     */
    void countVoteResult() {
        // 读取投票结果
        List<Long> voteResult = playWayRepository.getVoteResult(playWayGameId);

        // 无人投票，从存活玩家中随机选择一个玩家
        if (CollectionUtils.isEmpty(voteResult)) {
            List<Long> survivingPlayers = playWayPlayer.getSurvivingPlayers();
            voteResult = Arrays.asList(survivingPlayers.get(new Random().nextInt(survivingPlayers.size())));
        }

        // 把平票玩家状态设置为正常
        if (!CollectionUtils.isEmpty(playWayPlayer.getDrawPlayers())) {
            playWayPlayer.changePlayerStatus(playWayPlayer.getDrawPlayers(), GameInfoStatus.PlayerStatus.NORMAL.getValue());
        }

        List<PlayerStatusInfo> players = playWayGameInfo.getPlayers();
        // 平票
        if (voteResult.size() > 1) {
            // 设置状态为同分选举, 修改用户状态为平票发言
            playWayGameInfo.setGameResult(GameInfoStatus.GameResult.DRAW.getValue());
            playWayPlayer.changePlayerStatus(voteResult, GameInfoStatus.PlayerStatus.DRAW_SPEAK.getValue());
        } else {
            // 无平票，设置最高分状态为淘汰
            playWayPlayer.changePlayerStatus(voteResult, GameInfoStatus.PlayerStatus.ELIMINATE.getValue());
            // 计算未淘汰的卧底数量和平民数量
            int spyNum = 0;
            int civilianNum = 0;
            for (PlayerStatusInfo playerStatusInfo : players) {
                if (playerStatusInfo.getStatus() == GameInfoStatus.PlayerStatus.NORMAL.getValue()) {
                    spyNum += playerStatusInfo.getIdentity() == GameInfoStatus.PlayerIdentity.SPY.getValue() ? 1 : 0;
                    civilianNum += playerStatusInfo.getIdentity() == GameInfoStatus.PlayerIdentity.CIVILIAN.getValue() ? 1 : 0;
                }
            }

            // 计算投票结果
            if (spyNum == 0) {
                // 设置状态为平民胜利
                playWayGameInfo.setGameResult(GameInfoStatus.GameResult.CIVILIAN_WIN.getValue());
            } else if (civilianNum == 0 || (spyNum >= 1 && civilianNum == 1)) {
                // 设置状态为卧底胜利
                playWayGameInfo.setGameResult(GameInfoStatus.GameResult.SPY_WIN.getValue());
            } else {
                // 设置状态为继续游戏
                playWayGameInfo.setGameResult(GameInfoStatus.GameResult.CONTINUE.getValue());
            }

            // 发送淘汰结果kafka消息
            long eliminateUserId = voteResult.get(0);
            int eliminateIdentity = playWayPlayer.getPlayerIdentity(eliminateUserId);
            playWayRepository.sendPlayWayMsg(playWayGameInfo.getNjId(), playWayGameInfo.getPlayType(),
                    PlayWayMsgType.ELIMINATE.getValue(), 0, eliminateUserId, eliminateIdentity, 0, null, null, null, 1, 0);
        }
        // 设置本轮淘汰玩家到惩罚列表
        playWayGameInfo.setPunishPlayers(voteResult);
        // 删除投票结果
        playWayRepository.delVoteKey(playWayGameId);
    }
}
