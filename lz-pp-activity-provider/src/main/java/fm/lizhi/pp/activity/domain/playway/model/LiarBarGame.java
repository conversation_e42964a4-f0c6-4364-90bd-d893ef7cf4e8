package fm.lizhi.pp.activity.domain.playway.model;

import fm.lizhi.live.room.pp.protocol.LiveNewProto;
import fm.lizhi.pp.activity.domain.playway.event.PlayWayInitCommand;
import fm.lizhi.pp.activity.domain.playway.event.PlayerCommand;
import fm.lizhi.pp.activity.domain.playway.model.valueobject.LiarBarGameConfig;
import fm.lizhi.pp.activity.domain.playway.model.valueobject.PlayWayGameConfig;
import fm.lizhi.pp.activity.domain.playway.model.valueobject.PlayWayGameInfo;
import fm.lizhi.pp.activity.domain.playway.repository.PlayWayRepository;
import fm.lizhi.pp.constant.PlayMethodConstant;
import fm.lizhi.pp.smallgame.constant.SmallGameState;

import java.util.Objects;
import java.util.Optional;

/**
 * 骗子酒馆
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
public class LiarBarGame extends PlayWayGame {

    /**
     * 构造函数
     *
     * @param playWayGameId     玩法游戏ID
     * @param playWayRepository 玩法资源库
     */
    public LiarBarGame(Long playWayGameId, PlayWayRepository playWayRepository) {
        super(playWayGameId, playWayRepository, false);
    }
    /**
     * 初始化游戏
     *
     * @param playWayGameInfo
     */
    @Override
    public void initPlayWayGame(PlayWayGameInfo playWayGameInfo, PlayWayGameConfig playWayGameConfig) {
        playWayGameInfo.setGameState(SmallGameState.PREPARING.getState());
        this.playWayGameInfo = playWayGameInfo;
        //初始化小游戏配置
        if (Objects.nonNull(playWayGameConfig)) {
            this.playWayGameInfo.setPlayWayGameConfig(playWayGameConfig);
        }
        //保存数据
        playWayRepository.storeData(this.playWayGameInfo);
        playWayRepository.pushLiveDate(playWayGameInfo.getLiveId());
    }


    @Override
    protected int setStartGameStatus() {
        getPlayWayGameInfo().setGameState(SmallGameState.PLAYING.getState());
        return ((LiarBarGameConfig) getPlayWayGameConfig()).getMaxTime();
    }

    @Override
    protected void setEndGameStatus() {
        getPlayWayGameInfo().setGameState(SmallGameState.END.getState());
    }

    @Override
    boolean preCirculation(PlayerCommand playerCommand) {
        return true;
    }

    @Override
    boolean playing() {
        PlayWayGameInfo playWayGameInfo = getPlayWayGameInfo();
        //每次走到这里都重新赋值游戏的结束时间
        playWayGameInfo.setRoundEndTime(nextTime(((LiarBarGameConfig) getPlayWayGameConfig()).getMaxTime()));
        int gameState = playWayGameInfo.getGameState();
        return gameState == SmallGameState.PLAYING.getState();
    }

    @Override
    public boolean allowToEndState(int state) {
        return false;
    }

    @Override
    public void postStartGame() {
        playWayRepository.updRecordData(playWayGameInfo.getPlayWayGameId(),  playWayGameInfo.getLiveId(), playWayGameInfo.getPlayWayId(), SmallGameState.PLAYING.getState());
        playWayRepository.saveLiveInteractiveGameType(playWayGameInfo.getLiveId(), PlayMethodConstant.LIAR_BAR);

    }

    @Override
    public void postPunishmentPhase() {

    }

    @Override
    public void postEndGame() {
        playWayRepository.updRecordData(playWayGameInfo.getPlayWayGameId(),  playWayGameInfo.getLiveId(), playWayGameInfo.getPlayWayId(),
                SmallGameState.END.getState());
    }
}
