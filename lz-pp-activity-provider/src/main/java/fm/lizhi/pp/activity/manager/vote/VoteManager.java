package fm.lizhi.pp.activity.manager.vote;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.gift.bean.GiftMsg;
import fm.lizhi.live.gift.protocol.BeanProto;
import fm.lizhi.live.room.pp.constants.LiveStatus;
import fm.lizhi.live.room.pp.protocol.LiveNewProto;
import fm.lizhi.pp.activity.bo.vote.PunishEffectBo;
import fm.lizhi.pp.activity.confg.PpActivityConfig;
import fm.lizhi.pp.activity.constant.vote.VoteEndType;
import fm.lizhi.pp.activity.database.dao.VoteDao;
import fm.lizhi.pp.activity.database.entity.VoteInfo;
import fm.lizhi.pp.activity.database.entity.VoteTheme;
import fm.lizhi.pp.activity.manager.GiftManager;
import fm.lizhi.pp.activity.manager.LiveManager;
import fm.lizhi.pp.security.constant.review.StandardReviewTypeEnum;
import fm.lizhi.pp.vote.api.VoteService;
import fm.lizhi.pp.vote.constant.VotePushType;
import fm.lizhi.pp.vote.constant.VoteStatus;
import fm.lizhi.pp.vote.constant.VoteTargetStatus;
import fm.lizhi.pp.vote.constant.VoteType;
import fm.lizhi.pp.vote.dto.VoteTargetDto;
import fm.lizhi.pp.vote.dto.VoteThemeDto;
import fm.lizhi.pp.vote.protocol.VoteProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: chenzj
 * @date: 2024-09-12 11:23
 **/
@AutoBindSingleton
@Slf4j
public class VoteManager {
    @Inject
    private VoteDao voteDao;
    @Inject
    private GiftManager giftManager;
    @Inject
    private VoteEventManager voteEventManager;
    @Inject
    private LiveManager liveManager;
    @Inject
    private PpActivityConfig ppActivityConfig;


    /**
     * 分页获取投票主题列表
     *
     * @param voteType 投票类型
     * @param page     页码
     * @param pageSize 每页大小
     * @return
     */
    public PageList<VoteTheme> pageVoteTheme(VoteType voteType, int page, int pageSize) {
        return voteDao.pageVoteTheme(voteType, page, pageSize);
    }


    /**
     * 装换数据
     *
     * @param voteThemes
     * @return
     */
    public List<VoteThemeDto> buildVoteThemeDtoList(PageList<VoteTheme> voteThemes) {
        List<VoteThemeDto> list = new ArrayList<>();
        for (VoteTheme voteTheme : voteThemes) {
            VoteThemeDto dto = new VoteThemeDto();
            dto.setTheme(voteTheme.getTheme());
            dto.setIntroduce(voteTheme.getIntroduce());
            dto.setVoteType(voteTheme.getVoteType());
            dto.setId(voteTheme.getId());
            list.add(dto);
        }
        return list;
    }

    /**
     * 更新投票主题列表
     *
     * @return
     */
    public String updateVoteTheme(long id, String theme, String introduce, VoteType from) {
        return voteDao.updVoteTheme(id, theme, introduce, from);
    }

    /**
     * 删除投票主题列表
     *
     * @return
     */
    public void delVoteTheme(long id) {
        voteDao.delVoteTheme(id);
    }

    /**
     * 随机获取一个惩罚特效id
     *
     * @return
     */
    public long randomPunishEffectId() {
        List<PunishEffectBo> punishEffectBos =
                JsonUtil.loadsArray(ppActivityConfig.getPunishEffectConfig(), PunishEffectBo.class);
        if (CollectionUtils.isEmpty(punishEffectBos)) {
            return 0L;
        }
        return punishEffectBos.get(RandomUtil.randomInt(punishEffectBos.size())).getId();
    }

    /**
     * 获取主播的惩罚特效
     *
     * @param voteId
     * @param njId
     * @return
     */
    public PunishEffectBo getPunishEffect(long voteId, long njId) {
        long effectId = voteDao.getPunishEffect(voteId, njId);
        List<PunishEffectBo> punishEffectBos =
                JsonUtil.loadsArray(ppActivityConfig.getPunishEffectConfig(), PunishEffectBo.class);
        return punishEffectBos.stream().filter(e -> e.getId() == effectId).findFirst().orElse(null);
    }

    public Result<VoteProto.ResponseResetVote> resetVote(long voteId, long campId, int reviewType) {
        VoteProto.ResponseResetVote.Builder builder = VoteProto.ResponseResetVote.newBuilder();
        VoteInfo voteInfo = voteDao.getVoteInfoById(voteId, true);
        if(Objects.isNull(voteInfo)){
            log.warn("VoteManager resetVote voteInfo is null,voteId:{}",voteId);
            return new Result<>(VoteService.RESET_VOTE_NOT_EXIST, builder.build());
        }
        Integer voteStatus = voteInfo.getVoteStatus();
        if(voteStatus.equals(VoteStatus.END.getStatus())){
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
        }
        //主题，惩罚不需要加锁
        if(reviewType == StandardReviewTypeEnum.VOTE_THEME.getType()){
            if (!voteDao.updateVoteTheme(voteId,"快来参与投票吧!")) {
                log.error("VoteManager resetVote updateVoteTheme error,voteId:{}",voteId);
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
        }
        if(reviewType == StandardReviewTypeEnum.VOTE_CUSTOM_PUNISH.getType()){
            if (!voteDao.updateVotePunish(voteId,"内容因违规已屏蔽")) {
                log.error("VoteManager resetVote updateVotePunish error,voteId:{}",voteId);
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
        }
        boolean locked = voteDao.voteOptVoteLockRetry(voteId);
        // 加锁失败
        if (!locked) {
            log.info("resetVote but luck fail, voteId={}", voteId);
            return new Result<>(VoteService.RESET_VOTE_RETRY, builder.build());
        }
        try {
            //还没有结束，直接重置
            if(reviewType == StandardReviewTypeEnum.VOTE_CAMP_NAME.getType()){
                //阵营ID的话，需要把对应的投票信息的正营ID也重置掉
                String playerListJson = voteInfo.getPlayerListJson();
                List<VoteTargetDto> list = JSONArray.parseArray(playerListJson, VoteTargetDto.class);
                for (VoteTargetDto voteTargetDto : list) {
                    if(voteTargetDto.getId() == campId){
                        voteTargetDto.setName("阵营");
                    }
                }
                if (!voteDao.updatePlayerListJson(voteId, JSON.toJSONString(list))) {
                    log.error("VoteManager resetVote updatePlayerListJson error,voteId:{}",voteId);
                }
            }
        } catch (Exception e) {
            log.error("VoteManager resetVote error,voteId:{}",voteId,e);
            return new Result<>(VoteService.RESET_VOTE_FAIL, builder.build());
        } finally {
            voteDao.voteOptVoteUnlock(voteId);
        }
        voteEventManager.sendVotePushDataChangeMsg(voteInfo.getId(), voteInfo.getLiveId(), voteInfo.getVoteStatus(), VotePushType.DATA_CHANGE, "");
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
    }

    public  Boolean xxlCheckVoteInfo(VoteInfo voteInfo){
        Long liveId = voteInfo.getLiveId();
        if (Objects.isNull(liveId) || liveId <= 0) {
            log.error("xxlCheckVoteInfo liveId is null, voteInfo:{}", JSON.toJSONString(voteInfo));
            return false;
        }
        LiveNewProto.Live liveByLiveId = liveManager.getLiveByLiveId(liveId, true);
        if (Objects.isNull(liveByLiveId)) {
            //可能RPC异常了
            return false;
        }
        if (liveByLiveId.getStatus() != LiveStatus.ON_AIR.getValue()) {
            log.info("xxlCheckVoteInfo liveByLiveId live is not onAir, liveId:{}", liveId);
            //更新数据
            voteDao.finishVote(voteInfo.getId(), VoteEndType.CLOSED_LIVE.getType(), null, null);
            return false;
        }
        return true;
    }


    /**
     * 处理投票的分数
     * @param giftMsg
     */
    public void handleVoteGift(GiftMsg giftMsg) {
        long liveId = giftMsg.getLiveId();
        VoteInfo voteInfo = voteDao.getVoteInfoByLiveId(liveId, true);
        //没有开启过投票，直接返回
        if (Objects.isNull(voteInfo)) {
            return ;
        }
        long createTime = giftMsg.getCreateTime();
        Date endTime = voteInfo.getActualEndTime();
        if (Objects.nonNull(endTime) && endTime.getTime() < createTime) {
            return ;
        }

        Integer voteStatus = voteInfo.getVoteStatus();
        if (voteStatus != VoteStatus.VOTE.getStatus()) {
            //不在投票阶段，直接返回
            return ;
        }


        String playerListJson = voteInfo.getPlayerListJson();
        if (StringUtils.isEmpty(playerListJson)) {
            log.error("handleVoteGift vote playerListJson is empty,liveId:{},voteId:{}", liveId, voteInfo.getId());
            return ;
        }
        List<VoteTargetDto> list = JSONArray.parseArray(playerListJson, VoteTargetDto.class);
        long playerId = 0;  //主播ID
        long campId = 0;   //阵营ID
        for (VoteTargetDto voteTargetDto : list) {
            if (voteInfo.getVoteType().equals(VoteType.PERSON.getType())) {
                //如果是个人直接用当前的ID
                long id = voteTargetDto.getId();
                if (id == giftMsg.getRecTargetUserId() && voteTargetDto.getStatus() == VoteTargetStatus.NORMAL.getStatus()) {
                    playerId = voteTargetDto.getId();
                    break;
                }
                continue;
            }
            List<VoteTargetDto> children = voteTargetDto.getChildren();
            if (CollectionUtils.isEmpty(children)) {
                continue;
            }
            if (voteTargetDto.getStatus() != VoteTargetStatus.NORMAL.getStatus()) {
                continue;
            }
            for (VoteTargetDto child : children) {
                long id = child.getId();
                if (id == giftMsg.getRecTargetUserId() && child.getStatus() == VoteTargetStatus.NORMAL.getStatus()) {
                    //如果是阵营，并且在列表中，则用正营的ID
                    campId = voteTargetDto.getId();
                    playerId = child.getId();
                    break;
                }
            }
        }
        //收礼人没有参与投票，直接返回
        if (playerId < 1) {
            return ;
        }
        int giftValue = giftMsg.getValue();
        int giftCoin = giftMsg.getGiftCoin();
        long giftId = giftMsg.getGiftId();
        long actualGiftId = giftMsg.getActualGiftId();
        if (giftId != actualGiftId) {
            BeanProto.Gift gift = giftManager.getGift(actualGiftId, true);
            if (Objects.isNull(gift)) {
                log.error("handleVoteGift actualGiftId is null,liveId:{},voteId:{},giftId:{}", liveId, voteInfo.getId(), actualGiftId);
                return;
            }
            giftValue = gift.getValue();
            giftCoin = gift.getCoinAmount();
        }
        //增加投票redis分数
        voteDao.addVoteRedisScore(voteInfo.getId(), playerId, campId, giftMsg.getSendUserId(), giftValue, giftCoin);
        voteEventManager.sendVotePushDataChangeMsg(voteInfo.getId(), voteInfo.getLiveId(), voteInfo.getVoteStatus(), VotePushType.DATA_CHANGE, "");
    }
}
