package fm.lizhi.pp.activity.database.entity.playway;

import java.util.Date;
import javax.persistence.*;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 * <p>
 * 直播间玩法游戏记录表
 *
 * @date 2023-05-22 06:56:42
 */
@Table(name = "`live_game_info`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LiveGameInfo {
    /**
     * 主键
     */
    @Id
    @GeneratedValue
    @Column(name = "`id`")
    private Long id;

    /**
     * 房主ID
     */
    @Column(name = "`nj_id`")
    private Long njId;

    /**
     * 发起订单用户id
     */
    @Column(name = "`user_id`")
    private Long userId;

    /**
     * 游戏主持人ID，默认0
     */
    @Column(name = "`host_id`")
    private Long hostId;

    /**
     * 下单的厅id
     */
    @Column(name = "`live_id`")
    private Long liveId;

    /**
     * 玩法id,关联amusement_interact_play_way表主键
     */
    @Column(name = "`play_way_id`")
    private Long playWayId;

    /**
     * 玩法类型，-1:无类型，1:唱歌，2:卧底大师；3数字炸弹
     */
    @Column(name = "`play_type`")
    private Integer playType;

    /**
     * 游戏状态，-1:未开始，1:游戏中，2:结束游戏
     */
    @Column(name = "`state`")
    private Integer state;

    /**
     * 开始时间
     */
    @Column(name = "`start_time`")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "`end_time`")
    private Date endTime;

    /**
     * 创建时间
     */
    @Column(name = "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "`modify_time`")
    private Date modifyTime;

    /**
     * 配置信息，一般为json
     */
    @Column(name = "`config`")
    private String config;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", njId=").append(njId);
        sb.append(", userId=").append(userId);
        sb.append(", hostId=").append(hostId);
        sb.append(", liveId=").append(liveId);
        sb.append(", playWayId=").append(playWayId);
        sb.append(", playType=").append(playType);
        sb.append(", state=").append(state);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", config=").append(config);
        sb.append("]");
        return sb.toString();
    }
}