package fm.lizhi.pp.activity.manager.smallgame;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Supplier;
import com.google.common.collect.Lists;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.datacenter.comment.pp.constant.CommentType;
import fm.lizhi.datastore.RedisTuple;
import fm.lizhi.live.pp.core.protocol.LivePpUserProto;
import fm.lizhi.live.room.pp.protocol.LiveNewProto;
import fm.lizhi.ocean.seal.kafka.GamePlayerSettleResult;
import fm.lizhi.ocean.seal.kafka.GameSettleResult;
import fm.lizhi.pp.activity.bo.dto.smallgame.SmallGameAwardDto;
import fm.lizhi.pp.activity.bo.dto.smallgame.TransferDto;
import fm.lizhi.pp.activity.confg.PpActivityConfig;
import fm.lizhi.pp.activity.database.dao.SmallGameDao;
import fm.lizhi.pp.activity.database.dao.amusementinteract.AmusementInteractPlayWayDao;
import fm.lizhi.pp.activity.database.entity.AmusementInteractPlayWay;
import fm.lizhi.pp.activity.database.entity.SmallGamePlayerRecords;
import fm.lizhi.pp.activity.database.entity.SmallGameResult;
import fm.lizhi.pp.activity.database.entity.SmallGameRoundRecords;
import fm.lizhi.pp.activity.database.redis.smallgame.SmallGameRedisKey;
import fm.lizhi.pp.activity.domain.playway.event.PlayWayInitCommand;
import fm.lizhi.pp.activity.domain.playway.repository.PlayWayRepository;
import fm.lizhi.pp.activity.domain.playway.service.PlayWayService;
import fm.lizhi.pp.activity.kafka.KafkaMsgCommonProducer;
import fm.lizhi.pp.activity.manager.*;
import fm.lizhi.pp.activity.manager.amusementinteract.AmusementInteractLivePlayWayUserManager;
import fm.lizhi.pp.activity.manager.decorate.DecorateManager;
import fm.lizhi.pp.activity.manager.smallgame.design.AbsGameOperation;
import fm.lizhi.pp.activity.manager.smallgame.design.BizMqGameCloseOperationService;
import fm.lizhi.pp.activity.manager.smallgame.design.OperationFactory;
import fm.lizhi.pp.constant.AmusementInteractPlayTypeEnum;
import fm.lizhi.pp.constant.AmusementInteractTypeEnum;
import fm.lizhi.pp.mutex.protocol.MutexProto;
import fm.lizhi.pp.security.bean.ban.UserBizBanVO;
import fm.lizhi.pp.security.constant.ban.UserBanBiz;
import fm.lizhi.pp.smallgame.api.SmallGameService;
import fm.lizhi.pp.smallgame.constant.SmallGameJoinState;
import fm.lizhi.pp.smallgame.constant.SmallGameRecommendState;
import fm.lizhi.pp.smallgame.constant.SmallGameState;
import fm.lizhi.pp.smallgame.dto.*;
import fm.lizhi.pp.user.account.user.protocol.PpUserBaseProto;
import fm.lizhi.pp.util.utils.ConfigUtil;
import fm.lizhi.pp.util.utils.UrlUtils;
import fm.lizhi.pp.vip.bean.dto.DecorateDto;
import fm.lizhi.pp.vip.constant.DecorateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.skywalking.apm.toolkit.trace.ExecutorWrapper;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: chenzj
 * @date: 2025-04-17 11:53
 **/
@Slf4j
@AutoBindSingleton
public class SmallGameManager {
    @Inject
    private LiveRoomRoleManager liveRoomRoleManager;
    @Inject
    private LiveManager liveManager;
    @Inject
    private MutexManager mutexManager;
    @Inject
    private PlayWayService playWayService;
    @Inject
    private BizMqGameCloseOperationService bizMqGameCloseOperationService;
    @Inject
    private PlayWayRepository playWayRepository;
    @Inject
    private PpNewUserManager ppNewUserManager;
    @Inject
    private WhiteCategoryManager whiteCategoryManager;
    @Inject
    private AmusementInteractLivePlayWayUserManager amusementInteractLivePlayWayUserManager;
    @Inject
    private AmusementInteractPlayWayDao amusementInteractPlayWayDao;
    @Inject
    private PpActivityConfig ppActivityConfig;
    @Inject
    private CommentManager commentManager;
    @Inject
    private OperationFactory operationFactory;
    @Inject
    private SmallGameDao smallGameDao;
    @Inject
    private DecorateManager decorateManager;
    @Inject
    private SmallGamePushManager smallGamePushManager;
    @Inject
    private KafkaMsgCommonProducer kafkaMsgCommonProducer;
    @Inject
    private LiveNewServiceManager liveNewServiceManager;
    @Inject
    private UserGroupManager userGroupManager;
    @Inject
    private PpActivityConfig activityConfig;
    @Inject
    private BanManager banManager;
    @Inject
    private SealManager sealManager;

    private static final ExecutorWrapper THREAD_POOL = ExecutorWrapper.of(
            new ThreadPoolExecutor(5, 10, 0, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(500),
                    new ThreadFactory() {
                        private final AtomicInteger ids = new AtomicInteger(0);

                        @Override
                        public Thread newThread(Runnable r) {
                            Thread thread = new Thread(r);
                            thread.setDaemon(true);
                            thread.setName("SmallGameManagerExecutor-" + ids.getAndIncrement());
                            return thread;
                        }
                    },
                    new RejectedExecutionHandler() {
                        @Override
                        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                            log.error("Task " + r.toString() + " rejected from " + executor.toString());
                        }
                    }));


    /**
     * 生成推荐列表
     */
    public void createRecommendList() {
        try {
            //生成前先删除旧数据
            smallGameDao.delGameRecommendList();
            //获取小游戏配置
            List<AmusementInteractPlayWay> smallGames = amusementInteractPlayWayDao.getAllPlayWays().get();
            //获取准备中,x分钟不开始会被关闭
            Date dayStart = DateUtils.addMinutes(new Date(), -(ppActivityConfig.getPreparingSmallGameEndMinute() * 2));
            List<SmallGameRoundRecords> preparingGames = smallGameDao.getGamesByState(SmallGameState.PREPARING.getState(), dayStart, new Date());
            //排除非直播中的
            preparingGames = preparingGames.stream().filter(g -> !ppActivityConfig.getSmallGameBack().contains(String.valueOf(g.getNjId())) && liveNewServiceManager.isOnAir(g.getGameRoomId())).collect(Collectors.toList());
            log.info("查询到{}个准备中", preparingGames.size());
            //获取游戏中，x分钟没结束会被关闭
            dayStart = DateUtils.addMinutes(new Date(), -(ppActivityConfig.getPlayingSmallGameEndMinute() + 10));
            List<SmallGameRoundRecords> playingGames = smallGameDao.getGamesByState(SmallGameState.PLAYING.getState(), dayStart, new Date());
            //排除非直播中的
            playingGames = playingGames.stream().filter(g -> !ppActivityConfig.getSmallGameBack().contains(String.valueOf(g.getNjId())) && liveNewServiceManager.isOnAir(g.getGameRoomId())).collect(Collectors.toList());
            log.info("查询到{}个游戏中中", preparingGames.size());

            //准备中pgc房间
            List<SmallGameRoundRecords> pgcGames = preparingGames.stream().filter(g -> {
                Optional<List<LivePpUserProto.WhitelistCategory>> optional = whiteCategoryManager.getUserCateCache().getUnchecked(g.getNjId());
                boolean isPgc = false;
                if (optional.isPresent()) {
                    Optional<LivePpUserProto.WhitelistCategory> first = optional.get().stream().filter(c -> ConfigUtil.getBizUtilsConf().getPgcCategoryIds().contains(String.valueOf(c.getId()))).findFirst();
                    isPgc = first.isPresent();
                }
                return isPgc;
            }).collect(Collectors.toList());
            //准备中ugc房间
            preparingGames.removeAll(pgcGames);

            // 保存到Redis SortedSet中
            smallGameDao.setGameRecommendList(smallGames, pgcGames, true);
            smallGameDao.setGameRecommendList(smallGames, preparingGames, false);

            //游戏中pgc房间
            List<SmallGameRoundRecords> pgcPlayingGames = playingGames.stream().filter(g -> {
                Optional<List<LivePpUserProto.WhitelistCategory>> optional = whiteCategoryManager.getUserCateCache().getUnchecked(g.getNjId());
                boolean isPgc = false;
                if (optional.isPresent()) {
                    Optional<LivePpUserProto.WhitelistCategory> first = optional.get().stream().filter(c -> ConfigUtil.getBizUtilsConf().getPgcCategoryIds().contains(String.valueOf(c.getId()))).findFirst();
                    isPgc = first.isPresent();
                }
                return isPgc;
            }).collect(Collectors.toList());
            //游戏中ugc房间
            playingGames.removeAll(pgcPlayingGames);
            // 保存到Redis SortedSet中
            smallGameDao.setGameRecommendList(smallGames, pgcPlayingGames, true);
            smallGameDao.setGameRecommendList(smallGames, playingGames, true);

            //设置结束的个播游戏到推荐列表
            Set<String> njIds = smallGameDao.getInteractEndGameRecommendList(0, 100);
            if (njIds.isEmpty()) {
                return;
            }
            List<SmallGameRoundRecords> endGames = new ArrayList<>(njIds.size());
            for (String njId : njIds) {
                SmallGameRoundRecords rounds = smallGameDao.getGameRoundsByRoundIds(Long.parseLong(njId));
                if (Objects.nonNull(rounds)) {
                    endGames.add(smallGameDao.getGameRoundsByRoundIds(Long.parseLong(njId)));
                }
            }
            log.info("查询到{}个已结束的小游戏", endGames.size());
            this.setEndGameRecommendList(smallGames, endGames);
            log.info("小游戏推荐列表生成成功");
        } catch (Exception e) {
            log.error("生成小游戏推荐列表失败", e);
        }
    }

    private void setEndGameRecommendList(List<AmusementInteractPlayWay> smallGames, List<SmallGameRoundRecords> gamesRounds) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(smallGames) || org.apache.commons.collections.CollectionUtils.isEmpty(gamesRounds)) {
            log.info("setGameRecommendList is empty list");
            return;
        }
        //只保留结束状态的
        gamesRounds = gamesRounds.stream().filter(gr -> {
            if (ppActivityConfig.getSmallGameBack().contains(String.valueOf(gr.getNjId()))) {
                return false;
            }
            SmallGameRoundRecords newest = smallGameDao.getGamesRoundRecordsNewest(gr.getNjId());
            if (newest == null) {
                return false;
            }
            return newest.getState() == SmallGameState.END.getState();
        }).collect(Collectors.toList());
        Map<Long, Integer> gamePlaytypeMap = smallGames.stream().collect(Collectors.toMap(g -> g.getId(), g -> g.getPlayType()));
        for (SmallGameRoundRecords gamesRound : gamesRounds) {
            // 使用线程池异步执行每个游戏记录的处理
            THREAD_POOL.execute(() -> {
                try {
                    String gameType = String.valueOf(gamePlaytypeMap.getOrDefault(gamesRound.getPlayWayId(), AmusementInteractPlayTypeEnum.DRAW_GUESS.getType()));

                    //获取最新liveId
                    Long liveId = liveNewServiceManager.getLiveForUserId(gamesRound.getNjId());
                    if (Objects.isNull(liveId)) {
                        return;
                    }
                    //游戏类型
                    smallGameDao.setEndGameRecommendList(gameType, gamesRound.getEndTime(), liveId);
                } catch (Exception e) {
                    log.error("异步处理结束游戏推荐列表失败, gameRound={}", gamesRound, e);
                }
            });
        }
        log.info("已提交{}个结束游戏的异步处理任务", gamesRounds.size());
    }

    /**
     * 校验权限，只有房主和主持才能操作
     *
     * @param liveId
     * @param userId
     * @return
     */
    public boolean checkPermission(long liveId, long userId) {
        LiveNewProto.Live live = liveManager.getLiveByLiveId(liveId, true);
        if (live == null) {
            log.error("checkPermission but live is null, liveId={}, userId={}", liveId, userId);
            return false;
        }
        // 房主
        if (live.getUserId() == userId) {
            return true;
        }
        // 主持
        return liveRoomRoleManager.isHost(live.getUserId(), userId);
    }

    public ResAuthCheckResultDto doSmallGameAuthCheck(ReqAuthCheckDto param) {
        ResAuthCheckResultDto result = new ResAuthCheckResultDto();
        long liveId = param.getLiveId();
        long userId = param.getUserId();
        boolean pcSource = param.isPcSource();
        boolean isPermission = checkPermission(liveId, userId);
        long gameId = param.getGameId();

        AmusementInteractPlayWay playWay = amusementInteractPlayWayDao.getByIdCache(gameId);
        if (Objects.isNull(playWay)) {
            result.setToast("无该游戏");
            result.setOwnRole(false);
            return result;
        }
        Integer playType = playWay.getPlayType();
        String smallGameBlackTypeList = ppActivityConfig.getSmallGameBlackTypeList();
        if (!StringUtils.isEmpty(smallGameBlackTypeList)) {
            Optional<Integer> first = Arrays.stream(smallGameBlackTypeList.split(",")).map(Integer::valueOf).filter(s -> s.equals(playType)).findFirst();
            if (first.isPresent()) {
                result.setToast("游戏正在升级中");
                result.setOwnRole(false);
                return result;
            }
        }
        if (playType.equals(AmusementInteractPlayTypeEnum.LIAR_BAR.getType())) {
            //骗子酒馆的只有在UGC，还有个播的才显示
            LiveNewProto.Live live = liveManager.getLiveByLiveId(liveId, true);
            if (Objects.isNull(live)) {
                result.setToast("直播间信息为空");
                result.setOwnRole(false);
                return result;
            }
            Boolean larBarIsOpen = amusementInteractLivePlayWayUserManager.larBarIsOpen(live.getUserId());
            if (!larBarIsOpen) {
                result.setToast("该游戏无法开启");
                result.setOwnRole(false);
                return result;
            }
        }

        //如果没有权限获取
        if (!isPermission) {
            result.setOwnRole(false);
            result.setToast(SmallGameService.AUTH_CHECK_ERROR);
            if (pcSource) {
                return result;
            }
            //没有权限，不是pc源的请求的，需要发送公屏
            Optional<PpUserBaseProto.User> optional = ppNewUserManager.getUser(userId);
            if (optional.isPresent()) {
                //判断是否超时发送公屏限制，一分钟内最多2次
                Long count = smallGameDao.inrNormalUserCommentCount(liveId, userId);
                if (count <= ppActivityConfig.getSmallGameNormalUserSendCommentCount()) {
                    commentManager.sendCommentExtend(liveId, userId, optional.get().getName() + "想玩" + playWay.getName(),
                            null, CommentType.GENERAL_COMMENT);
                    //可以发送公屏就不需要toast了
                    result.setToast("");
                } else {
                    result.setToast(SmallGameService.AUTH_CHECK_BUSY_ERROR);
                }
                return result;
            }
        }
        result.setOwnRole(true);
        //判断玩法是否互斥
        ImmutablePair<Boolean, String> isMutex = mutexManager.isMutex(liveId, MutexProto.Feature.NONE.getNumber());
        if (isMutex.getLeft()) {
            result.setToast(isMutex.getRight());
            result.setOwnRole(false);
            return result;
        }
        return result;
    }


    /**
     * 游戏加载完毕，初始化数据
     *
     * @param param
     */
    public ResPrepareResultDto doPrepareSmallGame(ReqPrepareDto param) {
        /*
        1:游戏加载完毕，初始化 live_game_info
        2:游戏加载完毕，初始化 small_game_round_records
         */
        ResPrepareResultDto result = new ResPrepareResultDto();
        long gameId = param.getGameId();

        AmusementInteractPlayWay byIdCache = amusementInteractPlayWayDao.getByIdCache(gameId);
        if (Objects.isNull(byIdCache)) {
            result.setToast("无该游戏");
            return result;
        }

        if (isGameLimitUser(param.getOptUserId())) {
            result.setToast("您被禁止参与游戏");
            return result;
        }

        if (isGameLimitUser(param.getNjId())) {
            result.setToast("本房间禁止开启游戏");
            return result;
        }

        ImmutablePair<Boolean, String> banDrawGuess = isBanDrawGuess(param.getOptUserId(), param.getGameId());
        if (banDrawGuess.getLeft()) {
            result.setToast(banDrawGuess.getRight());
            return result;
        }

        //x分钟可以开启y次
        JSONObject jsonObject = JSONObject.parseObject(activityConfig.getSmallGameLimitTime());
        int minute = jsonObject.getIntValue("minute");
        int time = jsonObject.getIntValue("time");

        String key = SmallGameRedisKey.SMALL_GAME_START_COUNT.getKey(param.getNjId());
        Long count = smallGameDao.incr(key);
        if (count > time) {
            log.warn("doPrepareSmallGame limit njId={},time={}", param.getNjId(), count);
            result.setToast("游戏开启频繁，请稍后重试");
            return result;
        }
        smallGameDao.setRedisExpireTime(key, minute * 60);

        //游戏时长校验
        String res = checkGameDuration(param.getNjId());
        if (StringUtils.isNotBlank(res)) {
            result.setToast(res);
            return result;
        }

        long liveId = param.getLiveId();
        boolean isPermission = checkPermission(liveId, param.getOptUserId());
        if (!isPermission) {
            result.setToast(SmallGameService.AUTH_CHECK_ERROR);
            return result;
        }
        //判断玩法是否互斥
        ImmutablePair<Boolean, String> isMutex = mutexManager.isMutex(liveId, MutexProto.Feature.NONE.getNumber());
        if (isMutex.getLeft()) {
            result.setToast(isMutex.getRight());
            return result;
        }
        boolean isLock = smallGameDao.lock(param.getNjId());
        if (!isLock) {
            result.setToast("游戏准备中");
            return result;
        }
        long userId = param.getOptUserId();
        long njId = param.getNjId();
        try {

            Integer playType = byIdCache.getPlayType();
            AmusementInteractPlayTypeEnum playTypeEnum = AmusementInteractPlayTypeEnum.from(playType);
            // 初始化数据
            PlayWayInitCommand initCmd = new PlayWayInitCommand();
            initCmd.setUserId(userId);
            initCmd.setNjId(njId);
            initCmd.setLiveId(liveId);
            initCmd.setPlayWayId(gameId);
            initCmd.setPlayTypeEnum(playTypeEnum);
            initCmd.setSmallGameJson(byIdCache.getGameConfig());
            initCmd.setMaxJoinCount(byIdCache.getMaxJoinCount());
            playWayService.initGame(initCmd);

            // 使用线程池异步执行
            THREAD_POOL.execute(() -> {
                try {
                    //发公屏
                    PpUserBaseProto.User user = ppNewUserManager.getUserFromCache(userId).orElse(null);
                    String username = user == null ? "" : user.getName();
                    String content = username + " 发起" + byIdCache.getName() + "，快来参与！";
                    //只有你画我猜才需要加后面的文案
                    if (byIdCache.getPlayType().equals(AmusementInteractPlayTypeEnum.DRAW_GUESS.getType())) {
                        content = content + "请确保您绘制的内容符合平台管理规定，否则将有封号处理风险";
                    }
                    commentManager.sendSysComment(liveId, userId, content);
                    //移除结束游戏列表
                    smallGameDao.removeInteractEndGameRecommendList(String.valueOf(njId));
                    //推送PC
                    smallGamePushManager.pushPcPrepareGame(liveId);
                } catch (Exception e) {
                    log.error("doPrepareSmallGame send msg and mq error", e);
                }
            });


        } catch (Exception e) {
            log.error("doPrepareSmallGame initGame error param:{}", JSONObject.toJSONString(param), e);
            //初始化异常了，直接结束游戏
//            playWayService.endGame(param.getGameId(), false);
            TransferDto dto = new TransferDto();
            dto.setEventName(bizMqGameCloseOperationService.getEventType());
            dto.setGameId(gameId);
            dto.setNjId(njId);
            dto.setOptUserId(njId);
            dto.setLiveId(liveId);
            //设置结束游戏json
            JSONObject endDate = new JSONObject();
            endDate.put("room_id", String.valueOf(dto.getLiveId()));
            dto.setDataJson(endDate.toJSONString());
            bizMqGameCloseOperationService.forwardData(dto);
            result.setToast("服务异常");

        } finally {
            smallGameDao.unlock(param.getNjId());
        }
        return result;

    }


    public ResGameOperationResultDto doOperation(ReqGameOperationDto param) {
        ResGameOperationResultDto result = new ResGameOperationResultDto();
        AbsGameOperation handler = operationFactory.getHandler(param.getEventName());
        if (Objects.isNull(handler)) {
            log.error("doOperation get handler is null param:{}", JSONObject.toJSONString(param));
            result.setToast("服务异常");
            return result;
        }
        LiveNewProto.Live liveInfo = liveManager.getLiveByLiveId(param.getLiveId(), true);
        if (Objects.isNull(liveInfo)) {
            log.error("doOperation get live is null param:{}", JSONObject.toJSONString(param));
            result.setToast("直播间信息为空");
            return result;
        }
        TransferDto transferDto = new TransferDto();
        transferDto.setGameId(param.getGameId());
        transferDto.setDataJson(param.getDataJson());
        transferDto.setEventName(param.getEventName());
        transferDto.setLiveId(param.getLiveId());
        transferDto.setNjId(liveInfo.getUserId());
        transferDto.setOptUserId(param.getUserId());
        String forwardData = handler.forwardData(transferDto);
        if (StringUtils.isNotBlank(forwardData)) {
            result.setToast(forwardData);
            return result;
        }
        return result;
    }

    /**
     * 保存游戏推荐列表到Redis SortedSet
     * 第一位数字 {@link SmallGameRecommendState}
     * 第二位是参与游戏人数0表示人数够了
     * 第三位是游戏类型
     * 最后是时间戳
     */
    public List<SmallGameRecommendDto> getGameRecommendList(int keyIndex, int pageNum, int pageSize) {
        List<SmallGameRecommendDto> result = Lists.newArrayList();

        Supplier<List<AmusementInteractPlayWay>> smallGameList = amusementInteractPlayWayDao.getAllPlayWays();
        Map<Integer, AmusementInteractPlayWay> playtypeGameMap = smallGameList.get().stream()
                .filter(g -> g.getPlayType() > AmusementInteractPlayTypeEnum.SING.getType())
                .collect(Collectors.toMap(g -> g.getPlayType(), Function.identity()));

        Set<RedisTuple> gameRecommends = smallGameDao.getGameRecommendListDesc(keyIndex, pageNum, pageSize);
        for (RedisTuple gameRecommend : gameRecommends) {
            SmallGameRecommendDto dto = new SmallGameRecommendDto();

            String score = String.valueOf(gameRecommend.getScore().longValue());
            String gameSate = score.substring(0, 1);
            String playerNum = score.substring(1, 2);
            String playType = score.substring(score.length() - 1);

            AmusementInteractPlayWay playWay = playtypeGameMap.get(Integer.valueOf(playType));
            if (Objects.isNull(playWay)) {
                log.error("getGameRecommendList error playWay is null playType={}", playType);
                continue;
            }
            if (SmallGameRecommendState.PGC_PREPARE.getState().equals(gameSate)
                    || SmallGameRecommendState.UGC_PREPARE.getState().equals(gameSate)) {
                dto.setStatusDes(String.format("缺少%d人发车", SmallGameDao.PLAYER_NUM_BASE - Integer.valueOf(playerNum)));
                if (String.valueOf(SmallGameDao.PLAYER_NUM_BASE).equals(playerNum)) {
                    dto.setStatusDes("随时发车");
                }
                dto.setButtonDesc("去加入");
                dto.setStatus(SmallGameState.PREPARING.getState());
            } else if (SmallGameRecommendState.PGC_PLAYING.getState().equals(gameSate)
                    || SmallGameRecommendState.UGC_PLAYING.getState().equals(gameSate)) {
                dto.setStatusDes("火热进行中");
                dto.setButtonDesc("去围观");
                dto.setStatus(SmallGameState.PLAYING.getState());
            } else {
                dto.setStatusDes("最近玩过");
                dto.setButtonDesc("去围观");
                dto.setStatus(SmallGameState.END.getState());
            }
            dto.setGameId(playWay.getId());
            dto.setGameName(playWay.getName());
            dto.setLiveId(Long.parseLong(gameRecommend.getElement()));
            dto.setGameImg(UrlUtils.appendRomaCdnPrefix(playWay.getIcon()));
            result.add(dto);
        }

        return result;
    }


    /**
     * 记录游戏结算数据
     *
     * @param gameSettleResult
     */
    public void settleResult(GameSettleResult gameSettleResult, long njId) {
        GamePlayerSettleResult[] playerSettleResults = gameSettleResult.getGamePlayerSettleResults();
        //判空
        if (CollectionUtils.isEmpty(Arrays.asList(playerSettleResults))) {
            return;
        }
        List<SmallGameResult> gameResults = Lists.newArrayListWithCapacity(playerSettleResults.length);

        AmusementInteractPlayWay playWay = amusementInteractPlayWayDao.getById(gameSettleResult.getGameId(), 0);
        if (Objects.equals(AmusementInteractPlayTypeEnum.LIAR_BAR.getType(), playWay.getPlayType())) {
            //如果是骗子酒馆，有x人参加，第一名x分，第二名x-1分
            //playerSettleResults 按照排名升序排序
            Arrays.sort(playerSettleResults, Comparator.comparingInt(GamePlayerSettleResult::getRank));
            //得出逃跑的人数
            int escapedCount = (int) Arrays.stream(playerSettleResults).filter(GamePlayerSettleResult::isEscaped).count();
            //最高分数  =  参与人数 - 逃跑人数
            AtomicInteger atomicInteger = new AtomicInteger(playerSettleResults.length - escapedCount);

            for (GamePlayerSettleResult playerSettleResult : playerSettleResults) {
                SmallGameResult build = SmallGameResult.builder().njId(njId)
                        .gameId(gameSettleResult.getGameId())
                        .userId(playerSettleResult.getUserId())
                        .gameRoomId(Long.parseLong(gameSettleResult.getRoomId()))
                        .gameRoundId(gameSettleResult.getGameRoundId())
                        .rank(playerSettleResult.getRank())
                        .score(playerSettleResult.isEscaped() ? 0 : Math.max(playerSettleResult.getScore(), atomicInteger.getAndDecrement()))
                        .winner(playerSettleResult.getIsWin())
                        .escaped(playerSettleResult.isEscaped() ? 1 : 0)
                        .startTime(new Date(gameSettleResult.getGameStartAtTime()))
                        .endTime(new Date(gameSettleResult.getGameEndAtTime()))
                        .createTime(new Date())
                        .modifyTime(new Date())
                        .build();
                gameResults.add(build);
            }
        } else {
            //非骗子酒馆
            for (GamePlayerSettleResult playerSettleResult : playerSettleResults) {
                SmallGameResult build = SmallGameResult.builder().njId(njId)
                        .gameId(gameSettleResult.getGameId())
                        .userId(playerSettleResult.getUserId())
                        .gameRoomId(Long.parseLong(gameSettleResult.getRoomId()))
                        .gameRoundId(gameSettleResult.getGameRoundId())
                        .rank(playerSettleResult.getRank())
                        .score(playerSettleResult.isEscaped() ? 0 : playerSettleResult.getAward())
                        .winner(playerSettleResult.getIsWin())
                        .escaped(playerSettleResult.isEscaped() ? 1 : 0)
                        .startTime(new Date(gameSettleResult.getGameStartAtTime()))
                        .endTime(new Date(gameSettleResult.getGameEndAtTime()))
                        .createTime(new Date())
                        .modifyTime(new Date())
                        .build();
                gameResults.add(build);
            }
        }

        if (!gameResults.isEmpty()) {
            //记录结果
            smallGameDao.insertSmallGameResult(gameResults);
            //记录榜单
            smallGameDao.createSmallGameRank(gameResults);
        }
    }

    /**
     * 获取当前小游戏排行榜
     *
     * @param gameId
     * @param userId
     * @return
     */
    public ResGameRankDto getSmallGameRank(long gameId, long userId, int start, int end) {
        ResGameRankDto resGameRankDto = new ResGameRankDto();

        Set<RedisTuple> tuples = smallGameDao.getSmallGameRank(gameId, start, end, new Date());
        if (tuples.isEmpty()) {
            return resGameRankDto;
        }

        List<Long> userIdList = tuples.stream().map(t -> Long.valueOf(t.getElement())).distinct().collect(Collectors.toList());
        Optional<List<PpUserBaseProto.User>> optional = ppNewUserManager.getUsers(userIdList);
        if (!optional.isPresent()) {
            return resGameRankDto;
        }
        List<SmallGameAwardDto> smallGameAwardDtos = JSONObject.parseArray(ppActivityConfig.getSmallGameAward(), SmallGameAwardDto.class);
        Optional<SmallGameAwardDto> first = smallGameAwardDtos.stream().filter(s -> s.getGameId() == gameId && DecorateTypeEnum.USER_GLORY.getType() == s.getDecorateType()).findFirst();
        DecorateDto decorateDto = new DecorateDto();
        if (first.isPresent()) {
            //获取官方认证
            decorateDto = decorateManager.getDecorateInfoById(first.get().getDecorateId());
        }

        Map<Long, PpUserBaseProto.User> userMap = optional.get().stream().collect(Collectors.toMap(PpUserBaseProto.User::getId, Function.identity()));

        List<GameRankDto> ranks = tuples.stream().map(t -> {
            PpUserBaseProto.User user = userMap.get(Long.parseLong(t.getElement()));
            GameRankDto dto = new GameRankDto();
            dto.setScore(t.getScore().intValue());
            dto.setBand(Integer.parseInt(user.getBand()));
            dto.setUserId(user.getId());
            dto.setName(user.getName());
            dto.setPortrait(UrlUtils.appendRomaUserCdnPrefix(user.getPortrait()));
            return dto;
        }).sorted((o1, o2) -> {
            // 首先按照分数降序排序
            int scoreCompare = Integer.compare(o2.getScore(), o1.getScore());
            if (scoreCompare != 0) {
                return scoreCompare;
            }
            // 如果分数相同，则按照band升序排序（band越小排名越前）
            return Integer.compare(o1.getBand(), o2.getBand());
        }).collect(Collectors.toList());
        // 重新设置排名
        for (int i = 0; i < ranks.size(); i++) {
            if (i == 0) {
                ranks.get(i).setOfficialIcon(UrlUtils.appendRomaCdnPrefix(decorateDto.getIconUrl()));
            }
            ranks.get(i).setRank(i + 1);
        }
        // 设置返回结果
        resGameRankDto.setRanks(ranks);

        // 获取当前用户排名信息
        GameRankDto myRank = smallGameDao.getMySmallGameRank(gameId, userId);

        resGameRankDto.setMyRank(buildMyRank(userId, myRank));

        return resGameRankDto;
    }

    private GameRankDto buildMyRank(long userId, GameRankDto myRank) {
        Optional<PpUserBaseProto.User> optionalUser = ppNewUserManager.getUserFromCache(userId);
        if (!optionalUser.isPresent()) {
            return null;
        }
        PpUserBaseProto.User user = optionalUser.get();
        int rank = Objects.isNull(myRank) ? 0 : myRank.getRank();
        int score = Objects.isNull(myRank) ? 0 : myRank.getScore();
        return GameRankDto.builder()
                .rank(rank)
                .score(score)
                .userId(user.getId())
                .band(Integer.valueOf(user.getBand()))
                .name(user.getName())
                .portrait(UrlUtils.appendRomaUserCdnPrefix(user.getPortrait()))
                .build();
    }

    /**
     * 获取每个游戏的第一名
     *
     * @return
     */
    public ResGameRankDto getTop1SmallGameRank(Date date) {
        ResGameRankDto resGameRankDto = new ResGameRankDto();
        List<GameRankDto> topRanks = Lists.newArrayListWithCapacity(4);
        Supplier<List<AmusementInteractPlayWay>> playWaysSup = amusementInteractPlayWayDao.getAllPlayWays();
        List<AmusementInteractPlayWay> playWays = playWaysSup.get().stream().filter(s -> Objects.equals(AmusementInteractTypeEnum.SMALL_GAME.getType(), s.getType())).collect(Collectors.toList());
        for (AmusementInteractPlayWay playWay : playWays) {
            Set<RedisTuple> tuples = smallGameDao.getSmallGameRank(playWay.getId(), 0, 0, date);
            if (tuples.isEmpty()) {
                continue;
            }

            ArrayList<RedisTuple> redisTuples = new ArrayList<>(tuples);
            RedisTuple redisTuple = redisTuples.get(0);
            //获取相同分数用户
            Set<String> sameScore = smallGameDao.getSameScore(playWay.getId(), redisTuple.getScore(), date);
            List<Long> userIdList = sameScore.stream().map(Long::valueOf).distinct().collect(Collectors.toList());
            Optional<List<PpUserBaseProto.User>> optional = ppNewUserManager.getUsers(userIdList);

            Map<Long, PpUserBaseProto.User> userMap = optional.get().stream().collect(Collectors.toMap(PpUserBaseProto.User::getId, Function.identity()));
            //排序返回
            List<GameRankDto> ranks = sameScore.stream().map(t -> {
                PpUserBaseProto.User user = userMap.get(Long.parseLong(t));
                GameRankDto dto = new GameRankDto();
                dto.setScore(redisTuple.getScore().intValue());
                dto.setBand(Integer.parseInt(user.getBand()));
                dto.setUserId(user.getId());
                dto.setName(user.getName());
                dto.setPortrait(UrlUtils.appendRomaCdnPrefix(user.getPortrait()));
                return dto;
            }).sorted(Comparator.comparingInt(GameRankDto::getBand)).collect(Collectors.toList());
            // 设置第一名信息
            GameRankDto gameRankDto = ranks.get(0);
            gameRankDto.setGameId(playWay.getId());
            gameRankDto.setRank(1);
            gameRankDto.setTopDesc(playWay.getName() + "-" + gameRankDto.getName() + "暂时领先");
            topRanks.add(gameRankDto);
        }
        resGameRankDto.setTopRanks(topRanks);
        return resGameRankDto;
    }


    public MatchSmallGameDto getMatchSmallGame(ReqMatchSmallGameDto param) {
        //获取小游戏配置
        AmusementInteractPlayWay gameInfo = amusementInteractPlayWayDao.getByIdCache(param.getGameId());
        if (Objects.isNull(gameInfo)) {
            return null;
        }

        SmallGameRoundRecords matchGame = getMatchSmallGame(gameInfo);
        if (Objects.isNull(matchGame)) {
            return null;
        }
        return MatchSmallGameDto.builder().gameId(gameInfo.getId())
                .gameName(gameInfo.getName())
                .njId(matchGame.getNjId())
                .liveId(matchGame.getGameRoomId())
                .build();
    }

    /**
     * 匹配小游戏
     *
     * @param gameInfo
     * @return
     */
    public SmallGameRoundRecords getMatchSmallGame(AmusementInteractPlayWay gameInfo) {
        //获取准备中
        List<SmallGameRoundRecords> preparingGames = smallGameDao.getGamesRoundRecords(SmallGameRoundRecords.builder()
                .playWayId(gameInfo.getId()).state(SmallGameState.PREPARING.getState()).build());
        log.info("getMatchSmallGame preparingGames gameId={} size={}", gameInfo.getId(), preparingGames.size());

        //pgc准备中
        Optional<SmallGameRoundRecords> recordsOptional = matchGames(preparingGames, gameInfo.getMaxJoinCount(), true, true);
        if (recordsOptional.isPresent()) {
            return recordsOptional.get();
        }
        //ugc准备中
        recordsOptional = matchGames(preparingGames, gameInfo.getMinJoinCount(), false, true);
        if (recordsOptional.isPresent()) {
            return recordsOptional.get();
        }

        //骗子酒馆不返回游戏中
        if (Objects.equals(AmusementInteractPlayTypeEnum.LIAR_BAR.getType(), gameInfo.getPlayType())) {
            return null;
        }

        //游戏中
        List<SmallGameRoundRecords> playingGames = smallGameDao.getGamesRoundRecords(SmallGameRoundRecords.builder()
                .playWayId(gameInfo.getId()).state(SmallGameState.PLAYING.getState()).build());
        log.info("getMatchSmallGame playingGames gameId={} size={}", gameInfo.getId(), playingGames.size());

        recordsOptional = matchGames(playingGames, gameInfo.getMinJoinCount(), true, false);
        if (recordsOptional.isPresent()) {
            return recordsOptional.get();
        }
        recordsOptional = matchGames(playingGames, gameInfo.getMinJoinCount(), false, false);
        return recordsOptional.orElse(null);
    }

    /**
     * 匹配游戏
     *
     * @param gamesRoundRecords
     * @param maxJoinCount
     * @param isPgc
     * @return
     */
    public Optional<SmallGameRoundRecords> matchGames(List<SmallGameRoundRecords> gamesRoundRecords, int maxJoinCount, boolean isPgc, boolean isPreparing) {
        List<SmallGameRoundRecords> records = gamesRoundRecords.stream()
                .filter(g -> !ppActivityConfig.getSmallGameBack().contains(String.valueOf(g.getNjId())) && liveNewServiceManager.isOnAir(g.getGameRoomId()))
                .filter(g -> {
                    Optional<List<LivePpUserProto.WhitelistCategory>> optional = whiteCategoryManager.getUserCateCache().getUnchecked(g.getNjId());
                    if (optional.isPresent()) {
                        //如果是ugc，没有白名单
                        if (optional.get().isEmpty() && !isPgc) {
                            return true;
                        }
                        Optional<LivePpUserProto.WhitelistCategory> first = optional.get().stream().filter(c -> isPgc == ConfigUtil.getBizUtilsConf().getPgcCategoryIds().contains(String.valueOf(c.getId()))).findFirst();
                        return first.isPresent();
                    }
                    return !isPgc;
                }).collect(Collectors.toList());

        long count = records.size();
        if (count == 0) {
            return Optional.empty();
        }

        if (count > 1 && isPreparing) {
            //数量超过1个才需要比较
            Collections.shuffle(records);
            return records.stream().min(Comparator.comparingInt(game -> {
                // 计算最小参与人数与当前人数的差值
                int diff = maxJoinCount - game.getPlayerNum();
                return Math.max(diff, 0);
            }));
        }

        return records.stream().skip(ThreadLocalRandom.current().nextInt((int) count)).findFirst();
    }

    /**
     * 禁止参加和开启游戏
     *
     * @return
     */
    public boolean isGameLimitUser(long userId) {
        return userGroupManager.doIsUserInGroup(ppActivityConfig.getSmallGameLimitUserGroupId(), userId);
    }

    /**
     * 禁止参加和开启你画我猜
     * @param userId
     * @param gameId
     * @return true封禁 false未封禁
     */
    public ImmutablePair<Boolean, String> isBanDrawGuess(long userId, long gameId) {
        List<AmusementInteractPlayWay> playWays = amusementInteractPlayWayDao.getAllPlayWays().get();
        Optional<AmusementInteractPlayWay> first = playWays.stream().filter(way -> Objects.equals(way.getPlayType(), AmusementInteractPlayTypeEnum.DRAW_GUESS.getType())).findFirst();
        if (!first.isPresent()) {
            return ImmutablePair.of(true, "游戏已下线");
        }

        if (first.get().getId() != gameId) {
            return ImmutablePair.of(false, "");
        }

        UserBizBanVO userBizBan = banManager.getUserBizBan(UserBanBiz.SMALL_GAME_DRAW_GUESS_BAN.getBiz(), userId);
        if (Objects.isNull(userBizBan)) {
            return ImmutablePair.of(false, "");
        }
        if (Objects.isNull(userBizBan.getEndTime())) {
            return ImmutablePair.of(true, "您被禁止参与你画我猜游戏");
        }
        String msg = "您被禁止参与你画我猜游戏至%s";
        return ImmutablePair.of(true, String.format(msg, DateUtil.formatDateToString(userBizBan.getEndTime(), "yyyy年MM月dd日 HH:mm")));
    }

    /**
     * 校验游戏时长
     */
    public String checkGameDuration(long njId) {
        String res = null;
        JSONObject jsonObject = JSONObject.parseObject(ppActivityConfig.getSmallGameLimitDuration());
        int minute = jsonObject.getIntValue("minute");
        int round = jsonObject.getIntValue("round");

        Long second = smallGameDao.getPlayingGameTime(njId, DateUtil.getDayStart(new Date()), DateUtil.getDayEnd(new Date()));
        if (Objects.nonNull(second) && second / 60 >= minute) {
            res = "今日游戏时长已达上限";
        }

        Integer count = smallGameDao.getPlayingGameCount(njId, DateUtil.getDayStart(new Date()), DateUtil.getDayEnd(new Date()));
        if (Objects.nonNull(second) && count >= round) {
            res = "今日游戏次数已达上限";
        }

        if (StringUtils.isNotBlank(res)) {
            log.warn("checkGameDuration is over time njId={},res={}", njId, res);
        }

        return res;
    }

    /**
     * 游戏结束时剔除用户封禁用户
     * @param gameId 游戏id
     * @param settleResults 游戏结算结果
     */
    public void kickOutSmallGame(long gameId, GamePlayerSettleResult[] settleResults) {
        List<AmusementInteractPlayWay> playWays = amusementInteractPlayWayDao.getAllPlayWays().get();
        Optional<AmusementInteractPlayWay> first = playWays.stream().filter(way -> Objects.equals(way.getPlayType(), AmusementInteractPlayTypeEnum.DRAW_GUESS.getType())).findFirst();
        if (!first.isPresent()) {
            return;
        }

        if (first.get().getId() != gameId) {
            return;
        }

        for (GamePlayerSettleResult settleResult : settleResults) {
            //获取封禁状态，正常封禁才有数据
            UserBizBanVO userBizBan = banManager.getUserBizBan(UserBanBiz.SMALL_GAME_DRAW_GUESS_BAN.getBiz(), settleResult.getUserId());
            if (Objects.isNull(userBizBan)) {
                continue;
            }
            kickOutSmallGame(settleResult.getUserId(), BizMqGameCloseOperationService.USER_OUT);
        }
    }

    /**
     * 根据游戏状态执行不同事件剔除用户
     *
     * @param userId
     */
    public void kickOutSmallGameByGameState(long userId, long gameId) {
        //一局游戏最多半小时数
        Date start = DateUtils.addMinutes(new Date(), -30);
        Date end = DateUtils.addMinutes(new Date(), 1);
        SmallGamePlayerRecords latestRecord = smallGameDao.getLatestRecord(userId, start, end);
        if (Objects.isNull(latestRecord)) {
            return;
        }

        if (latestRecord.getGameId() != gameId) {
            return;
        }

        if (latestRecord.getState() == SmallGameJoinState.OUT.getState()) {
            return;
        }
        //获取最新局数据
        SmallGameRoundRecords roundNewest = smallGameDao.getGamesRoundRecordsNewest(latestRecord.getNjId());
        if (Objects.isNull(roundNewest)) {
            log.error("kickOutSmallGameByGameState error,latestRecord={}", JSONObject.toJSONString(latestRecord));
            return;
        }
        //游戏中
        if (roundNewest.getState() == SmallGameState.PLAYING.getState()) {
            this.kickOutSmallGame(userId, BizMqGameCloseOperationService.GAME_END);
            return;
        }
        this.kickOutSmallGame(userId, BizMqGameCloseOperationService.USER_OUT);
    }

    /**
     * 踢用户下游戏房
     * @param userId
     * @param evenName sud事件 user_out或game_end
     */
    public void kickOutSmallGame(long userId, String evenName) {
        //小游戏最多玩3小时，获取4小时数内最新已经足够
        Date start = DateUtils.addHours(new Date(), -4);
        Date end = DateUtils.addMinutes(new Date(), 1);
        SmallGamePlayerRecords latestRecord = smallGameDao.getLatestRecord(userId, start, end);
        if (Objects.isNull(latestRecord)) {
            return;
        }

        if (latestRecord.getState() == SmallGameJoinState.OUT.getState()) {
            return;
        }

        //加入游戏了，需要踢出游戏
        TransferDto outDto = new TransferDto();
        outDto.setEventName(evenName);
        outDto.setGameId(latestRecord.getGameId());
        outDto.setNjId(latestRecord.getNjId());
        outDto.setLiveId(latestRecord.getLiveId());
        outDto.setOptUserId(latestRecord.getPlayerId());

        //用户逃跑的时候，需要给他离开座位，自定义一个逻辑调用
        JSONObject endDate = new JSONObject();
        endDate.put("room_id", String.valueOf(latestRecord.getLiveId()));
        endDate.put("uid", String.valueOf(latestRecord.getPlayerId()));
        if (BizMqGameCloseOperationService.USER_OUT.equals(evenName)) {
            endDate.put("is_cancel_ready", true);
        }
        outDto.setDataJson(endDate.toJSONString());
        String res = sealManager.invokeTarget(outDto);
        if (StringUtils.isNotBlank(res)) {
            log.warn("kickOutSmallGame error res={},evenName={},playerRecordId={},gameId={}", res, evenName, latestRecord.getId(), latestRecord.getGameId());
        }
    }
}
