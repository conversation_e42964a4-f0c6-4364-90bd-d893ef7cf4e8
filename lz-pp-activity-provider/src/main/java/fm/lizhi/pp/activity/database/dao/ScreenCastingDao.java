package fm.lizhi.pp.activity.database.dao;

import fm.lizhi.pp.activity.database.entity.ScreenCastingInfo;
import fm.lizhi.pp.activity.database.mapper.ScreenCastingInfoMapper;
import io.shardingsphere.core.routing.router.masterslave.MasterSlaveRouteOnceVisitedHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/4 10:26
 */
@Component
@Slf4j
public class ScreenCastingDao {
    @Autowired
    private ScreenCastingInfoMapper screenCastingInfoMapper;

    /**
     * 根据id查询记录
     *
     * @param id
     * @return
     */
    public ScreenCastingInfo getById(long id, boolean fromMaster) {
        if (fromMaster) {
            MasterSlaveRouteOnceVisitedHolder.routeMaster();
        }
        return screenCastingInfoMapper.selectByPrimaryKey(ScreenCastingInfo.builder().id(id).build());
    }

    /**
     * 保存投屏信息
     *
     * @param info
     * @return
     */
    public boolean saveScreenCastingInfo(ScreenCastingInfo info) {
        int res = screenCastingInfoMapper.insert(info);
        log.info("saveScreenCastingInfo, info={}, res={}", info, res);
        return res == 1;
    }

    /**
     * 更新当前播放图片id
     *
     * @param id
     * @param curImageId
     * @return
     */
    public boolean updateCurImageId(long id, long curImageId) {
        int res = screenCastingInfoMapper.updateByPrimaryKey(
                ScreenCastingInfo.builder().id(id).curImageId(curImageId).modifyTime(new Date()).build());
        log.info("updateCurImageId, id={}, curImageId={}, res={}", id, curImageId, res);
        return res == 1;
    }

    /**
     * 更新投屏状态
     *
     * @param id
     * @param status
     * @param oldStatus
     * @return
     */
    public boolean updateStatus(long id, int status, int oldStatus) {
        int res = screenCastingInfoMapper.updateStatus(id, status, oldStatus);
        log.info("updateStatus, id={}, status={}, oldStatus={}, res={}", id, status, oldStatus, res);
        return res == 1;
    }

    /**
     * 获取直播最后一条投票记录
     *
     * @param liveId
     * @return
     */
    public ScreenCastingInfo getLast(long liveId) {
        return screenCastingInfoMapper.getLast(liveId);
    }
}
