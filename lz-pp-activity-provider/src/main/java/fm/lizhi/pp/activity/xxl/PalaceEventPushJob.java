package fm.lizhi.pp.activity.xxl;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import fm.lizhi.pp.activity.confg.PpActivityConfig;
import fm.lizhi.pp.activity.manager.palace.PalaceEventManager;
import fm.lizhi.pp.activity.database.redis.palace.PalaceRedisManager;
import lombok.extern.slf4j.Slf4j;

/**
 * 宫斗游戏事件推送定时任务
 *
 * <AUTHOR>
 * @date 2023/9/13 18:18
 */
@Slf4j
@JobHandler(value = "palaceEventPushJob")
@AutoBindSingleton(baseClass = IJobHandler.class, multiple = true)
public class PalaceEventPushJob extends IJobHandler {
    @Inject
    private PalaceRedisManager palaceRedisManager;
    @Inject
    private PalaceEventManager palaceEventManager;
    @Inject
    private PpActivityConfig ppActivityConfig;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        // 获取上次推送时间
        long latestEventPushTime = palaceRedisManager.getLatestEventPushTime();
        long now = System.currentTimeMillis();
        // 最多推送5s前的事件
        latestEventPushTime = Math.max(latestEventPushTime, now - 5000L);

        if (latestEventPushTime >= now) {
            return ReturnT.SUCCESS;
        }

        palaceEventManager.pushEvent(latestEventPushTime + 1, now - 1);

        // 清除早期数据
        palaceRedisManager.removeEvent(0L, System.currentTimeMillis() - ppActivityConfig.getPalaceRemoveEventTime());

        return ReturnT.SUCCESS;
    }
}
