package fm.lizhi.pp.activity.database.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 * <p>
 * 奖励流水
 *
 * @date 2021-08-10 05:52:17
 */
@Table(name = "`award_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AwardRecord {
    @Id
    @GeneratedValue
    @Column(name = "`id`")
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "`user_id`")
    private Long userId;

    /**
     * 奖励类型
     */
    @Column(name = "`type`")
    private String type;

    /**
     * 奖励id
     */
    @Column(name = "`award_id`")
    private Long awardId;

    /**
     * 内容
     */
    @Column(name = "`content`")
    private String content;

    /**
     * 创建时间
     */
    @Column(name = "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", type=").append(type);
        sb.append(", awardId=").append(awardId);
        sb.append(", content=").append(content);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}