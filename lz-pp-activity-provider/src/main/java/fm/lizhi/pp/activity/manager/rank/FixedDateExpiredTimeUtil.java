package fm.lizhi.pp.activity.manager.rank;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.pp.activity.confg.LzRankConf;
import fm.lizhi.pp.activity.constant.rank.FixRankConstants;

import java.util.Date;

/**
 * Created by liuxiaoxiong on 2018/6/22.
 */
@AutoBindSingleton
public class FixedDateExpiredTimeUtil {

    @Inject
    private LzRankConf lzRankConf;

    public int getExpireTime(Date endTime, int playType) {
        if (playType == FixRankConstants.PlayType.DAILY) {
            endTime = new Date(System.currentTimeMillis());
        }
        Date time = DateUtil.getDayAfter(endTime, lzRankConf.getGetTimeExpiredForFixeRank());
        return (int) ((time.getTime() - System.currentTimeMillis()) / 1000);

    }
}
