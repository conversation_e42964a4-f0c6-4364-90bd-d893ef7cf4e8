package fm.lizhi.pp.activity.kafka.infrastructure.kafka.consumer;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.pp.activity.confg.PpActivityConfig;
import fm.lizhi.pp.activity.constant.AuditResultNotifyEvent;
import fm.lizhi.pp.activity.domain.playway.service.PlayWayService;
import fm.lizhi.pp.activity.database.redis.PpActivityRedis;
import fm.lizhi.pp.activity.database.redis.RedisKey;
import fm.lizhi.pp.activity.manager.ChatManager;
import fm.lizhi.pp.activity.manager.amusementinteract.AmusementInteractPlayWaySongSheetManager;
import fm.lizhi.pp.social.protocol.chat.PpChatBaseProto;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@AutoBindSingleton
public class AuditResultNotifyReceiver extends AbstractProcess<AuditResultNotifyEvent> {
    @Inject
    private AmusementInteractPlayWaySongSheetManager amusementInteractPlayWaySongSheetManager;
    @Inject
    private ChatManager chatManager;
    @Inject
    private PpActivityConfig config;
    @Inject
    private PpActivityRedis ppActivityRedis;
    @Inject
    private PlayWayService playWayService;


    @Override
    protected Class<AuditResultNotifyEvent> getClazz() {
        return AuditResultNotifyEvent.class;
    }

    @Override
    protected boolean msgReceived0(AuditResultNotifyEvent event) {
        if (!Objects.equals(event.getType(), config.getContentPlatformSongType())
                && !Objects.equals(event.getType(), config.getExpressWallType())
                && !Objects.equals(event.getType(), config.getSpyContentReviewType())) {
            return true;
        }

        log.info("AuditResultNotifyReceiver event={}", JsonUtil.dumps(event));
        if (Objects.equals(event.getReviewStatus(), 0)) {
            return true;
        }

        if (Objects.equals(event.getType(), config.getContentPlatformSongType())) {
            amusementInteractPlayWaySongSheetManager.manualApprFail(Long.parseLong(event.getId()));

            // 发私信
            String msg = "很抱歉，您的歌曲名文案审核未通过，原因是不符合PP《平台管理规则》，如有疑问请进入【我的】-【客服中心】，联系客服帮您解决。";
            chatManager.sendChatOfficial(event.getFromUserId(), msg, PpChatBaseProto.ChatType.TEXT);
            return true;
        } else if (Objects.equals(event.getType(), config.getExpressWallType())) {
            String[] split = event.getId().split("_");
            if (split.length == 2) {
                String taskId = split[0];
                String uuid = split[1];
                // 删除对应表白墙文案
                ppActivityRedis.zrem(RedisKey.EXPRESS_WALL_CONTENT.getKey(taskId), uuid + "_" + event.getFromUserId() + "_" + event.getData());
                // 发私信
                String msg = "很抱歉，您修改的告白墙文案审核未通过，原因是不符合PP《平台管理规则》，如有疑问请进入【我的】-【客服中心】，联系客服帮您解决。";
                chatManager.sendChatOfficial(event.getFromUserId(), msg, PpChatBaseProto.ChatType.TEXT);
            }
            return true;
        } else if (Objects.equals(event.getType(), config.getSpyContentReviewType())) {
            // 卧底大师人审结果
            String[] split = event.getId().split("_");
            if (split.length == 2) {
                playWayService.endGame(Long.parseLong(split[0]), false);
                // 发私信
                String msg = "很抱歉，您在谁是卧底游戏编辑的词汇审核未通过，原因是不符合PP《平台管理规则》，如有疑问请进入【我的】-【客服中心】，联系客服帮您解决。";
                chatManager.sendChatOfficial(event.getFromUserId(), msg, PpChatBaseProto.ChatType.TEXT);
            }
            return true;
        }
        return true;
    }
}
