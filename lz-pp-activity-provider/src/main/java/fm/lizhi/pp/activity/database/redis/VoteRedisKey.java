package fm.lizhi.pp.activity.database.redis;

import fm.lizhi.pp.util.utils.EnvUtils;

public enum VoteRedisKey implements CacheKeyGenerator.CacheKeyType {
    /**
     * 投票操作对直播维度加锁，针对场景：开启投票
     * type = string
     * key = #{prefix}_#{liveId}
     * value = 加锁时间戳
     */
    VOTE_OPT_LIVE_LOCK,
    /**
     * 投票操作对单次投票维度加锁，针对场景：开启投票后单场投票内的操作（包括结束投票）
     * type = string
     * key = #{prefix}_#{voteId}
     * value = 加锁时间戳
     */
    VOTE_OPT_VOTE_LOCK,
    /**
     * 主播惩罚特效
     * type = string
     * key = #{prefix}_#{voteId}_#{njId}
     * value = 惩罚特效id
     */
    PUNISH_EFFECT,

    /**
     * 投票阶段，主播的魅力值
     * type = string
     * key = VOTE_ANCHOR_SCORE_#{voteId}_#{njId}
     * value = 分数
     */
    VOTE_ANCHOR_SCORE,
    /**
     * 投票阶段，获胜者主播列表
     * type = list
     * key = VOTE_WIN_ANCHOR_LIST_#{voteId}
     * value: {用户ID}
     */
    VOTE_WIN_ANCHOR_LIST,
    /**
     * 投票阶段，失败者主播列表
     * type = list
     * key = VOTE_LOSE_ANCHOR_LIST_#{voteId}
     * value: {用户ID}
     */
    VOTE_LOSE_ANCHOR_LIST,

    /**
     * 投票阶段，获胜者阵营列表
     * type = list
     * key = VOTE_WIN_CAMP_LIST_#{voteId}
     * value: {阵营ID}
     */
    VOTE_WIN_CAMP_LIST,
    /**
     * 投票阶段，失败者阵营列表
     * type = list
     * key = VOTE_LOSE_CAMP_LIST_#{voteId}
     * value: {阵营ID}
     */
    VOTE_LOSE_CAMP_LIST,

    /**
     * 投票阶段，用户为主播贡献的魅力值
     * type = sort set
     * key = VOTE_USER_SCORE_#{voteId}_#{njId}
     * element: {用户ID}
     * score : 魅力值
     */
    VOTE_ANCHOR_USER_SCORE,
    /**
     * 投票阶段，用户为阵营贡献的魅力值
     * type = sort set
     * key = VOTE_USER_SCORE_#{voteId}_#{阵营ID}
     * element: {用户ID}
     * score : 魅力值
     */
    VOTE_CAMP_USER_SCORE,
    /**
     * 投票阶段，用户贡献的金币数
     * type = sort set
     * key = VOTE_USER_COIN_#{voteId}
     * element: {用户ID}
     * score : 金币值
     */
    VOTE_USER_COIN,

    /**
     * 投票阶段，用户为主播贡献的魅力值
     * type = STRING
     * key = VOTE_MOD_TIME_#{voteId}
     * value: 时间戳
     */
    VOTE_MOD_TIME,
    ;

    @Override
    public String getKey(Object... args) {
        StringBuilder sb = new StringBuilder(this.getPrefix());

        sb.append("_");
        sb.append(this.name());

        if (args != null && args.length > 0) {
            for (Object o : args) {
                sb.append("_" + o);
            }
        }

        return sb.toString();
    }

    @Override
    public String getPrefix() {
        if (EnvUtils.isPre()) {
            return "PRE_PP";
        }
        return "PP";
    }
}
