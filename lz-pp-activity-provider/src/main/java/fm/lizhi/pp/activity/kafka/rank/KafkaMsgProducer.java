package fm.lizhi.pp.activity.kafka.rank;

import com.alibaba.fastjson.JSON;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.queue.service.Producer;
import fm.lizhi.pp.activity.bean.msg.*;
import fm.lizhi.pp.activity.confg.PpActivityConfig;
import fm.lizhi.pp.activity.constant.GameTaskTypeEnum;
import fm.lizhi.pp.activity.constant.KafkaTopic;
import fm.lizhi.pp.bean.CpInLiveMsg;
import fm.lizhi.pp.constants.ActivityKafkaTopic;
import fm.lizhi.pp.monopoly.bean.MonopolyEventMsg;
import fm.lizhi.pp.util.msg.CallStatusMsg;
import fm.lizhi.pp.util.utils.EnvUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * KafkaMsgProducer
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022/4/29 11:35
 */
@AutoBindSingleton
public class KafkaMsgProducer {
    private static Logger logger = LoggerFactory.getLogger(KafkaMsgProducer.class);
    @Inject
    private PpActivityConfig activityConfig;
    private Producer producer;

    @PostConstruct
    public void init() {
        producer = new Producer(EnvUtils.isProduct(), activityConfig.getKafkaConfigKey(), activityConfig.getKafkaSendTimeout(),
                activityConfig.getKafkaSendRetries());
        logger.info("init kafka producer success, isProduct:{}, kafkaConfigKey:{}, timeoutMs:{}, retries:{}", EnvUtils.isProduct(),
                activityConfig.getKafkaConfigKey(), activityConfig.getKafkaSendTimeout(), activityConfig.getKafkaSendRetries());
    }

    /**
     * 发送信息
     *
     * @param topic 队列名  如无特殊需求，建议实体增删改查用一个队列，消息中op（add,delete,update,query）字段做标识
     * @param key   内部主要是分片用，根据key做hash分片，不需求传null，可传记录ID，方便消费端做滤重
     * @param msg
     * @return
     */
    private boolean send(String topic, String key, String msg) {
        boolean success = producer.send(topic, key, msg);
        logger.info("send kafka msg, success:{}, topic:{}, key:{}, msg:{}", success, topic, key, msg);
        return success;
    }


    /**
     * 宫斗贡献值统计，实时，宫斗值增加才需要调用
     *
     * @param contributeMsg 贡献值统计消息
     */
    public void sendUserGiveGifMsg(UserContributeMsg contributeMsg) {
        PalaceTaskGatherMsg msg = PalaceTaskGatherMsg
                .builder()
                .njId(contributeMsg.getNjId())
                .gameId(contributeMsg.getGameId())
                .taskType(GameTaskTypeEnum.contribute.getType())
                .userId(contributeMsg.getUserId())
                .userFightingCapacity(contributeMsg.getUserFightingCapacity())
                .build();

        send(KafkaTopic.PP_TOPIC_ROOM_GAME_EVENT, String.valueOf(msg.getUserId()), JSON.toJSONString(msg));
    }

    /**
     * 厅维度游戏结束发送统计信息
     *
     * @param roomGameMsg 厅维度游戏结束发送统计信息
     */
    public void sendRoomFinishGameMsg(RoomGameMsg roomGameMsg) {
        PalaceTaskGatherMsg msg = PalaceTaskGatherMsg
                .builder()
                .njId(roomGameMsg.getNjId())
                .gameId(roomGameMsg.getGameId())
                .gameCharm(roomGameMsg.getGameCharm())
                .taskType(GameTaskTypeEnum.room.getType())
                .endType(roomGameMsg.getEndType())
                .build();
        sendRoomGameMsg(msg);
    }

    /**
     * 主播维度游戏结束发送统计信息
     *
     * @param anchorGameMsgList 主播维度游戏结束发送统计信息列表
     */
    public void sendAnchorGameMsg(List<AnchorGameMsg> anchorGameMsgList) {
        for (AnchorGameMsg anchorGameMsg : anchorGameMsgList) {
            PalaceTaskGatherMsg msg = PalaceTaskGatherMsg
                    .builder()
                    .anchorId(anchorGameMsg.getAnchorId())
                    .njId(anchorGameMsg.getNjId())
                    .gameId(anchorGameMsg.getGameId())
                    .taskType(GameTaskTypeEnum.anchor.getType())
                    .grade(anchorGameMsg.getGrade())
                    .fightingCapacity(anchorGameMsg.getFightingCapacity())
                    .win(anchorGameMsg.isWin())
                    .queen(anchorGameMsg.isQueen())
                    .endType(anchorGameMsg.getEndType())
                    .build();
            sendRoomGameMsg(msg);
        }

    }

    /**
     * 游戏结束成为皇上的用户
     *
     * @param userGameMsg 游戏结束成为皇上的用户
     */
    public void sendUserGameMsg(UserGameMsg userGameMsg) {
        PalaceTaskGatherMsg msg = PalaceTaskGatherMsg
                .builder()
                .njId(userGameMsg.getNjId())
                .gameId(userGameMsg.getGameId())
                .taskType(GameTaskTypeEnum.user.getType())
                .userId(userGameMsg.getUserId())
                .becomeEmperor(userGameMsg.isBecomeEmperor())
                .endType(userGameMsg.getEndType())
                .build();
        sendRoomGameMsg(msg);

    }

    /**
     * 发送任务条件
     *
     * @param msg 任务条件
     */
    private void sendRoomGameMsg(PalaceTaskGatherMsg msg) {
        send(KafkaTopic.PP_TOPIC_ROOM_GAME_EVENT, String.valueOf(msg.getNjId()), JSON.toJSONString(msg));
    }

    /**
     * pk通话事件通知
     *
     * @param callStatusMsg
     */
    public void callEvenMsg(CallStatusMsg callStatusMsg) {
        send("pp_topic_call_status_event", String.valueOf(callStatusMsg.getUserId()), JSON.toJSONString(callStatusMsg));
    }

    /**
     * 发送大富翁事件消息
     *
     * @param msg
     */
    public void sendMonopolyEvent(MonopolyEventMsg msg) {
        send(KafkaTopic.PP_TOPIC_MONOPOLY_EVENT, null, JSON.toJSONString(msg));
    }

    /**
     * PK通知事件-通知创作者
     */
    public void sendPkEventMsg(LivePkEventMsg msg) {
        try {
            send(KafkaTopic.PP_TOPIC_PK_EVENT, String.valueOf(msg.getLiveId()), JSON.toJSONString(msg));
        } catch (Exception e) {
            logger.error("sendPkEventMsg error. msg:{}", msg, e);
        }
    }

    /**
     * 发送cp赛在同一个直播间直播间消息
     *
     * @param msg
     */
    public void sendCpInLiveOneMinuteMsg(CpInLiveMsg msg) {
        send(ActivityKafkaTopic.PP_TOPIC_CP_IN_LIVE_ONE_MINUTE, String.valueOf(msg.getUserId()), JSON.toJSONString(msg));
    }
}
