package fm.lizhi.pp.activity.database.dao.amusementinteract;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.pp.activity.database.entity.AmusementInteractLivePlayWayUser;
import fm.lizhi.pp.activity.database.mapper.amusementinteract.AmusementInteractLivePlayWayUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@AutoBindSingleton
@Slf4j
public class AmusementInteractLivePlayWayUserDao {
    @Inject
    private AmusementInteractLivePlayWayUserMapper amusementInteractLivePlayWayUserMapper;

    public AmusementInteractLivePlayWayUser getById(Long id) {
        AmusementInteractLivePlayWayUser query = new AmusementInteractLivePlayWayUser();
        query.setId(id);
        return amusementInteractLivePlayWayUserMapper.selectOne(query);
    }

    public AmusementInteractLivePlayWayUser getByNjIdAndPlayWayId(Long njId, Long playWayId) {
        AmusementInteractLivePlayWayUser query = new AmusementInteractLivePlayWayUser();
        query.setNjId(njId);
        query.setPlayWayId(playWayId);
        return amusementInteractLivePlayWayUserMapper.selectOne(query);
    }

    public int add(AmusementInteractLivePlayWayUser playWayUser) {
        if (playWayUser == null) {
            return 0;
        }

        playWayUser.setCreateTime(new Date());
        playWayUser.setModifyTime(new Date());

        return amusementInteractLivePlayWayUserMapper.insert(playWayUser);
    }

    public int updateByPrimaryKey(AmusementInteractLivePlayWayUser playWayUser) {
        if (playWayUser == null || playWayUser.getId() == null) {
            return 0;
        }

        return amusementInteractLivePlayWayUserMapper.updateByPrimaryKey(playWayUser);
    }

    public Map<Long, AmusementInteractLivePlayWayUser> getPlayWayId2UserSetPlayWay(long njId, Integer adminStatus) {
        AmusementInteractLivePlayWayUser query = new AmusementInteractLivePlayWayUser();
        query.setNjId(njId);
        query.setAdminStatus(adminStatus);
        List<AmusementInteractLivePlayWayUser> userSetPlayWays
                = amusementInteractLivePlayWayUserMapper.selectMany(query);
        Map<Long, AmusementInteractLivePlayWayUser> playWayId2UserSetPlayWay = CollectionUtils.isEmpty(userSetPlayWays)
                ? new HashMap<>()
                : userSetPlayWays.stream().collect(Collectors.toMap(AmusementInteractLivePlayWayUser::getPlayWayId, playWay -> playWay));
        return playWayId2UserSetPlayWay;
    }
}
