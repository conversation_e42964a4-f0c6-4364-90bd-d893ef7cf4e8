package fm.lizhi.pp.activity.domain.playway.model.valueobject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 美式8球
 * <p>
 *
 * <AUTHOR>
 * @Date 2023-05-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EightBallGameConfig extends PlayWayGameConfig {
    /**
     * 游戏配置
     */
    private String gameConfig = "";
    /**
     * 最大参与人数
     */
    private int maxJoinCount;
    /**
     * 最长小游戏时长，默认3天，单位：秒
     */
    private int maxTime = 86400 * 3;
}

