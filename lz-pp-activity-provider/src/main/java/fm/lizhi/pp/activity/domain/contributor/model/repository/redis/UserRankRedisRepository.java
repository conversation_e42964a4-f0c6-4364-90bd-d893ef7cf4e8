package fm.lizhi.pp.activity.domain.contributor.model.repository.redis;

import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.datastore.RedisTuple;
import fm.lizhi.pp.activity.confg.PpActivityConfig;
import fm.lizhi.pp.activity.domain.contributor.model.ContributorRank;
import fm.lizhi.pp.activity.database.redis.PpActivityRedis;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/8/26
 * @description 比赛贡献榜Redis 资源库
 */
@Slf4j
@AutoBindSingleton
public class UserRankRedisRepository {

    @Inject
    private PpActivityRedis ppActivityRedis;
    @Inject
    private PpActivityConfig liveMatchConfig;

    /**
     * 获取某个PK比赛下指定房主的贡献列表
     *
     * @param njId
     * @param matchId
     * @return
     */
    public List<ContributorRank> getUserRankList(long njId, long matchId) {
        List<ContributorRank> list = new ArrayList<>();
        Set<RedisTuple> redisTuples = ppActivityRedis.zrevrangeWithScores(
                getBaseKey(njId, matchId), 0, -1);
        for (RedisTuple redisTuple : redisTuples) {
            ContributorRank contributorRank = new ContributorRank(Long.valueOf(redisTuple.getElement()),
                    BigDecimal.valueOf(redisTuple.getScore()).intValue());
            list.add(contributorRank);
        }

        if (list.size() > 3) {
            return list.subList(0, 3);
        }
        return list;
    }

    /**
     * 记录用户的贡献
     *
     * @param njId    房主ID
     * @param matchId 比赛ID
     * @param userId  送礼用户ID
     * @param score   分数
     */
    public void addUserRank(long njId, long matchId, long userId, long score) {
        ppActivityRedis.zincrby(getBaseKey(njId, matchId), BigDecimal.valueOf(score).doubleValue(),
                String.valueOf(userId));
        ppActivityRedis.expire(getBaseKey(njId, matchId), liveMatchConfig.getExpirationTime());
    }

    /**
     * 获取用户排行
     *
     * @param njId
     * @param matchId
     * @param userId
     * @return
     */
    public int getUserIndex(long njId, long matchId, long userId) {
        Long index = ppActivityRedis.zrevrank(getBaseKey(njId, matchId), String.valueOf(userId));
        if (null == index) {
            return 0;
        }

        return BigDecimal.valueOf(index).intValue();
    }

    /**
     * 获取某个PK比赛中指定房主的最大贡献用户Id
     *
     * @param njId
     * @param matchId
     * @return
     */
    public List<Long> getMaxContributorId(long njId, long matchId, int count) {
        Set<String> result = ppActivityRedis.zrevrange(getBaseKey(njId, matchId), 0L, count - 1);
        //返回0L, 表示没有贡献用户
        if (result.size() <= 0) {
            return Collections.emptyList();
        }
        List<Long> ret = new ArrayList<>();
        for (String s : result) {
            ret.add(Long.parseLong(s));
        }
        return ret;
    }

    /**
     * 获取某个PK比赛中指定房主的贡献者人数
     *
     * @param njId
     * @param matchId
     * @return
     */
    public long getContributorAmount(long njId, long matchId) {
        return ppActivityRedis.zcount(getBaseKey(njId, matchId), Double.MIN_VALUE, Double.MAX_VALUE);
    }

    /**
     * 获取基础key
     *
     * @param njId
     * @param matchId
     * @return
     */
    private String getBaseKey(long njId, long matchId) {
        String key = UserRankRedisKey.LIVE_PK_MATCH_RANK.getRedisKey(njId, matchId);
        return key;
    }
}
