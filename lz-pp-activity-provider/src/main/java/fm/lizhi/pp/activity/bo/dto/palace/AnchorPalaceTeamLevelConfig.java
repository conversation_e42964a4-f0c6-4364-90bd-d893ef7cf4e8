package fm.lizhi.pp.activity.bo.dto.palace;

import lombok.Data;

import java.util.List;

/**
 * 游戏规则，团队内 主播等级
 */
@Data
public class AnchorPalaceTeamLevelConfig {
    private int startTime;
    /**
     * 结束分钟 不能相等，需要用用小于
     */
    private int endTime;
    private int awardStartTime;
    /**
     * 结束分钟 不能相等，需要用用小于
     */
    private int awardEndTime;

    /**
     * 等级阶段提示文案
     */
    private String tipsContent;

    /**
     * 主播等级配置
     */
    private List<AnchorLevelAward> anchorPalaceLevel;

    // Getters and setters
}