package fm.lizhi.pp.activity.api.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.inject.Inject;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.amusement.task.api.AwardPoolService;
import fm.lizhi.live.amusement.task.bean.award.AwardType;
import fm.lizhi.live.amusement.task.protocol.AwardPoolProto;
import fm.lizhi.pp.activity.bean.monopoly.MonopolyGameConfig;
import fm.lizhi.pp.activity.bean.monopoly.MonopolySquareAwardConfig;
import fm.lizhi.pp.activity.bean.monopoly.UseDiceContext;
import fm.lizhi.pp.activity.confg.PpActivityConfig;
import fm.lizhi.pp.activity.manager.MonopolyManager;
import fm.lizhi.pp.activity.manager.PropManager;
import fm.lizhi.pp.activity.manager.UserGroupManager;
import fm.lizhi.pp.activity.database.redis.monopoly.MonopolyRedisManager;
import fm.lizhi.pp.monopoly.api.MonopolyService;
import fm.lizhi.pp.monopoly.constant.MonopolySquareType;
import fm.lizhi.pp.monopoly.protocol.MonopolyProto;
import fm.lizhi.pp.util.constant.AppIdConstant;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 大富翁
 *
 * <AUTHOR>
 * @date 2023/12/14 16:47
 */
@ServiceProvider
@Slf4j
public class MonopolyServiceImpl implements MonopolyService {
    @Inject
    private PpActivityConfig ppActivityConfig;
    @Inject
    private MonopolyRedisManager monopolyRedisManager;
    @Inject
    private MonopolyManager monopolyManager;
    @Inject
    private PropManager propManager;
    @Inject
    private AwardPoolService awardPoolService;
    @Inject
    private UserGroupManager userGroupManager;

    @Override
    public Result<MonopolyProto.ResponseGetGameInfo> getGameInfo(long userId) {
        MonopolyProto.ResponseGetGameInfo.Builder builder = MonopolyProto.ResponseGetGameInfo.newBuilder();
        buildGameInfo(ppActivityConfig.getMonopolyGameConfigBean(), builder);
        // 播报内容
        builder.addAllBroadcastList(getMonopolyBroadcast());

        long userCoin = monopolyRedisManager.getUserCoin(userId);
        int userIndex = monopolyRedisManager.getMonopolyUserIndex(userId);
        builder.setCoinProgressCount((int) (userCoin % ppActivityConfig.getMonopolyCoinPreDice()))
                .setCoinProgressTarget(ppActivityConfig.getMonopolyCoinPreDice())
                .setDiceCount((int) propManager.getPropAmount(userId, ppActivityConfig.monopolyDicePropId))
                .setUserIndex(userIndex <= 0 ? 1 : userIndex)
                .setBoxCount(monopolyManager.getBoxCount(userId));

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
    }

    @Override
    public Result<MonopolyProto.ResponseUseDice> useDice(long userId) {
        LogContext.addReqLog("userId={}", userId);
        LogContext.addResLog("userId={}", userId);

        if (!ppActivityConfig.isMonopolySwitch()
                || !userGroupManager.doIsUserInGroup(ppActivityConfig.getMonopolyWhitelistGroupId(), userId)) {
            // 玩法关闭 或 白名单控制
            return new Result<>(MonopolyService.USE_DICE_FAILED, null);
        }

        UseDiceContext context = new UseDiceContext();
        context.setUserId(userId);
        context.setMoveIndexList(Lists.newArrayList());
        if (!monopolyManager.useDice(context)) {
            return new Result<>(MonopolyService.USE_DICE_FAILED, null);
        }

        List<MonopolyProto.Move> moveList = context.getMoveIndexList().stream().map(item -> MonopolyProto.Move.newBuilder()
                .setType(item.getLeft().getType()).setIndex(item.getRight()).build()).collect(Collectors.toList());

        MonopolyProto.ResponseUseDice.Builder builder = MonopolyProto.ResponseUseDice.newBuilder()
                .addAllMoveIndexList(moveList).setDiceResult(context.getDiceResult())
                .setObtainBox(context.isObtainBox());

        MonopolySquareAwardConfig award = context.getAward();
        if (award != null) {
            builder.setAwardWindow(
                    MonopolyProto.AwardWindow.newBuilder().setImage(award.getImage())
                            .setName(award.getName()).setTips(award.getTips()).setCount(award.getCount())
                            .setQuantifier(award.getQuantifier()).build()
            );
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, builder.build());
    }

    @Override
    public Result<MonopolyProto.ResponseOpenBox> openBox(long userId) {
        LogContext.addReqLog("userId={}", userId);
        LogContext.addResLog("userId={}", userId);

        if (!ppActivityConfig.isMonopolySwitch()
                || !userGroupManager.doIsUserInGroup(ppActivityConfig.getMonopolyWhitelistGroupId(), userId)) {
            // 玩法关闭 或 白名单控制
            return new Result<>(MonopolyService.OPEN_BOX_FAILED, null);
        }

        Result<AwardPoolProto.ResponseLottery> result = awardPoolService.lottery(
                userId, ppActivityConfig.getMonopolyBoxAwardPoolId(), 0, 0, AppIdConstant.PPYW.getAppId());
        int rCode = result.rCode();
        if (rCode != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("awardPoolService.lottery error, rCode={}, userId={}", rCode, userId);
            return new Result<>(MonopolyService.OPEN_BOX_FAILED, null);
        }

        MonopolyProto.AwardWindow.Builder builder = MonopolyProto.AwardWindow.newBuilder();
        AwardPoolProto.Award award = result.target().getAward();
        String awardType = award.getAwardType();
        String broadcastAwardName;
        if (AwardType.PP_COIN.name().equals(awardType)) {
            builder.setAwardType(1).setTips("奖励已下发到您的「金币账户」").setCount((int) award.getCount())
                    .setName("金币").setQuantifier("金币");
            broadcastAwardName = formatCoinAwardName(award.getCount());
        } else {
            buildGoodsAwardWindow(builder, award);
            broadcastAwardName = award.getAwardName();
        }

        monopolyManager.saveMonopolyBroadcast(userId, broadcastAwardName);

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS,
                MonopolyProto.ResponseOpenBox.newBuilder().setAwardWindow(builder.build()).build());
    }

    /**
     * 构造物品奖励弹窗信息
     *
     * @param builder
     * @param award
     */
    private void buildGoodsAwardWindow(MonopolyProto.AwardWindow.Builder builder, AwardPoolProto.Award award) {
        builder.setAwardType(2).setImage(award.getImg()).setName(award.getAwardName())
                .setTips("").setCount((int) award.getCount()).setQuantifier("");

        String awardType = award.getAwardType();
        if (AwardType.AVATAR_WIDGET.name().equals(awardType) || AwardType.MOUNT.name().equals(awardType)
                || AwardType.BUBBLE.name().equals(awardType)) {
            builder.setTips("请到「直播间-更多-个性装扮」中使用奖励").setQuantifier("天");
        } else if (AwardType.PROP.name().equals(awardType) &&
                (ppActivityConfig.getMonopolyAwardTicketPropIds() + ",").contains(award.getAwardId() + ",")) {
            // 奖励包裹且是某种券
            builder.setTips("请到「直播间-礼物栏-包裹」中使用奖励").setQuantifier("张");
        }
    }

    /**
     * 格式化金币奖励名称
     *
     * @param count
     * @return
     */
    private String formatCoinAwardName(long count) {
        if (count >= 10000) {
            return (int) (count / 10000) + "w金币";
        }
        return count + "金币";
    }

    /**
     * 构造游戏静态数据
     *
     * @param config
     * @param builder
     */
    private void buildGameInfo(MonopolyGameConfig config, MonopolyProto.ResponseGetGameInfo.Builder builder) {
        builder.setGameConfVersion(config.getGameConfVersion()).setSquareTotalCount(config.getSquareTotalCount());
        if (CollectionUtil.isNotEmpty(config.getSquareList())) {
            builder.addAllSquareList(config.getSquareList().stream().map(item -> {
                MonopolyProto.Square.Builder squareBuilder = MonopolyProto.Square.newBuilder()
                        .setIndex(item.getIndex()).setType(item.getType()).setIcon(item.getIcon())
                        .setHighlightIcon(item.getHighlightIcon()).setSizeType(item.getSizeType())
                        .setContent(item.getContent());
                if (item.getType() == MonopolySquareType.JUMP.getType()) {
                    squareBuilder.setJumpTargetIndex(item.getMoveValue());
                }
                MonopolySquareAwardConfig award = item.getAward();
                if (award != null) {
                    squareBuilder.setAward(MonopolyProto.SquareAward.newBuilder()
                            .setImage(award.getImage()).setCount(award.getCount())
                            .setName(award.getName()).setQuantifier(award.getQuantifier()).build());
                }
                return squareBuilder.build();
            }).collect(Collectors.toList()));
        }
    }

    /**
     * 获取播报内容列表
     *
     * @return
     */
    private List<String> getMonopolyBroadcast() {
        List<String> listFromRedis = monopolyRedisManager.getMonopolyBroadcast();
        if (listFromRedis.size() < ppActivityConfig.getMonopolyBroadcastMinCount()) {
            // 补数据
            int count = Math.min(
                    ppActivityConfig.getMonopolyBroadcastMinCount() - listFromRedis.size(),
                    ppActivityConfig.getMonopolyBroadcastSpareContentList().size()
            );
            listFromRedis.addAll(ppActivityConfig.getMonopolyBroadcastSpareContentList().subList(0, count));
        }
        return listFromRedis;
    }
}
