package fm.lizhi.pp.activity.constant.vote;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 投票结束类型
 */
@Getter
@AllArgsConstructor
public enum VoteEndType {
    /**
     * 正常结束
     */
    NORMAL(1),
    /**
     * 提前结束投票
     */
    EARLY_VOTE(2),
    /**
     * 提前结束惩罚
     */
    EARLY_PUNISH(3),
    /**
     * 阵营不足
     */
    CAMP(4),
    /**
     * 主播数量不足
     */
    ANCHOR(5),
    /**
     * 关播
     */
    CLOSED_LIVE(6),
    ;

    private final int type;
}
