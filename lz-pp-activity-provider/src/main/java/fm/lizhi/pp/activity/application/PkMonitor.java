package fm.lizhi.pp.activity.application;

import com.dianping.cat.Cat;
import fm.lizhi.pp.activity.constant.PkCommandEnum;

import java.util.LinkedHashMap;

/**
 * pk监控
 *
 * <AUTHOR>
 */
public class PkMonitor {

    public static final Integer EXCEPTION = -1;
    public static final String SUCCESS = "0";
    public static final String FAIL = "1";
    private static final String LIVE_ROOM_PK = "live_room_pk";
    /**
     * 场景
     */
    private String scene;
    /**
     * 0成功 1失败
     */
    private String result;
    /**
     * -1：匹配程序异常
     * 0：匹配成功
     * {@link PkCommandEnum}
     */
    private String type;
    /**
     * 维度map
     */
    private final LinkedHashMap<String, String> tags = new LinkedHashMap<>();

    public PkMonitor(String scene) {
        this.scene = scene;
        addTag("scene", LIVE_ROOM_PK);
    }

    public PkMonitor setResult(String result) {
        this.result = String.valueOf(result);
        addTag("result", this.result);
        return this;
    }

    public PkMonitor setType(Integer type) {
        this.type = String.valueOf(type);
        addTag("type", this.type);
        return this;
    }

    private PkMonitor addTag(String name, String value) {
        tags.put(name, value);
        return this;
    }

    public void catCount() {
        Cat.logMetricForCount(scene, 1, tags);
    }

    public static PkMonitor build() {
        return new PkMonitor(LIVE_ROOM_PK);
    }
}
