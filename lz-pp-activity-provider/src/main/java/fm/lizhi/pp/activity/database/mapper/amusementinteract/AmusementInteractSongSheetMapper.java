package fm.lizhi.pp.activity.database.mapper.amusementinteract;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseDeleteProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseInsertProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseSelectProvider;
import fm.lizhi.common.datastore.mysql.mybatis.sqlprovider.BaseUpdateProvider;
import fm.lizhi.pp.activity.database.entity.AmusementInteractSongSheet;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 */
@DataStore(namespace = "mysql_pplive_lzppactivity")
public interface AmusementInteractSongSheetMapper {

    /**
     * 根据实体对象的字段值查询多条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 实体对象列表
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "select")
    List<AmusementInteractSongSheet> selectMany(AmusementInteractSongSheet entity);

    /**
     * 根据实体对象的字段值查询单条记录，查询条件会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "select")
    AmusementInteractSongSheet selectOne(AmusementInteractSongSheet entity);

    /**
     * 根据实体对象中主键（{@link javax.persistence.Id}标注）字段查询单条数据。
     *
     * @param entity 实体对象
     * @return 单个实体对象
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "selectByPrimaryKey")
    AmusementInteractSongSheet selectByPrimaryKey(AmusementInteractSongSheet entity);

    /**
     * 根据实体对象的字段值查询分页记录，查询条件会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 分页数据
     */
    @SelectProvider(type = BaseSelectProvider.class, method = "selectPage")
    PageList<AmusementInteractSongSheet> selectPage(@Param(ParamContants.ENTITIE) AmusementInteractSongSheet entity, @Param(ParamContants.PAGE_NUMBER) int pageNumber, @Param(ParamContants.PAGE_SIZE) int pageSize);

    /**
     * 根据实体对象中主键字段（{@link javax.persistence.Id}标注）删除数据。
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    @DeleteProvider(type = BaseDeleteProvider.class, method = "deleteByPrimaryKey")
    int deleteByPrimaryKey(AmusementInteractSongSheet entity);

    /**
     * 将实体对象写入数据库，会跳过NULL值的字段。<br/>
     * 如果主键字段（{@link javax.persistence.Id}标注）有设置{@link javax.persistence.GeneratedValue}时，会自动生成主键并设置到实体对象中。
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method = "insert")
    int insert(AmusementInteractSongSheet entity);

    /**
     * 批量将实体对象写入数据库（不会跳过NULL值)。<br/>
     * 如果字段有设置{@link javax.persistence.Id}和{@link javax.persistence.GeneratedValue}时，会自动生成值，并设置到实体类实例中。
     *
     * @param entities 实体对象列表
     * @return 影响行数
     */
    @InsertProvider(type = BaseInsertProvider.class, method = "batchInsert")
    int batchInsert(@Param(ParamContants.ENTITIES) List<AmusementInteractSongSheet> entities);

    /**
     * 根据实体类中主键（{@link javax.persistence.Id}标注的字段）更新数据，会跳过NULL值的字段。
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    @UpdateProvider(type = BaseUpdateProvider.class, method = "updateByPrimaryKey")
    int updateByPrimaryKey(AmusementInteractSongSheet entity);

    @Select({"<script>",
            "select * from amusement_interact_song_sheet where is_delete = 0 and  id in ",
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>#{id}</foreach>",
            "</script>"})
    List<AmusementInteractSongSheet> queryByIds(@Param("ids") List<Long> ids);

    @Update({"<script>",
            "update amusement_interact_song_sheet set is_delete = 1 where id in ",
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>#{id}</foreach>",
            "</script>"})
    int deleteByIds(@Param("ids") List<Long> ids);

    @Select("select * from amusement_interact_song_sheet where is_delete=0 and nj_id=#{njId} order by is_top desc,modify_time desc")
    List<AmusementInteractSongSheet> queryByNjId(@Param("njId") long njId);
}
