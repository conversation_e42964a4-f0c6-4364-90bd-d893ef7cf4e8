package fm.lizhi.pp.activity.xxl.vote;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import fm.lizhi.pp.activity.bo.vote.VoteTargetBo;
import fm.lizhi.pp.activity.bo.vote.VoteUserScoreBo;
import fm.lizhi.pp.activity.confg.PpActivityConfig;
import fm.lizhi.pp.activity.database.dao.VoteDao;
import fm.lizhi.pp.activity.database.entity.VoteInfo;
import fm.lizhi.pp.activity.manager.CommentManager;
import fm.lizhi.pp.activity.manager.PpNewUserManager;
import fm.lizhi.pp.activity.manager.vote.VoteEventManager;
import fm.lizhi.pp.activity.manager.vote.VoteManager;
import fm.lizhi.pp.user.account.user.protocol.PpUserBaseProto;
import fm.lizhi.pp.vote.constant.*;
import fm.lizhi.pp.vote.dto.VotePushEventDto;
import fm.lizhi.pp.vote.dto.VoteTargetDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;
import redis.clients.jedis.Tuple;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Slf4j
@JobHandler(value = "voteEndStatusJob")
@AutoBindSingleton(baseClass = IJobHandler.class, multiple = true)
public class VoteEndStatusJob extends IJobHandler {

    private ExecutorService executorService;

    @Inject
    private VoteEventManager voteEventManager;
    @Inject
    private PpNewUserManager ppNewUserManager;
    @Inject
    private PpActivityConfig ppActivityConfig;
    @Inject
    private VoteDao voteDao;
    @Inject
    private CommentManager commentManager;
    @Inject
    private VoteManager voteManager;

    @PostConstruct
    public void init() {
        executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors(), new ThreadFactory() {
            private AtomicInteger ids = new AtomicInteger(0);

            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r);
                thread.setDaemon(true);
                thread.setName("vote-end-check-task-executor-" + ids.getAndIncrement());
                return thread;
            }
        });
    }

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        Date now = new Date();
        List<VoteInfo> voteInfoList = voteDao.selectVoteEnd(now);

        if (CollectionUtils.isEmpty(voteInfoList)) {
            return SUCCESS;
        }
        List<List<VoteInfo>> partition = Lists.partition(voteInfoList, 20);
        StopWatch sw = new StopWatch();
        sw.start();
        for (List<VoteInfo> subList : partition) {
            CheckVoteStatusThread checkGameInfoThread = new CheckVoteStatusThread(now, subList);
            executorService.submit(RunnableWrapper.of(checkGameInfoThread));
        }
        sw.stop();
        log.info("VoteEndStatusJob CountDownLatch  start,partitionSize:{},totalSize:{},cost second:{}", partition.size(), voteInfoList.size(), sw.getTotalTimeSeconds());
        return SUCCESS;
    }

    class CheckVoteStatusThread implements Runnable {

        private Date jobTime;

        private List<VoteInfo> voteList;

        public CheckVoteStatusThread(Date jobTime, List<VoteInfo> voteList) {
            this.jobTime = jobTime;
            this.voteList = voteList;
        }

        @Override
        public void run() {
            if (CollectionUtils.isEmpty(voteList)) {
                return;
            }

            try {
                for (VoteInfo voteInfo : voteList) {
                    Long liveId = voteInfo.getLiveId();
                    if (!voteManager.xxlCheckVoteInfo(voteInfo)) {
                        continue;
                    }

                    VoteInfo forMaster = voteDao.getVoteInfoById(voteInfo.getId(), true);
                    Date voteEndTime = forMaster.getVoteEndTime();
                    //如果结束时间小于当前时间,并且投票状态为投票中，结束投票
                    if (voteEndTime.before(jobTime) && forMaster.getVoteStatus() == VoteStatus.VOTE.getStatus()) {
                        boolean locked = voteDao.voteOptVoteLockRetry(forMaster.getId());
                        // 加锁失败
                        if (!locked) {
                            //等到下一次XXL任务执行
                            log.info("VoteEndStatusJob but lock fail,id={}", voteInfo.getId());
                            continue;
                        }
                        try {
                            //再次获取一下，以防有改动
                            forMaster = voteDao.getVoteInfoById(voteInfo.getId(), true);
                            //再次获取状态不是投票中的，不处理了
                            if (forMaster.getVoteStatus() != VoteStatus.VOTE.getStatus()) {
                                continue;
                            }
                            //更新数据
                            voteDao.updStatus(forMaster.getId(), VoteStatus.PUNISH, null);

                        } catch (Exception e) {
                            log.error("VoteEndStatusJob error, id={}", voteInfo.getId(), e);
                        } finally {
                            // 解锁
                            voteDao.voteOptVoteUnlock(voteInfo.getId());
                        }

                        //处理 MVP 逻辑
                        String playerListJson = forMaster.getPlayerListJson();
                        if (!StringUtils.isEmpty(playerListJson)) {
                            List<VoteTargetDto> list = JSONArray.parseArray(playerListJson, VoteTargetDto.class);
                            if (forMaster.getVoteType().equals(VoteType.PERSON.getType())) {
                                //处理主播的MVP 逻辑
                                doAnchorMvp(list, forMaster);
                            } else if (forMaster.getVoteType().equals(VoteType.CAMP.getType())) {
                                doCampMvp(list, forMaster);
                            }
                        }

                        //投票信息变化消息
                        voteEventManager.sendVotePushDataChangeMsg(forMaster.getId(), forMaster.getLiveId(), VoteStatus.PUNISH.getStatus(),
                                VotePushType.DATA_CHANGE, null);

                        //发送投票完成任务消息
                        voteEventManager.taskRoomFinishVote(forMaster);
                        //发送公屏
                        commentManager.sendSysComment(liveId, forMaster.getStartUserId(), ppActivityConfig.getVoteEndSysContent());
                        //处理用户送礼的金币任务
                        sendUserGiftCoin(forMaster);
                    }
                }
            } catch (Exception e) {
                log.error("VoteEndStatusJob handler error", e);
            }
        }


        /**
         * 处理用户送礼的金币任务
         *
         * @param forMaster
         */
        private void sendUserGiftCoin(VoteInfo forMaster) {
            Set<Tuple> voteUserGiftCoin = voteDao.getVoteUserGiftCoin(forMaster.getId());
            if (CollectionUtils.isEmpty(voteUserGiftCoin)) {
                return;
            }
            for (Tuple tuple : voteUserGiftCoin) {
                String userId = tuple.getElement();
                double score = tuple.getScore();
                voteEventManager.taskUserGiftCoin(forMaster, Long.parseLong(userId), (long) score);
            }
        }

        private void doCampMvp(List<VoteTargetDto> list, VoteInfo forMaster) {
            List<VoteTargetBo> scoreList = new ArrayList<>(8);
            for (VoteTargetDto voteTargetDto : list) {
                if (voteTargetDto.getStatus() != VoteTargetStatus.NORMAL.getStatus()) {
                    continue;
                }
                List<VoteTargetDto> children = voteTargetDto.getChildren();
                if (CollectionUtils.isEmpty(children)) {
                    continue;
                }
                //累计阵营下主播的分数
                int total = 0;
                for (VoteTargetDto childDto : children) {
                    if (childDto.getStatus() == VoteTargetStatus.NORMAL.getStatus()) {
                        Integer voteNjScore = voteDao.getVoteNjScore(forMaster.getId(), childDto.getId());
                        total += voteNjScore;
                    }
                }
                VoteTargetBo voteTargetBo = new VoteTargetBo();
                voteTargetBo.setId(voteTargetDto.getId());
                voteTargetBo.setScore(total);
                voteTargetBo.setType(VoteTargetType.CAMP.getType());
                voteTargetBo.setChildren(children);
                scoreList.add(voteTargetBo);
            }
            if (CollectionUtils.isEmpty(scoreList)) {
                return;
            }

            //获取最低分数
            Optional<VoteTargetBo> min = scoreList.stream().min(Comparator.comparing(VoteTargetBo::getScore));
            //获取最高分数
            Optional<VoteTargetBo> max = scoreList.stream().max(Comparator.comparing(VoteTargetBo::getScore));
            //分数相同的情况
            if (min.get().getScore() == max.get().getScore()) {
                //如果分数都相同，则判断是否大于0，小于0
                if (max.get().getScore() > 0) {
                    //所有人都是获胜者
                    for (VoteTargetBo voteTargetBo : scoreList) {
                        voteDao.saveWinCampData(forMaster.getId(), voteTargetBo.getId());
                        List<VoteTargetDto> children = voteTargetBo.getChildren();
                        if (!CollectionUtils.isEmpty(children)) {
                            // 在投票中获胜，个人投票的获胜者，或者是对应的阵营获胜
                            for (VoteTargetDto child : children) {
                                if (child.getStatus() != VoteTargetStatus.NORMAL.getStatus()) {
                                    continue;
                                }
                                voteEventManager.taskAnchorWinVote(forMaster, child.getId());
                            }
                        }
                    }
                    pushCampMvp(list, forMaster, scoreList);
                    return;
                }
                if (min.get().getScore() <= 0) {
                    //所有人都是受罚者
                    for (VoteTargetBo voteTargetBo : scoreList) {
                        voteDao.saveLoseCampData(forMaster.getId(), voteTargetBo.getId());
                        // 为阵营下的主播设置惩罚特效
                        voteTargetBo.getChildren().forEach(e -> {
                            voteDao.savePunishEffect(forMaster.getId(), e.getId(), voteManager.randomPunishEffectId());
                        });
                    }
                }
                return;
            }


            //获取列表中最低分数相同的数据，并且分数是负数才需要受罚
            List<VoteTargetBo> minList = scoreList.stream().filter(voteTargetBo -> voteTargetBo.getScore() == min.get().getScore()).collect(Collectors.toList());
            //存在受罚者，保存数据
            for (VoteTargetBo voteTargetBo : minList) {
                long voteIdForMaster = forMaster.getId();
                voteDao.saveLoseCampData(voteIdForMaster, voteTargetBo.getId());
                // 为阵营下的主播设置惩罚特效
                voteTargetBo.getChildren().forEach(e -> {
                    voteDao.savePunishEffect(voteIdForMaster, e.getId(), voteManager.randomPunishEffectId());
                });
            }

            //获取列表中最高分数相同的数据
            List<VoteTargetBo> maxList = scoreList.stream().filter(voteTargetBo -> voteTargetBo.getScore() == max.get().getScore()).collect(Collectors.toList());
            //存在获胜者，保存数据
            for (VoteTargetBo voteTargetBo : maxList) {
                voteDao.saveWinCampData(forMaster.getId(), voteTargetBo.getId());
                List<VoteTargetDto> children = voteTargetBo.getChildren();
                if (!CollectionUtils.isEmpty(children)) {
                    // 在投票中获胜，个人投票的获胜者，或者是对应的阵营获胜
                    for (VoteTargetDto child : children) {
                        if (child.getStatus() != VoteTargetStatus.NORMAL.getStatus()) {
                            continue;
                        }
                        voteEventManager.taskAnchorWinVote(forMaster, child.getId());
                    }
                }
            }
            pushCampMvp(list, forMaster, maxList);
        }

        private void pushCampMvp(List<VoteTargetDto> list, VoteInfo forMaster, List<VoteTargetBo> maxList) {
            //处理需要推送MVP的用户
            List<VoteUserScoreBo> userScoreBoList = new ArrayList<>(8);
            for (VoteTargetBo voteTargetBo : maxList) {
                //统计每个阵营下用户贡献最大的分数
                Set<Tuple> maxUserScoreSet = voteDao.getVoteCampMaxUserScore(forMaster.getId(), voteTargetBo.getId());
                if (CollectionUtils.isEmpty(maxUserScoreSet)) {
                    continue;
                }
                for (Tuple tuple : maxUserScoreSet) {
                    VoteUserScoreBo userScoreBo = new VoteUserScoreBo();
                    userScoreBo.setUserId(Long.parseLong(tuple.getElement()));
                    userScoreBo.setBizId(voteTargetBo.getId());
                    userScoreBo.setType(voteTargetBo.getType());
                    userScoreBo.setScore((int) tuple.getScore());
                    userScoreBoList.add(userScoreBo);
                }
            }
            if (CollectionUtils.isEmpty(userScoreBoList)) {
                return;
            }
            //获取用户贡献值最大的数据
            Map<Long, List<Long>> userNjMap = getUserMaxScore(userScoreBoList);

            //如果数据不为空，需要处理MVP的用户
            if (CollectionUtils.isEmpty(userNjMap)) {
                return;
            }
            Map<Long, String> campIdNameMap = list.stream().collect(Collectors.toMap(VoteTargetDto::getId, VoteTargetDto::getName));
            Set<Long> sendUserMvpSet = new HashSet<>();
            for (Map.Entry<Long, List<Long>> entry : userNjMap.entrySet()) {
                List<Long> campIdList = entry.getValue();
                Collections.shuffle(campIdList);
                Long userId = entry.getKey();
                //推送用户
                VotePushEventDto dto = new VotePushEventDto();
                dto.setVoteId(forMaster.getId());
                dto.setMvpUserId(userId);
                dto.setVoteStatus(VoteStatus.PUNISH.getStatus());
                dto.setPushType(VotePushType.MVP.getType());
                dto.setVoteType(forMaster.getVoteType());
                dto.setLiveId(forMaster.getLiveId());
                Long campId = campIdList.get(0);
                String campName = campIdNameMap.get(campId);
                dto.setMvpCampName(campName);
                String voteCampMvpContent = ppActivityConfig.getVoteCampMvpContent().replace("#{name}", campName);
                dto.setContent(voteCampMvpContent);
                //发送推送MVP  MQ消息
                if (!sendUserMvpSet.contains(userId)) {
                    sendUserMvpSet.add(userId);
                    //发送推送MVP  MQ消息
                    voteEventManager.sendVotePushMvpMsg(dto);
                }
                //用户成为MVP 任务
                voteEventManager.taskUserMvp(forMaster, userId);
            }
        }


        /**
         * 获取用户贡献值最大的数据
         *
         * @param userScoreBoList
         * @return
         */
        private Map<Long, List<Long>> getUserMaxScore(List<VoteUserScoreBo> userScoreBoList) {
            Map<Long, List<Long>> userNjMap = new ConcurrentHashMap<>();//一个用户对应多个获胜方的列表
            Map<Long, List<VoteUserScoreBo>> userIdMapList = userScoreBoList.stream().collect(Collectors.groupingBy(VoteUserScoreBo::getUserId, Collectors.toList()));
            //保留分数贵高的数据，因为每个主播对应的用户最高贡献值的分数都不同
            for (Map.Entry<Long, List<VoteUserScoreBo>> entry : userIdMapList.entrySet()) {
                List<VoteUserScoreBo> value = entry.getValue();
                //获取贡献值最高的
                Optional<VoteUserScoreBo> max = value.stream().max(Comparator.comparing(VoteUserScoreBo::getScore));
                List<Long> maxUserScoreList = value.stream()
                        .filter(voteUserScoreBo -> voteUserScoreBo.getScore() == max.get().getScore())
                        .map(VoteUserScoreBo::getBizId)
                        .collect(Collectors.toList());
                userNjMap.put(entry.getKey(), maxUserScoreList);
            }
            return userNjMap;
        }

        private void doAnchorMvp(List<VoteTargetDto> list, VoteInfo forMaster) {
            List<VoteTargetBo> scoreList = new ArrayList<>(8);
            for (VoteTargetDto voteTargetDto : list) {
                if (voteTargetDto.getStatus() == VoteTargetStatus.NORMAL.getStatus()) {
                    Integer voteNjScore = voteDao.getVoteNjScore(forMaster.getId(), voteTargetDto.getId());
                    VoteTargetBo voteTargetBo = new VoteTargetBo();
                    voteTargetBo.setId(voteTargetDto.getId());
                    voteTargetBo.setScore(voteNjScore);
                    voteTargetBo.setType(VoteTargetType.ANCHOR.getType());
                    scoreList.add(voteTargetBo);
                }
            }
            if (CollectionUtils.isEmpty(scoreList)) {
                return;
            }

            //获取最低分数
            Optional<VoteTargetBo> min = scoreList.stream().min(Comparator.comparing(VoteTargetBo::getScore));
            //获取最高分数
            Optional<VoteTargetBo> max = scoreList.stream().max(Comparator.comparing(VoteTargetBo::getScore));
            //分数相同的情况
            if (min.get().getScore() == max.get().getScore()) {
                //如果分数都相同，则判断是否大于0，小于0
                if (max.get().getScore() > 0) {
                    //所有人都是获胜者
                    for (VoteTargetBo voteTargetBo : scoreList) {
                        voteDao.saveWinAnchorData(forMaster.getId(), voteTargetBo.getId());
                        //在投票中获胜，个人投票的获胜者，或者是对应的阵营获胜
                        voteEventManager.taskAnchorWinVote(forMaster, voteTargetBo.getId());
                    }
                    pushAnchorMvp(forMaster, scoreList);
                    return;
                }
                if (min.get().getScore() <= 0) {
                    //所有人都是受罚者
                    for (VoteTargetBo voteTargetBo : scoreList) {
                        voteDao.saveLosePlayerData(forMaster.getId(), voteTargetBo.getId());
                        voteDao.savePunishEffect(forMaster.getId(), voteTargetBo.getId(), voteManager.randomPunishEffectId());
                    }
                }
                return;
            }

            //最大最小分数不一致，分别处理获胜者，受罚者

            //获取列表中最低分数相同的数据，并且分数是负数才需要受罚
            List<VoteTargetBo> minList = scoreList.stream().filter(voteTargetBo -> voteTargetBo.getScore() == min.get().getScore()).collect(Collectors.toList());
            //存在受罚者，保存数据
            for (VoteTargetBo voteTargetBo : minList) {
                voteDao.saveLosePlayerData(forMaster.getId(), voteTargetBo.getId());
                voteDao.savePunishEffect(forMaster.getId(), voteTargetBo.getId(), voteManager.randomPunishEffectId());
            }


            //存在获胜者，保存数据
            //获取列表中最高分数相同的数据
            List<VoteTargetBo> maxList = scoreList.stream().filter(voteTargetBo -> voteTargetBo.getScore() == max.get().getScore()).collect(Collectors.toList());

            //打乱顺序
//            Collections.shuffle(maxList);

            for (VoteTargetBo voteTargetBo : maxList) {
                voteDao.saveWinAnchorData(forMaster.getId(), voteTargetBo.getId());
                //在投票中获胜，个人投票的获胜者，或者是对应的阵营获胜
                voteEventManager.taskAnchorWinVote(forMaster, voteTargetBo.getId());
            }
            pushAnchorMvp(forMaster, maxList);
        }


        /**
         * 处理需要推送MVP的主播
         *
         * @param forMaster
         * @param maxList
         */
        private void pushAnchorMvp(VoteInfo forMaster, List<VoteTargetBo> maxList) {
            //处理需要推送MVP的用户
            List<VoteUserScoreBo> userScoreBoList = new ArrayList<>(8);
            for (VoteTargetBo voteTargetBo : maxList) {
                //统计每个主播下用户贡献最大的分数
                Set<Tuple> maxUserScoreSet = voteDao.getVoteAnchorUserScore(forMaster.getId(), voteTargetBo.getId());
                if (CollectionUtils.isEmpty(maxUserScoreSet)) {
                    continue;
                }
                for (Tuple tuple : maxUserScoreSet) {
                    VoteUserScoreBo userScoreBo = new VoteUserScoreBo();
                    userScoreBo.setUserId(Long.parseLong(tuple.getElement()));
                    userScoreBo.setBizId(voteTargetBo.getId());
                    userScoreBo.setType(voteTargetBo.getType());
                    userScoreBo.setScore((int) tuple.getScore());
                    userScoreBoList.add(userScoreBo);
                }
            }

            if (CollectionUtils.isEmpty(userScoreBoList)) {
                return;
            }

            //获取用户贡献值最大的数据
            Map<Long, List<Long>> userNjMap = getUserMaxScore(userScoreBoList);

            //如果数据不为空，需要处理MVP的用户
            if (CollectionUtils.isEmpty(userNjMap)) {
                return;
            }
            Set<Long> sendUserMvpSet = new HashSet<>();
            for (Map.Entry<Long, List<Long>> entry : userNjMap.entrySet()) {
                List<Long> njList = entry.getValue();
                Collections.shuffle(njList);
                Long userId = entry.getKey();
                //推送用户
                VotePushEventDto dto = new VotePushEventDto();
                dto.setVoteId(forMaster.getId());
                dto.setMvpUserId(userId);
                Long njId = njList.get(0);
                dto.setWinnerUserId(njId);
                dto.setVoteStatus(VoteStatus.PUNISH.getStatus());
                dto.setLiveId(forMaster.getLiveId());
                dto.setVoteType(forMaster.getVoteType());
                dto.setPushType(VotePushType.MVP.getType());
                Optional<PpUserBaseProto.User> user = ppNewUserManager.getUser(njId);
                if (!user.isPresent()) {
                    log.warn("push player max user not exist,userId:{},voteId:{},njList:{}", njId, forMaster.getId(), njList);
                    continue;
                }
                String votePlayerMvpContent = ppActivityConfig.getVotePlayerMvpContent();
                votePlayerMvpContent = votePlayerMvpContent.replace("#{name}", user.get().getName());
                dto.setContent(votePlayerMvpContent);
                //发送推送MVP  MQ消息
                if (!sendUserMvpSet.contains(userId)) {
                    sendUserMvpSet.add(userId);
                    voteEventManager.sendVotePushMvpMsg(dto);
                }
                //用户成为MVP 任务
                voteEventManager.taskUserMvp(forMaster, userId);
            }
        }

    }
}
